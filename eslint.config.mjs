import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

// 添加  rules: {
//   "@typescript-eslint/no-explicit-any": "error"
// }

const customRules = {
  rules: {
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "react/display-name": "off",
    "@typescript-eslint/no-unsafe-function-type": "off",
    "react-hooks/exhaustive-deps": "off",
    "@next/next/no-img-element": "off",
    "jsx-a11y/alt-text": "off",
    "@typescript-eslint/no-unused-expressions": "off",
    "@typescript-eslint/no-unsafe-member-access": "off",
    "react-hooks/rules-of-hooks": "off",
  },
};

const customConfig = {
  ...customRules,
};

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  customConfig,
];

export default eslintConfig;
