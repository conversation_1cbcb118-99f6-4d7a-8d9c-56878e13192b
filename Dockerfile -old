# =================== 构建阶段 ===================
FROM node:20-alpine AS builder
WORKDIR /app

# 启用 corepack 和 pnpm
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable && corepack prepare pnpm@latest --activate

# 复制依赖文件
COPY package.json pnpm-lock.yaml ./

# 安装生产依赖（挂载缓存加速）
RUN --mount=type=cache,target=/root/.pnpm-store pnpm install --prod --frozen-lockfile

# 复制项目源码
COPY . .

# 生成 Prisma client（挂载缓存）
RUN --mount=type=cache,target=/root/.cache pnpm exec prisma generate

# 构建 Next.js
ENV NODE_ENV=production
RUN pnpm build

# =================== 运行阶段 ===================
FROM node:20-alpine AS runner
WORKDIR /app

# 创建非 root 用户
RUN addgroup -S nextjs && adduser -S nextjs -G nextjs

# 环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 复制必要文件
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma

# 拷贝入口脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh
# 切换到非 root 用户
USER nextjs

EXPOSE 3000
ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["pnpm", "start"]
