# 简化的nginx配置 - 专门解决uploads 404问题
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    keepalive_timeout 65;

    # 上游服务器
    upstream nextjs_app {
        server app:3000;
    }

    # HTTPS 服务器
    server {
        listen 443 ssl http2;
        server_name ccxc.online;

        # SSL 配置
        ssl_certificate /etc/nginx/ssl/ccxc.online.pem;
        ssl_certificate_key /etc/nginx/ssl/ccxc.online.key;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers on;

        # 客户端最大请求体大小
        client_max_body_size 10M;

        # 上传文件服务 - 最高优先级
        location ^~ /uploads/ {
            alias /var/www/uploads/;
            
            # 调试头部
            add_header X-Served-By "nginx-static";
            add_header X-File-Path $request_filename;
            
            # 直接提供文件
            try_files $uri =404;
            
            # 缓存设置
            expires 1y;
            add_header Cache-Control "public, immutable";
            
            # 访问日志
            access_log /var/log/nginx/uploads.log main;
        }

        # API 路由
        location /api/ {
            proxy_pass http://nextjs_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Next.js 应用
        location @nextjs {
            proxy_pass http://nextjs_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 其他静态文件
        location ~* \.(js|css|ico|svg|woff|woff2|ttf|eot)$ {
            try_files $uri @nextjs;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 默认路由
        location / {
            try_files $uri $uri/ @nextjs;
        }
    }

    # HTTP 重定向到 HTTPS
    server {
        listen 80;
        server_name ccxc.online;
        return 301 https://$server_name$request_uri;
    }
}
