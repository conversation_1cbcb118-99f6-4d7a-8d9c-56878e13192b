{"name": "mp-react-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "pnpm db:deploy && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "docker:build": "docker build -t mp-react-nextjs .", "docker:run": "docker run -d --name mp-react-app -p 3000:3000 --env-file .env.local mp-react-nextjs", "docker:stop": "docker stop mp-react-app && docker rm mp-react-app", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:prod": "docker-compose up -d", "docker:down": "docker-compose down", "deploy:dev": "./scripts/deploy.sh development", "deploy:staging": "./scripts/deploy.sh staging", "deploy:prod": "./scripts/deploy.sh production", "check:deployment": "./scripts/check-deployment.sh", "k8s:apply": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete -f k8s/", "analyze": "ANALYZE=true npm run build", "deploy:vercel": "./scripts/deploy-vercel.sh", "deploy:vercel:prod": "./scripts/deploy-vercel.sh production", "vercel:env": "vercel env pull .env.local", "type-check": "tsc --noEmit", "db:deploy": "npx prisma migrate deploy && npx prisma generate"}, "dependencies": {"@ant-design/charts": "^2.6.2", "@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.1.0", "@prisma/client": "^6.13.0", "@types/lodash-es": "^4.17.12", "@uiw/react-md-editor": "^4.0.8", "antd": "^5.26.7", "antd-img-crop": "^4.25.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "dayjs": "^1.11.13", "ioredis": "^5.7.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "next": "15.4.5", "next-intl": "^4.3.4", "next-pwa": "^5.6.0", "qs": "^6.14.0", "quill": "^2.0.3", "react": "19.1.0", "react-dom": "19.1.0", "react-quill": "^2.0.0", "uuid": "^11.1.0", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/qs": "^6.14.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5.6", "prisma": "^6.13.0", "tailwindcss": "^4", "typescript": "^5"}, "prisma": {"schema": "./prisma/schema"}}