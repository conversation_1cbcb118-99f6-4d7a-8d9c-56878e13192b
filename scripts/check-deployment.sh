#!/bin/bash

# 部署状态检查脚本
# 用法: ./scripts/check-deployment.sh [environment] [url]
# 示例: ./scripts/check-deployment.sh production https://your-domain.com

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}
BASE_URL=${2:-http://localhost:3000}

log_info "检查部署状态..."
log_info "环境: $ENVIRONMENT"
log_info "URL: $BASE_URL"

# 检查工具依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，JSON 输出将不会格式化"
    fi
    
    log_success "依赖检查完成"
}

# 检查应用健康状态
check_health() {
    log_info "检查应用健康状态..."
    
    local health_url="$BASE_URL/api/health"
    local response
    local http_code
    
    # 发送健康检查请求
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$health_url" 2>/dev/null || echo "HTTPSTATUS:000")
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')
    
    case $http_code in
        200)
            log_success "应用健康状态: 正常"
            if command -v jq &> /dev/null; then
                echo "$body" | jq '.'
            else
                echo "$body"
            fi
            return 0
            ;;
        503)
            log_error "应用健康状态: 不健康"
            if command -v jq &> /dev/null; then
                echo "$body" | jq '.'
            else
                echo "$body"
            fi
            return 1
            ;;
        000)
            log_error "无法连接到应用"
            return 1
            ;;
        *)
            log_warning "应用健康状态: 未知 (HTTP $http_code)"
            echo "$body"
            return 1
            ;;
    esac
}

# 检查主页访问
check_homepage() {
    log_info "检查主页访问..."
    
    local response
    local http_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$BASE_URL" 2>/dev/null || echo "HTTPSTATUS:000")
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    
    case $http_code in
        200|302|301)
            log_success "主页访问: 正常 (HTTP $http_code)"
            return 0
            ;;
        000)
            log_error "主页访问: 无法连接"
            return 1
            ;;
        *)
            log_error "主页访问: 失败 (HTTP $http_code)"
            return 1
            ;;
    esac
}

# 检查 API 端点
check_api_endpoints() {
    log_info "检查 API 端点..."
    
    local endpoints=(
        "/api/health"
        "/api/auth/userinfo"
        "/api/users"
        "/api/roles"
    )
    
    local failed_count=0
    
    for endpoint in "${endpoints[@]}"; do
        local url="$BASE_URL$endpoint"
        local http_code
        
        http_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        
        case $http_code in
            200|401|403)
                log_success "API 端点 $endpoint: 可访问 (HTTP $http_code)"
                ;;
            000)
                log_error "API 端点 $endpoint: 无法连接"
                ((failed_count++))
                ;;
            *)
                log_warning "API 端点 $endpoint: 异常响应 (HTTP $http_code)"
                ;;
        esac
    done
    
    if [ $failed_count -eq 0 ]; then
        log_success "所有 API 端点检查完成"
        return 0
    else
        log_error "$failed_count 个 API 端点检查失败"
        return 1
    fi
}

# 检查静态资源
check_static_resources() {
    log_info "检查静态资源..."
    
    local resources=(
        "/_next/static/css"
        "/_next/static/js"
        "/favicon.ico"
    )
    
    local failed_count=0
    
    for resource in "${resources[@]}"; do
        local url="$BASE_URL$resource"
        local http_code
        
        # 对于目录，我们只检查是否返回合理的状态码
        http_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        
        case $http_code in
            200|301|302|403|404)
                log_success "静态资源 $resource: 可访问 (HTTP $http_code)"
                ;;
            000)
                log_error "静态资源 $resource: 无法连接"
                ((failed_count++))
                ;;
            *)
                log_warning "静态资源 $resource: 异常响应 (HTTP $http_code)"
                ;;
        esac
    done
    
    if [ $failed_count -eq 0 ]; then
        log_success "静态资源检查完成"
        return 0
    else
        log_error "$failed_count 个静态资源检查失败"
        return 1
    fi
}

# 检查安全头
check_security_headers() {
    log_info "检查安全头..."
    
    local headers
    headers=$(curl -s -I "$BASE_URL" 2>/dev/null || echo "")
    
    local security_headers=(
        "X-Frame-Options"
        "X-Content-Type-Options"
        "Referrer-Policy"
    )
    
    local missing_headers=()
    
    for header in "${security_headers[@]}"; do
        if echo "$headers" | grep -qi "$header"; then
            log_success "安全头 $header: 已设置"
        else
            log_warning "安全头 $header: 未设置"
            missing_headers+=("$header")
        fi
    done
    
    if [ ${#missing_headers[@]} -eq 0 ]; then
        log_success "安全头检查完成"
        return 0
    else
        log_warning "${#missing_headers[@]} 个安全头缺失"
        return 1
    fi
}

# 检查 HTTPS 配置
check_https() {
    if [[ $BASE_URL == https://* ]]; then
        log_info "检查 HTTPS 配置..."
        
        local ssl_info
        ssl_info=$(curl -s -I "$BASE_URL" 2>/dev/null | grep -i "strict-transport-security" || echo "")
        
        if [ -n "$ssl_info" ]; then
            log_success "HTTPS: HSTS 已启用"
        else
            log_warning "HTTPS: HSTS 未启用"
        fi
        
        # 检查 SSL 证书有效性
        local ssl_check
        ssl_check=$(curl -s --connect-timeout 10 "$BASE_URL" > /dev/null 2>&1 && echo "valid" || echo "invalid")
        
        if [ "$ssl_check" = "valid" ]; then
            log_success "HTTPS: SSL 证书有效"
        else
            log_error "HTTPS: SSL 证书无效或过期"
            return 1
        fi
    else
        log_info "跳过 HTTPS 检查 (使用 HTTP)"
    fi
    
    return 0
}

# 性能测试
check_performance() {
    log_info "检查性能指标..."
    
    local start_time
    local end_time
    local response_time
    
    start_time=$(date +%s%N)
    curl -s -o /dev/null "$BASE_URL" 2>/dev/null || true
    end_time=$(date +%s%N)
    
    response_time=$(( (end_time - start_time) / 1000000 ))
    
    if [ $response_time -lt 1000 ]; then
        log_success "响应时间: ${response_time}ms (优秀)"
    elif [ $response_time -lt 3000 ]; then
        log_success "响应时间: ${response_time}ms (良好)"
    elif [ $response_time -lt 5000 ]; then
        log_warning "响应时间: ${response_time}ms (一般)"
    else
        log_error "响应时间: ${response_time}ms (较慢)"
    fi
}

# Kubernetes 特定检查
check_kubernetes() {
    if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "staging" ]; then
        log_info "检查 Kubernetes 部署状态..."
        
        if command -v kubectl &> /dev/null; then
            # 检查 Pod 状态
            local pod_status
            pod_status=$(kubectl get pods -l app=mp-react-nextjs -o jsonpath='{.items[*].status.phase}' 2>/dev/null || echo "")
            
            if [[ $pod_status == *"Running"* ]]; then
                log_success "Kubernetes: Pod 运行正常"
            else
                log_error "Kubernetes: Pod 状态异常"
                kubectl get pods -l app=mp-react-nextjs 2>/dev/null || true
            fi
            
            # 检查服务状态
            local service_status
            service_status=$(kubectl get service mp-react-nextjs-service -o jsonpath='{.spec.clusterIP}' 2>/dev/null || echo "")
            
            if [ -n "$service_status" ]; then
                log_success "Kubernetes: Service 配置正常"
            else
                log_error "Kubernetes: Service 配置异常"
            fi
        else
            log_warning "kubectl 未安装，跳过 Kubernetes 检查"
        fi
    fi
}

# 生成检查报告
generate_report() {
    log_info "生成检查报告..."
    
    local report_file="deployment-check-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "部署状态检查报告"
        echo "=================="
        echo "检查时间: $(date)"
        echo "环境: $ENVIRONMENT"
        echo "URL: $BASE_URL"
        echo ""
        echo "检查结果:"
        echo "--------"
    } > "$report_file"
    
    log_success "检查报告已生成: $report_file"
}

# 主函数
main() {
    local exit_code=0
    
    echo "🚀 部署状态检查开始"
    echo "===================="
    
    # 检查依赖
    check_dependencies
    
    # 执行各项检查
    check_health || exit_code=1
    echo ""
    
    check_homepage || exit_code=1
    echo ""
    
    check_api_endpoints || exit_code=1
    echo ""
    
    check_static_resources || exit_code=1
    echo ""
    
    check_security_headers || exit_code=1
    echo ""
    
    check_https || exit_code=1
    echo ""
    
    check_performance
    echo ""
    
    check_kubernetes
    echo ""
    
    # 生成报告
    generate_report
    
    # 总结
    echo "===================="
    if [ $exit_code -eq 0 ]; then
        log_success "✅ 所有检查通过！部署状态良好。"
    else
        log_error "❌ 部分检查失败，请查看上述详细信息。"
    fi
    
    exit $exit_code
}

# 执行主函数
main "$@"
