#!/bin/bash

# 切换nginx配置的脚本

echo "🔄 切换到简化的nginx配置..."

# 1. 备份当前配置
echo "1. 备份当前配置..."
cp nginx/nginx.conf nginx/nginx.conf.backup

# 2. 使用简化配置
echo "2. 使用简化配置..."
cp nginx/nginx-simple.conf nginx/nginx.conf

# 3. 测试新配置
echo "3. 测试新配置..."
docker-compose exec nginx nginx -t

if [ $? -ne 0 ]; then
    echo "❌ 新配置有错误，恢复原配置..."
    cp nginx/nginx.conf.backup nginx/nginx.conf
    exit 1
fi

# 4. 重新加载配置
echo "4. 重新加载nginx配置..."
docker-compose exec nginx nginx -s reload

# 5. 创建测试文件
echo "5. 创建测试文件..."
docker-compose exec app mkdir -p /app/uploads/test
docker-compose exec app sh -c 'echo "Simple config test - $(date)" > /app/uploads/test/simple-test.txt'

# 6. 等待配置生效
echo "6. 等待配置生效..."
sleep 5

# 7. 测试访问
echo "7. 测试文件访问..."
echo "测试新文件："
curl -I https://ccxc.online/uploads/test/simple-test.txt

echo ""
echo "测试原始文件："
curl -I https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png

echo ""
echo "✅ 配置切换完成！"
echo ""
echo "如果问题仍然存在，可能的原因："
echo "1. 文件真的不存在于 /var/www/uploads/ 目录"
echo "2. Docker volume挂载有问题"
echo "3. 文件权限问题"
echo ""
echo "恢复原配置的命令："
echo "cp nginx/nginx.conf.backup nginx/nginx.conf"
echo "docker-compose exec nginx nginx -s reload"
