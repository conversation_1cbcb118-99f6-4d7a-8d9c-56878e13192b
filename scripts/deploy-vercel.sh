#!/bin/bash

# Vercel 部署脚本
# 用法: ./scripts/deploy-vercel.sh [environment]
# 示例: ./scripts/deploy-vercel.sh production

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-preview}

log_info "开始部署到 Vercel，环境: $ENVIRONMENT"

# 检查 Vercel CLI
check_vercel_cli() {
    log_info "检查 Vercel CLI..."
    
    if ! command -v vercel &> /dev/null; then
        log_error "Vercel CLI 未安装"
        log_info "请运行: npm install -g vercel"
        exit 1
    fi
    
    log_success "Vercel CLI 检查通过"
}

# 检查登录状态
check_auth() {
    log_info "检查 Vercel 登录状态..."
    
    if ! vercel whoami &> /dev/null; then
        log_warning "未登录 Vercel，开始登录..."
        vercel login
    fi
    
    log_success "Vercel 认证通过"
}

# 检查项目配置
check_project() {
    log_info "检查项目配置..."
    
    # 检查是否已链接项目
    if [ ! -f ".vercel/project.json" ]; then
        log_warning "项目未链接，开始链接..."
        vercel link
    fi
    
    log_success "项目配置检查通过"
}

# 设置环境变量
setup_env_vars() {
    log_info "设置环境变量..."
    
    if [ -f ".env.vercel" ]; then
        log_info "从 .env.vercel 读取环境变量..."
        
        # 读取环境变量文件并设置到 Vercel
        while IFS='=' read -r key value; do
            # 跳过注释和空行
            if [[ $key =~ ^[[:space:]]*# ]] || [[ -z $key ]]; then
                continue
            fi
            
            # 移除可能的引号
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
            
            if [ "$ENVIRONMENT" = "production" ]; then
                vercel env add "$key" production <<< "$value" 2>/dev/null || true
            else
                vercel env add "$key" preview <<< "$value" 2>/dev/null || true
            fi
        done < .env.vercel
        
        log_success "环境变量设置完成"
    else
        log_warning ".env.vercel 文件不存在，跳过环境变量设置"
    fi
}

# 运行构建前检查
pre_build_check() {
    log_info "运行构建前检查..."
    
    # 检查 package.json
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件不存在"
        exit 1
    fi
    
    # 检查 Next.js 配置
    if [ ! -f "next.config.js" ] && [ ! -f "next.config.ts" ]; then
        log_error "Next.js 配置文件不存在"
        exit 1
    fi
    
    # 运行类型检查
    log_info "运行 TypeScript 类型检查..."
    npm run type-check || {
        log_error "TypeScript 类型检查失败"
        exit 1
    }
    
    # 运行 ESLint
    log_info "运行 ESLint 检查..."
    npm run lint || {
        log_error "ESLint 检查失败"
        exit 1
    }
    
    log_success "构建前检查通过"
}

# 部署到 Vercel
deploy_to_vercel() {
    log_info "开始部署到 Vercel..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "部署到生产环境..."
        vercel --prod
    else
        log_info "部署到预览环境..."
        vercel
    fi
    
    log_success "部署完成"
}

# 获取部署信息
get_deployment_info() {
    log_info "获取部署信息..."
    
    # 获取最新部署的 URL
    DEPLOYMENT_URL=$(vercel ls --meta | head -n 1 | awk '{print $2}')
    
    if [ -n "$DEPLOYMENT_URL" ]; then
        log_success "部署 URL: https://$DEPLOYMENT_URL"
        
        # 如果是生产环境，也显示自定义域名
        if [ "$ENVIRONMENT" = "production" ]; then
            CUSTOM_DOMAIN=$(grep -o '"alias":\s*\["[^"]*"' vercel.json 2>/dev/null | cut -d'"' -f4)
            if [ -n "$CUSTOM_DOMAIN" ]; then
                log_success "自定义域名: https://$CUSTOM_DOMAIN"
            fi
        fi
    fi
}

# 运行部署后测试
post_deploy_test() {
    log_info "运行部署后测试..."
    
    # 等待部署完成
    sleep 10
    
    # 获取测试 URL
    TEST_URL="https://$DEPLOYMENT_URL"
    if [ "$ENVIRONMENT" = "production" ]; then
        CUSTOM_DOMAIN=$(grep -o '"alias":\s*\["[^"]*"' vercel.json 2>/dev/null | cut -d'"' -f4)
        if [ -n "$CUSTOM_DOMAIN" ]; then
            TEST_URL="https://$CUSTOM_DOMAIN"
        fi
    fi
    
    # 测试健康检查
    log_info "测试健康检查接口..."
    if curl -f "$TEST_URL/api/health" > /dev/null 2>&1; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        return 1
    fi
    
    # 测试主页
    log_info "测试主页访问..."
    if curl -f "$TEST_URL" > /dev/null 2>&1; then
        log_success "主页访问正常"
    else
        log_error "主页访问失败"
        return 1
    fi
    
    log_success "部署后测试通过"
}

# 显示部署总结
show_summary() {
    log_success "🎉 部署成功完成！"
    echo ""
    echo "📊 部署总结:"
    echo "  环境: $ENVIRONMENT"
    echo "  部署 URL: https://$DEPLOYMENT_URL"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        CUSTOM_DOMAIN=$(grep -o '"alias":\s*\["[^"]*"' vercel.json 2>/dev/null | cut -d'"' -f4)
        if [ -n "$CUSTOM_DOMAIN" ]; then
            echo "  自定义域名: https://$CUSTOM_DOMAIN"
        fi
    fi
    
    echo ""
    echo "🔗 有用的链接:"
    echo "  Vercel Dashboard: https://vercel.com/dashboard"
    echo "  部署日志: vercel logs"
    echo "  环境变量: vercel env ls"
    echo ""
    echo "📝 后续步骤:"
    echo "  1. 在 Vercel Dashboard 中配置自定义域名"
    echo "  2. 设置数据库和 Redis 连接"
    echo "  3. 配置监控和错误追踪"
    echo "  4. 设置 CI/CD 自动部署"
}

# 主函数
main() {
    # 检查依赖
    check_vercel_cli
    check_auth
    check_project
    
    # 设置环境变量
    setup_env_vars
    
    # 构建前检查
    pre_build_check
    
    # 部署
    deploy_to_vercel
    
    # 获取部署信息
    get_deployment_info
    
    # 部署后测试
    if ! post_deploy_test; then
        log_error "部署后测试失败，请检查应用状态"
        exit 1
    fi
    
    # 显示总结
    show_summary
}

# 错误处理
trap 'log_error "部署过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
