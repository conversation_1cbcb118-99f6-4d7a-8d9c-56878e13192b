#!/bin/bash

# 修复docker volume挂载问题

echo "🔧 修复docker volume挂载问题..."

# 1. 停止服务
echo "1. 停止所有服务..."
docker-compose down

# 2. 清理旧的volume（谨慎操作）
echo "2. 检查现有volumes..."
docker volume ls | grep uploads

# 3. 重新启动服务
echo "3. 重新启动服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 20

# 4. 检查容器状态
echo "4. 检查容器状态："
docker-compose ps

# 5. 检查volume挂载
echo ""
echo "5. 检查volume挂载情况："
echo "App容器的uploads目录："
docker-compose exec app ls -la /app/uploads/ 2>/dev/null || echo "❌ app容器uploads目录不存在"

echo ""
echo "Nginx容器的uploads目录："
docker-compose exec nginx ls -la /var/www/uploads/ 2>/dev/null || echo "❌ nginx容器uploads目录不存在"

# 6. 创建目录结构
echo ""
echo "6. 创建必要的目录结构..."
docker-compose exec app mkdir -p /app/uploads/images/2025-09
docker-compose exec app mkdir -p /app/uploads/test

# 7. 创建测试文件
echo ""
echo "7. 创建测试文件..."
docker-compose exec app sh -c 'echo "Volume mount test - $(date)" > /app/uploads/test/volume-test.txt'
docker-compose exec app sh -c 'echo "Original file recreation - $(date)" > /app/uploads/images/2025-09/1756978061895_sftyw1.png'

# 8. 检查文件是否在nginx容器中可见
echo ""
echo "8. 检查文件同步情况..."
sleep 3

echo "测试文件在nginx容器中："
docker-compose exec nginx ls -la /var/www/uploads/test/volume-test.txt 2>/dev/null && echo "✅ 测试文件同步成功" || echo "❌ 测试文件同步失败"

echo ""
echo "原始文件在nginx容器中："
docker-compose exec nginx ls -la /var/www/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null && echo "✅ 原始文件同步成功" || echo "❌ 原始文件同步失败"

# 9. 修复权限
echo ""
echo "9. 修复文件权限..."
docker-compose exec app chown -R 1000:1000 /app/uploads
docker-compose exec nginx chown -R nginx:nginx /var/www/uploads 2>/dev/null || echo "nginx权限修复需要root权限"

# 10. 测试nginx配置并重载
echo ""
echo "10. 测试nginx配置..."
docker-compose exec nginx nginx -t && docker-compose exec nginx nginx -s reload

# 11. 测试文件访问
echo ""
echo "11. 测试文件访问..."
echo "测试新创建的文件："
curl -I https://ccxc.online/uploads/test/volume-test.txt

echo ""
echo "测试原始文件："
curl -I https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png

# 12. 显示volume信息
echo ""
echo "12. Docker volume详细信息："
docker volume ls | grep uploads
VOLUME_NAME=$(docker-compose config --volumes | grep uploads | head -1)
if [ ! -z "$VOLUME_NAME" ]; then
    echo "Volume详情："
    docker volume inspect $VOLUME_NAME
fi

echo ""
echo "✅ Volume挂载修复完成！"
echo ""
echo "如果问题仍然存在，可能需要："
echo "1. 完全删除volume并重新创建"
echo "2. 检查docker-compose.yml中的volume配置"
echo "3. 确认SSL证书路径正确"
