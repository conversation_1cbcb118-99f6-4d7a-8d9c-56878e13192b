#!/bin/bash

# 部署脚本
# 用法: ./scripts/deploy.sh [environment] [version]
# 示例: ./scripts/deploy.sh production v1.0.0

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}

log_info "开始部署到 $ENVIRONMENT 环境，版本: $VERSION"

# 验证环境参数
case $ENVIRONMENT in
    development|staging|production)
        log_info "环境验证通过: $ENVIRONMENT"
        ;;
    *)
        log_error "无效的环境参数: $ENVIRONMENT"
        log_error "支持的环境: development, staging, production"
        exit 1
        ;;
esac

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_warning "Helm 未安装，将跳过 Helm 相关操作"
    fi
    
    log_success "依赖检查完成"
}

# 构建 Docker 镜像
build_image() {
    log_info "构建 Docker 镜像..."
    
    IMAGE_NAME="ghcr.io/your-org/mp-react-nextjs"
    IMAGE_TAG="$VERSION"
    FULL_IMAGE="$IMAGE_NAME:$IMAGE_TAG"
    
    # 构建镜像
    docker build -t "$FULL_IMAGE" .
    
    # 推送镜像
    if [ "$ENVIRONMENT" != "development" ]; then
        log_info "推送镜像到注册表..."
        docker push "$FULL_IMAGE"
        log_success "镜像推送完成: $FULL_IMAGE"
    fi
}

# 部署到 Kubernetes
deploy_k8s() {
    log_info "部署到 Kubernetes..."
    
    # 设置命名空间
    NAMESPACE="default"
    if [ "$ENVIRONMENT" != "production" ]; then
        NAMESPACE="$ENVIRONMENT"
    fi
    
    # 创建命名空间（如果不存在）
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # 更新镜像标签
    sed -i.bak "s|ghcr.io/your-org/mp-react-nextjs:.*|ghcr.io/your-org/mp-react-nextjs:$VERSION|g" k8s/deployment.yaml
    
    # 应用配置
    kubectl apply -f k8s/configmap.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/deployment.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/ingress.yaml -n "$NAMESPACE"
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl rollout status deployment/mp-react-nextjs-app -n "$NAMESPACE" --timeout=300s
    
    log_success "Kubernetes 部署完成"
}

# 使用 Docker Compose 部署（开发环境）
deploy_docker_compose() {
    log_info "使用 Docker Compose 部署..."
    
    # 设置环境变量
    export IMAGE_TAG="$VERSION"
    export ENVIRONMENT="$ENVIRONMENT"
    
    # 选择 compose 文件
    COMPOSE_FILE="docker-compose.yml"
    if [ "$ENVIRONMENT" = "development" ]; then
        COMPOSE_FILE="docker-compose.dev.yml"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        COMPOSE_FILE="docker-compose.staging.yml"
    fi
    
    # 停止现有服务
    docker-compose -f "$COMPOSE_FILE" down
    
    # 启动服务
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_success "Docker Compose 部署完成"
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 确定健康检查 URL
    HEALTH_URL="http://localhost:3000/api/health"
    if [ "$ENVIRONMENT" = "production" ]; then
        HEALTH_URL="https://your-domain.com/api/health"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        HEALTH_URL="https://staging.your-domain.com/api/health"
    elif [ "$ENVIRONMENT" = "development" ]; then
        HEALTH_URL="https://dev.your-domain.com/api/health"
    fi
    
    # 等待服务启动
    sleep 30
    
    # 检查健康状态
    for i in {1..10}; do
        if curl -f "$HEALTH_URL" > /dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        fi
        log_warning "健康检查失败，重试 $i/10..."
        sleep 10
    done
    
    log_error "健康检查失败"
    return 1
}

# 运行冒烟测试
smoke_test() {
    log_info "运行冒烟测试..."
    
    # 基本页面访问测试
    BASE_URL="http://localhost:3000"
    if [ "$ENVIRONMENT" = "production" ]; then
        BASE_URL="https://your-domain.com"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        BASE_URL="https://staging.your-domain.com"
    elif [ "$ENVIRONMENT" = "development" ]; then
        BASE_URL="https://dev.your-domain.com"
    fi
    
    # 测试主页
    if curl -f "$BASE_URL" > /dev/null 2>&1; then
        log_success "主页访问正常"
    else
        log_error "主页访问失败"
        return 1
    fi
    
    # 测试 API
    if curl -f "$BASE_URL/api/health" > /dev/null 2>&1; then
        log_success "API 访问正常"
    else
        log_error "API 访问失败"
        return 1
    fi
    
    log_success "冒烟测试通过"
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    if [ "$DEPLOYMENT_TYPE" = "k8s" ]; then
        kubectl rollout undo deployment/mp-react-nextjs-app -n "$NAMESPACE"
        kubectl rollout status deployment/mp-react-nextjs-app -n "$NAMESPACE"
    else
        # Docker Compose 回滚需要手动指定之前的版本
        log_error "Docker Compose 回滚需要手动操作"
    fi
    
    log_success "回滚完成"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 恢复修改的文件
    if [ -f "k8s/deployment.yaml.bak" ]; then
        mv k8s/deployment.yaml.bak k8s/deployment.yaml
    fi
    
    log_success "清理完成"
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup EXIT
    trap 'log_error "部署失败，开始回滚..."; rollback; exit 1' ERR
    
    # 检查依赖
    check_dependencies
    
    # 确定部署类型
    DEPLOYMENT_TYPE="docker-compose"
    if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "staging" ]; then
        DEPLOYMENT_TYPE="k8s"
    fi
    
    log_info "部署类型: $DEPLOYMENT_TYPE"
    
    # 构建镜像
    build_image
    
    # 部署
    if [ "$DEPLOYMENT_TYPE" = "k8s" ]; then
        deploy_k8s
    else
        deploy_docker_compose
    fi
    
    # 健康检查
    if ! health_check; then
        log_error "健康检查失败，开始回滚..."
        rollback
        exit 1
    fi
    
    # 冒烟测试
    if ! smoke_test; then
        log_error "冒烟测试失败，开始回滚..."
        rollback
        exit 1
    fi
    
    log_success "部署成功完成！"
    log_info "环境: $ENVIRONMENT"
    log_info "版本: $VERSION"
    log_info "部署类型: $DEPLOYMENT_TYPE"
}

# 执行主函数
main "$@"
