#!/usr/bin/env node

/**
 * 测试报告生成器
 * 生成综合的测试报告，包括单元测试、集成测试和E2E测试的结果
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 读取Jest覆盖率报告
function readJestCoverage() {
  const coveragePath = path.join(__dirname, '../coverage/coverage-summary.json');
  
  if (!fs.existsSync(coveragePath)) {
    return null;
  }
  
  try {
    const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
    return coverage.total;
  } catch (error) {
    console.error('Error reading Jest coverage:', error);
    return null;
  }
}

// 读取Playwright测试结果
function readPlaywrightResults() {
  const resultsPath = path.join(__dirname, '../test-results/results.json');
  
  if (!fs.existsSync(resultsPath)) {
    return null;
  }
  
  try {
    const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
    return results;
  } catch (error) {
    console.error('Error reading Playwright results:', error);
    return null;
  }
}

// 生成覆盖率徽章
function getCoverageBadge(percentage) {
  if (percentage >= 90) return colorize('🟢 优秀', 'green');
  if (percentage >= 80) return colorize('🟡 良好', 'yellow');
  if (percentage >= 70) return colorize('🟠 一般', 'yellow');
  return colorize('🔴 需要改进', 'red');
}

// 生成测试状态徽章
function getTestStatusBadge(passed, total) {
  const percentage = (passed / total) * 100;
  if (percentage === 100) return colorize('✅ 全部通过', 'green');
  if (percentage >= 90) return colorize('🟡 大部分通过', 'yellow');
  return colorize('❌ 有失败', 'red');
}

// 生成报告
function generateReport() {
  console.log(colorize('\n📊 测试报告', 'cyan'));
  console.log(colorize('='.repeat(50), 'cyan'));
  
  // Jest单元测试报告
  console.log(colorize('\n🧪 单元测试 (Jest)', 'blue'));
  console.log(colorize('-'.repeat(30), 'blue'));
  
  const jestCoverage = readJestCoverage();
  if (jestCoverage) {
    console.log(`📈 代码覆盖率:`);
    console.log(`   语句覆盖率: ${jestCoverage.statements.pct}% ${getCoverageBadge(jestCoverage.statements.pct)}`);
    console.log(`   分支覆盖率: ${jestCoverage.branches.pct}% ${getCoverageBadge(jestCoverage.branches.pct)}`);
    console.log(`   函数覆盖率: ${jestCoverage.functions.pct}% ${getCoverageBadge(jestCoverage.functions.pct)}`);
    console.log(`   行覆盖率:   ${jestCoverage.lines.pct}% ${getCoverageBadge(jestCoverage.lines.pct)}`);
  } else {
    console.log(colorize('❌ 未找到Jest覆盖率报告', 'red'));
    console.log('   请运行: npm run test:coverage');
  }
  
  // Playwright E2E测试报告
  console.log(colorize('\n🎭 端到端测试 (Playwright)', 'magenta'));
  console.log(colorize('-'.repeat(30), 'magenta'));
  
  const playwrightResults = readPlaywrightResults();
  if (playwrightResults) {
    const { stats } = playwrightResults;
    const total = stats.expected + stats.unexpected + stats.flaky;
    const passed = stats.expected;
    
    console.log(`📊 测试结果:`);
    console.log(`   总测试数: ${total}`);
    console.log(`   通过数:   ${passed} ${getTestStatusBadge(passed, total)}`);
    console.log(`   失败数:   ${stats.unexpected}`);
    console.log(`   不稳定:   ${stats.flaky}`);
    console.log(`   跳过数:   ${stats.skipped}`);
    
    if (stats.unexpected > 0) {
      console.log(colorize('\n❌ 失败的测试:', 'red'));
      // 这里可以添加失败测试的详细信息
    }
  } else {
    console.log(colorize('❌ 未找到Playwright测试结果', 'red'));
    console.log('   请运行: npm run test:e2e');
  }
  
  // 测试文件统计
  console.log(colorize('\n📁 测试文件统计', 'yellow'));
  console.log(colorize('-'.repeat(30), 'yellow'));
  
  const testFiles = {
    unit: findTestFiles('src/**/*.test.{ts,tsx}'),
    integration: findTestFiles('__tests__/**/*.test.{ts,tsx}'),
    e2e: findTestFiles('e2e/**/*.spec.ts'),
  };
  
  console.log(`🧪 单元测试文件: ${testFiles.unit.length}`);
  console.log(`🔗 集成测试文件: ${testFiles.integration.length}`);
  console.log(`🎭 E2E测试文件:   ${testFiles.e2e.length}`);
  console.log(`📊 总测试文件:   ${testFiles.unit.length + testFiles.integration.length + testFiles.e2e.length}`);
  
  // 建议和下一步
  console.log(colorize('\n💡 建议', 'green'));
  console.log(colorize('-'.repeat(30), 'green'));
  
  if (jestCoverage) {
    const avgCoverage = (
      jestCoverage.statements.pct +
      jestCoverage.branches.pct +
      jestCoverage.functions.pct +
      jestCoverage.lines.pct
    ) / 4;
    
    if (avgCoverage < 80) {
      console.log('📈 建议提高代码覆盖率到80%以上');
      console.log('   - 为核心业务逻辑添加更多测试');
      console.log('   - 测试边界条件和错误情况');
    }
  }
  
  if (testFiles.e2e.length < 5) {
    console.log('🎭 建议添加更多E2E测试');
    console.log('   - 测试关键用户流程');
    console.log('   - 测试不同设备和浏览器');
  }
  
  console.log(colorize('\n🚀 快速命令', 'cyan'));
  console.log(colorize('-'.repeat(30), 'cyan'));
  console.log('npm run test:watch     # 监听模式运行单元测试');
  console.log('npm run test:coverage  # 生成覆盖率报告');
  console.log('npm run test:e2e:ui    # 可视化运行E2E测试');
  console.log('npm run test:all       # 运行所有测试');
  
  console.log(colorize('\n✨ 报告生成完成!', 'green'));
}

// 查找测试文件
function findTestFiles(pattern) {
  const glob = require('glob');
  try {
    return glob.sync(pattern, { cwd: path.join(__dirname, '..') });
  } catch (error) {
    return [];
  }
}

// 主函数
function main() {
  try {
    generateReport();
  } catch (error) {
    console.error(colorize('❌ 生成报告时出错:', 'red'), error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateReport,
  readJestCoverage,
  readPlaywrightResults,
};
