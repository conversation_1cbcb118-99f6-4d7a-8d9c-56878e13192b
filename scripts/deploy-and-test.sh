#!/bin/bash

# 部署和测试脚本

set -e  # 遇到错误立即退出

echo "🚀 开始部署和测试..."

# 1. 停止现有服务
echo "1. 停止现有服务..."
docker-compose down

# 2. 清理旧的镜像和容器
echo "2. 清理旧资源..."
docker system prune -f

# 3. 构建和启动服务
echo "3. 构建和启动服务..."
docker-compose up -d --build

# 4. 等待服务启动
echo "4. 等待服务启动..."
sleep 30

# 5. 检查服务状态
echo "5. 检查服务状态..."
docker-compose ps

# 6. 检查服务健康状态
echo "6. 检查服务健康状态..."
echo "检查数据库连接..."
docker-compose exec -T db pg_isready -U root -d ccxc || echo "❌ 数据库未就绪"

echo "检查Redis连接..."
docker-compose exec -T redis redis-cli ping || echo "❌ Redis未就绪"

echo "检查应用健康状态..."
curl -f http://localhost:3000/health || echo "❌ 应用未就绪"

# 7. 创建uploads目录结构
echo "7. 创建uploads目录结构..."
docker-compose exec -T app mkdir -p /app/uploads/images
docker-compose exec -T app mkdir -p /app/uploads/documents
docker-compose exec -T app mkdir -p /app/uploads/temp

# 8. 创建测试文件
echo "8. 创建测试文件..."
docker-compose exec -T app sh -c 'echo "Test image content - $(date)" > /app/uploads/test.txt'
docker-compose exec -T app sh -c 'echo "Test image in images folder - $(date)" > /app/uploads/images/test-image.txt'

# 9. 检查文件权限
echo "9. 检查文件权限..."
docker-compose exec -T app ls -la /app/uploads/
docker-compose exec -T nginx ls -la /var/www/uploads/ || echo "❌ nginx无法访问uploads目录"

# 10. 测试nginx配置
echo "10. 测试nginx配置..."
docker-compose exec -T nginx nginx -t

# 11. 重载nginx配置
echo "11. 重载nginx配置..."
docker-compose exec -T nginx nginx -s reload

# 12. 测试文件访问
echo "12. 测试文件访问..."
echo "测试直接文件访问..."
curl -I http://localhost/uploads/test.txt || echo "❌ 无法访问测试文件"

echo "测试图片目录文件访问..."
curl -I http://localhost/uploads/images/test-image.txt || echo "❌ 无法访问图片目录文件"

# 13. 检查日志
echo "13. 检查最近的错误日志..."
echo "Nginx错误日志："
docker-compose logs --tail=10 nginx

echo "应用日志："
docker-compose logs --tail=10 app

# 14. 显示测试结果
echo ""
echo "✅ 部署完成！"
echo ""
echo "🧪 手动测试步骤："
echo "1. 访问应用: http://localhost:3000"
echo "2. 测试文件上传功能"
echo "3. 检查上传的文件是否可以正常访问"
echo ""
echo "📁 测试文件URL："
echo "- http://localhost/uploads/test.txt"
echo "- http://localhost/uploads/images/test-image.txt"
echo ""
echo "🔧 如果仍有问题，运行诊断脚本："
echo "bash scripts/fix-image-404.sh"
