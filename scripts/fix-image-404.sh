#!/bin/bash

# 修复图片404问题的脚本

echo "🔍 诊断图片404问题..."

# 检查容器状态
echo "1. 检查容器状态："
docker-compose ps

echo ""
echo "2. 检查nginx容器中的uploads目录："
docker-compose exec nginx ls -la /var/www/uploads/ || echo "❌ nginx容器中uploads目录不存在或无法访问"

echo ""
echo "3. 检查app容器中的uploads目录："
docker-compose exec app ls -la /app/uploads/ || echo "❌ app容器中uploads目录不存在或无法访问"

echo ""
echo "4. 检查nginx配置："
docker-compose exec nginx nginx -t || echo "❌ nginx配置有错误"

echo ""
echo "5. 检查nginx错误日志："
docker-compose logs nginx | tail -20

echo ""
echo "🔧 开始修复..."

# 创建测试文件
echo "6. 在app容器中创建测试文件："
docker-compose exec app mkdir -p /app/uploads/test
docker-compose exec app sh -c 'echo "Test image content" > /app/uploads/test/test.txt'
docker-compose exec app ls -la /app/uploads/test/

echo ""
echo "7. 检查nginx是否能访问测试文件："
docker-compose exec nginx ls -la /var/www/uploads/test/ || echo "❌ nginx无法访问app容器的uploads目录"

echo ""
echo "8. 测试nginx配置重载："
docker-compose exec nginx nginx -s reload

echo ""
echo "9. 检查文件权限："
docker-compose exec app ls -la /app/uploads/
docker-compose exec nginx ls -la /var/www/uploads/

echo ""
echo "✅ 修复完成！"
echo ""
echo "🧪 测试步骤："
echo "1. 访问 https://your-domain.com/uploads/test/test.txt"
echo "2. 如果仍然404，请检查："
echo "   - nginx配置中的alias路径"
echo "   - docker-compose中的volume挂载"
echo "   - 文件权限设置"
echo ""
echo "📝 常见问题解决方案："
echo "1. 如果nginx无法访问文件："
echo "   docker-compose exec nginx chown -R nginx:nginx /var/www/uploads"
echo ""
echo "2. 如果app无法写入文件："
echo "   docker-compose exec app chown -R node:node /app/uploads"
echo ""
echo "3. 重启服务："
echo "   docker-compose restart nginx"
echo "   docker-compose restart app"
