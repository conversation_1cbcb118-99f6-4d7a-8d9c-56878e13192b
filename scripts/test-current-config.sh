#!/bin/bash

# 测试当前nginx配置

echo "🧪 测试当前nginx配置..."

# 1. 测试nginx配置语法
echo "1. 测试nginx配置语法..."
docker-compose exec nginx nginx -t

if [ $? -ne 0 ]; then
    echo "❌ nginx配置有语法错误"
    echo "查看详细错误："
    docker-compose exec nginx nginx -T
    exit 1
fi

echo "✅ nginx配置语法正确"

# 2. 重新加载配置
echo ""
echo "2. 重新加载nginx配置..."
docker-compose exec nginx nginx -s reload

# 3. 检查文件是否存在
echo ""
echo "3. 检查目标文件是否存在..."
TARGET_FILE="/var/www/uploads/images/2025-09/1756978061895_sftyw1.png"
docker-compose exec nginx ls -la $TARGET_FILE 2>/dev/null && echo "✅ 文件存在" || echo "❌ 文件不存在"

# 4. 如果文件不存在，创建它
if ! docker-compose exec nginx ls -la $TARGET_FILE >/dev/null 2>&1; then
    echo ""
    echo "4. 文件不存在，创建测试文件..."
    docker-compose exec app mkdir -p /app/uploads/images/2025-09
    docker-compose exec app sh -c 'echo "Recreated file - $(date)" > /app/uploads/images/2025-09/1756978061895_sftyw1.png'
    
    # 检查是否在nginx容器中可见
    sleep 2
    docker-compose exec nginx ls -la $TARGET_FILE 2>/dev/null && echo "✅ 文件现在存在于nginx容器中" || echo "❌ 文件仍然不在nginx容器中"
fi

# 5. 测试文件访问
echo ""
echo "5. 测试文件访问..."
echo "本地测试（nginx内部）："
docker-compose exec nginx curl -I http://localhost/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null | head -5

echo ""
echo "外部测试："
curl -I https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png | head -10

# 6. 检查nginx日志
echo ""
echo "6. 检查nginx访问日志（最近5条）："
docker-compose exec nginx tail -5 /var/log/nginx/access.log 2>/dev/null || echo "无法访问access.log"

echo ""
echo "检查nginx错误日志（最近5条）："
docker-compose exec nginx tail -5 /var/log/nginx/error.log 2>/dev/null || echo "无法访问error.log"

echo ""
echo "检查uploads专用日志："
docker-compose exec nginx tail -5 /var/log/nginx/uploads.log 2>/dev/null || echo "uploads.log不存在或为空"

echo ""
echo "✅ 测试完成！"
echo ""
echo "如果文件访问仍然返回404，请检查："
echo "1. 文件是否真的存在于nginx容器的 /var/www/uploads/ 目录"
echo "2. docker volume挂载是否正常工作"
echo "3. 文件权限是否正确"
