#!/bin/bash

# 创建uploads目录结构脚本

echo "设置uploads目录..."

# 创建uploads目录
mkdir -p uploads
mkdir -p uploads/images
mkdir -p uploads/documents
mkdir -p uploads/temp

# 设置权限
chmod -R 755 uploads

# 创建测试图片（如果不存在）
if [ ! -f "uploads/test.jpg" ]; then
    echo "创建测试图片..."
    # 创建一个简单的测试文件
    echo "Test image file" > uploads/test.txt
fi

echo "uploads目录设置完成！"
echo "目录结构："
ls -la uploads/

echo ""
echo "请确保以下配置正确："
echo "1. docker-compose.yml 中的 volumes 映射"
echo "2. nginx.conf 中的 alias 路径"
echo "3. Next.js 应用中的文件上传路径"
