#!/bin/bash

# 重新加载nginx配置的脚本

echo "🔄 重新加载nginx配置..."

# 1. 测试nginx配置
echo "1. 测试nginx配置..."
docker-compose exec nginx nginx -t

if [ $? -ne 0 ]; then
    echo "❌ nginx配置有错误，请检查配置文件"
    exit 1
fi

# 2. 重新加载nginx配置
echo "2. 重新加载nginx配置..."
docker-compose exec nginx nginx -s reload

# 3. 检查nginx状态
echo "3. 检查nginx状态..."
docker-compose exec nginx ps aux | grep nginx

# 4. 创建测试文件
echo "4. 创建测试文件..."
docker-compose exec app mkdir -p /app/uploads/test
docker-compose exec app sh -c 'echo "Nginx reload test - $(date)" > /app/uploads/test/reload-test.txt'

# 5. 测试文件访问
echo "5. 测试文件访问..."
sleep 2
curl -I https://ccxc.online/uploads/test/reload-test.txt

echo ""
echo "6. 测试原始文件..."
curl -I https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png

echo ""
echo "✅ nginx配置重新加载完成！"
echo ""
echo "如果仍然404，请检查："
echo "1. 文件是否真的存在于 /var/www/uploads/ 目录中"
echo "2. 文件权限是否正确"
echo "3. volume挂载是否正常工作"
