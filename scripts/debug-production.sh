#!/bin/bash

# 生产环境调试脚本

echo "🔍 调试生产环境图片404问题..."
echo "目标文件: https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png"
echo ""

# 1. 检查容器状态
echo "1. 检查容器状态："
docker-compose ps

echo ""
echo "2. 检查nginx容器中的文件是否存在："
docker-compose exec nginx ls -la /var/www/uploads/images/2025-09/ 2>/dev/null || echo "❌ 目录不存在或无法访问"

echo ""
echo "3. 检查app容器中的文件是否存在："
docker-compose exec app ls -la /app/uploads/images/2025-09/ 2>/dev/null || echo "❌ 目录不存在或无法访问"

echo ""
echo "4. 检查具体文件："
docker-compose exec nginx ls -la /var/www/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null || echo "❌ nginx容器中文件不存在"
docker-compose exec app ls -la /app/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null || echo "❌ app容器中文件不存在"

echo ""
echo "5. 检查nginx配置是否生效："
docker-compose exec nginx nginx -T | grep -A 10 "location /uploads"

echo ""
echo "6. 测试nginx内部访问："
docker-compose exec nginx curl -I http://localhost/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null || echo "❌ nginx内部访问失败"

echo ""
echo "7. 检查文件权限："
docker-compose exec nginx ls -la /var/www/uploads/ 2>/dev/null || echo "❌ 无法检查权限"

echo ""
echo "8. 检查nginx错误日志："
docker-compose logs nginx | tail -10

echo ""
echo "9. 检查volume挂载："
docker volume ls | grep uploads
docker volume inspect $(docker-compose config --volumes | grep uploads) 2>/dev/null || echo "❌ 无法检查volume"

echo ""
echo "10. 测试创建新文件："
docker-compose exec app mkdir -p /app/uploads/test
docker-compose exec app sh -c 'echo "Test file created at $(date)" > /app/uploads/test/debug.txt'
echo "创建测试文件后，检查nginx是否能访问："
docker-compose exec nginx ls -la /var/www/uploads/test/debug.txt 2>/dev/null || echo "❌ nginx无法访问新创建的文件"

echo ""
echo "11. 测试访问新创建的文件："
curl -I https://ccxc.online/uploads/test/debug.txt

echo ""
echo "🔧 建议的修复步骤："
echo "1. 如果文件在app容器中存在但nginx容器中不存在，说明volume挂载有问题"
echo "2. 如果nginx配置没有生效，需要重新加载配置"
echo "3. 如果权限有问题，需要修复文件权限"
echo ""
echo "快速修复命令："
echo "docker-compose restart nginx"
echo "docker-compose exec nginx nginx -s reload"
echo "docker-compose exec nginx chown -R nginx:nginx /var/www/uploads"
