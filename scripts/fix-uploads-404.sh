#!/bin/bash

# 修复uploads 404问题的完整脚本

echo "🔧 修复uploads 404问题..."
echo "目标文件: https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png"
echo ""

# 1. 检查并重新构建服务
echo "1. 重新构建和启动服务..."
docker-compose down
docker-compose up -d --build

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 2. 检查容器状态
echo "2. 检查容器状态："
docker-compose ps

# 3. 检查文件是否存在
echo ""
echo "3. 检查目标文件是否存在："
echo "在app容器中："
docker-compose exec app ls -la /app/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null && echo "✅ 文件存在于app容器" || echo "❌ 文件不存在于app容器"

echo "在nginx容器中："
docker-compose exec nginx ls -la /var/www/uploads/images/2025-09/1756978061895_sftyw1.png 2>/dev/null && echo "✅ 文件存在于nginx容器" || echo "❌ 文件不存在于nginx容器"

# 4. 如果文件不存在，创建测试文件
echo ""
echo "4. 创建测试文件结构..."
docker-compose exec app mkdir -p /app/uploads/images/2025-09
docker-compose exec app sh -c 'echo "Test image content - $(date)" > /app/uploads/images/2025-09/test.png'
docker-compose exec app sh -c 'echo "Original file recreation - $(date)" > /app/uploads/images/2025-09/1756978061895_sftyw1.png'

# 5. 检查volume挂载
echo ""
echo "5. 检查volume挂载是否正常："
docker-compose exec nginx ls -la /var/www/uploads/images/2025-09/ 2>/dev/null || echo "❌ nginx无法访问uploads目录"

# 6. 修复权限
echo ""
echo "6. 修复文件权限..."
docker-compose exec app chown -R 1000:1000 /app/uploads
docker-compose exec nginx chown -R nginx:nginx /var/www/uploads 2>/dev/null || echo "权限修复可能需要root权限"

# 7. 测试nginx配置
echo ""
echo "7. 测试nginx配置..."
docker-compose exec nginx nginx -t

# 8. 重新加载nginx
echo ""
echo "8. 重新加载nginx配置..."
docker-compose exec nginx nginx -s reload

# 9. 测试文件访问
echo ""
echo "9. 测试文件访问..."
echo "测试新创建的文件："
curl -I https://ccxc.online/uploads/images/2025-09/test.png

echo ""
echo "测试原始文件："
curl -I https://ccxc.online/uploads/images/2025-09/1756978061895_sftyw1.png

# 10. 检查nginx日志
echo ""
echo "10. 检查nginx访问日志："
docker-compose exec nginx tail -5 /var/log/nginx/access.log 2>/dev/null || echo "无法访问nginx日志"

echo ""
echo "检查nginx错误日志："
docker-compose exec nginx tail -5 /var/log/nginx/error.log 2>/dev/null || echo "无法访问nginx错误日志"

# 11. 最终测试
echo ""
echo "11. 最终测试 - 获取详细响应头："
curl -v https://ccxc.online/uploads/images/2025-09/test.png 2>&1 | head -20

echo ""
echo "✅ 修复脚本执行完成！"
echo ""
echo "📋 检查清单："
echo "1. 如果仍然404，文件可能真的不存在"
echo "2. 检查nginx配置中的location优先级"
echo "3. 确认docker volume挂载正常"
echo "4. 检查文件权限设置"
echo ""
echo "🔍 手动检查命令："
echo "docker-compose exec nginx ls -la /var/www/uploads/images/2025-09/"
echo "docker-compose exec app ls -la /app/uploads/images/2025-09/"
echo "docker-compose logs nginx | tail -20"
