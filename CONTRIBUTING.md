# 贡献指南

感谢您对 MP React Next.js 项目的关注！我们欢迎所有形式的贡献。

## 📋 目录

- [行为准则](#行为准则)
- [如何贡献](#如何贡献)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [提交规范](#提交规范)
- [问题报告](#问题报告)
- [功能请求](#功能请求)

## 🤝 行为准则

### 我们的承诺

为了营造一个开放和友好的环境，我们作为贡献者和维护者承诺：无论年龄、体型、残疾、种族、性别认同和表达、经验水平、国籍、个人形象、种族、宗教或性取向如何，参与我们项目和社区的每个人都能获得无骚扰的体验。

### 我们的标准

有助于创造积极环境的行为包括：
- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 执行

如果您遇到不当行为，请联系项目团队：<EMAIL>

## 🚀 如何贡献

### 贡献类型

我们欢迎以下类型的贡献：

1. **🐛 Bug 修复**
   - 修复已知问题
   - 改进错误处理
   - 性能优化

2. **✨ 新功能**
   - 添加新的组件
   - 实现新的功能模块
   - 改进用户体验

3. **📚 文档改进**
   - 修正文档错误
   - 添加使用示例
   - 翻译文档

4. **🧪 测试**
   - 添加测试用例
   - 改进测试覆盖率
   - 修复测试问题

5. **🎨 设计改进**
   - UI/UX 优化
   - 主题改进
   - 响应式设计

### 开始之前

1. **查看现有 Issues**
   - 检查是否已有相关问题
   - 避免重复工作

2. **阅读文档**
   - [开发指南](docs/DEVELOPMENT.md)
   - [架构设计](docs/ARCHITECTURE.md)
   - [API 文档](docs/API.md)

3. **了解项目结构**
   ```
   mp-react-nextjs/
   ├── src/              # 源代码
   ├── docs/             # 文档
   ├── __tests__/        # 测试文件
   ├── public/           # 静态资源
   └── scripts/          # 构建脚本
   ```

## 🛠️ 开发流程

### 1. Fork 项目

```bash
# 1. Fork 项目到您的 GitHub 账户
# 2. 克隆您的 Fork
git clone https://github.com/YOUR_USERNAME/mp-react-nextjs.git
cd mp-react-nextjs

# 3. 添加上游仓库
git remote add upstream https://github.com/ORIGINAL_OWNER/mp-react-nextjs.git
```

### 2. 设置开发环境

```bash
# 安装依赖
npm install

# 复制环境变量
cp .env.example .env.local

# 启动开发服务器
npm run dev
```

### 3. 创建功能分支

```bash
# 从 main 分支创建新分支
git checkout -b feature/your-feature-name

# 或者修复 bug
git checkout -b fix/your-bug-fix
```

### 4. 开发和测试

```bash
# 运行测试
npm test

# 运行 E2E 测试
npm run test:e2e

# 检查代码质量
npm run lint

# 格式化代码
npm run format
```

### 5. 提交更改

```bash
# 添加更改
git add .

# 提交更改（遵循提交规范）
git commit -m "feat: add new user management feature"

# 推送到您的 Fork
git push origin feature/your-feature-name
```

### 6. 创建 Pull Request

1. 访问您的 Fork 页面
2. 点击 "New Pull Request"
3. 填写 PR 模板
4. 等待代码审查

## 📝 代码规范

### TypeScript 规范

```typescript
// ✅ 好的示例
interface UserProps {
  id: string
  name: string
  email: string
  role: UserRole
}

const UserCard: React.FC<UserProps> = ({ id, name, email, role }) => {
  return (
    <div className={styles.userCard}>
      <h3>{name}</h3>
      <p>{email}</p>
      <span>{role}</span>
    </div>
  )
}

// ❌ 避免的写法
const UserCard = (props: any) => {
  return <div>{props.name}</div>
}
```

### React 组件规范

```typescript
// ✅ 组件结构
import React from 'react'
import { ComponentProps } from './types'
import styles from './Component.module.css'

const Component: React.FC<ComponentProps> = ({
  prop1,
  prop2,
  ...props
}) => {
  // Hooks
  const [state, setState] = useState()
  
  // 事件处理
  const handleClick = useCallback(() => {
    // 处理逻辑
  }, [])
  
  // 渲染
  return (
    <div className={styles.container} {...props}>
      {/* 组件内容 */}
    </div>
  )
}

export default Component
```

### 样式规范

```css
/* ✅ CSS Modules 规范 */
.container {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md);
  background: var(--bg-color);
  border-radius: var(--border-radius);
}

.title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }
}
```

### 测试规范

```typescript
// ✅ 测试示例
import { render, screen, fireEvent } from '@testing-library/react'
import { UserCard } from '../UserCard'

describe('UserCard', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'admin'
  }

  it('should render user information correctly', () => {
    render(<UserCard user={mockUser} />)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('admin')).toBeInTheDocument()
  })

  it('should handle click events', () => {
    const handleClick = jest.fn()
    render(<UserCard user={mockUser} onClick={handleClick} />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledWith(mockUser)
  })
})
```

## 📋 提交规范

### 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型 (type)

- **feat**: 新功能
- **fix**: Bug 修复
- **docs**: 文档更新
- **style**: 代码格式修改
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 范围 (scope)

- **auth**: 认证相关
- **user**: 用户管理
- **ui**: UI 组件
- **api**: API 相关
- **test**: 测试相关
- **docs**: 文档相关

### 示例

```bash
# 新功能
git commit -m "feat(user): add user profile editing functionality"

# Bug 修复
git commit -m "fix(auth): resolve login redirect issue"

# 文档更新
git commit -m "docs(api): update user API documentation"

# 重构
git commit -m "refactor(ui): improve button component structure"
```

## 🐛 问题报告

### 报告 Bug

使用 [Bug 报告模板](https://github.com/your-repo/issues/new?template=bug_report.md) 创建 Issue。

**包含信息**:
- 问题描述
- 重现步骤
- 预期行为
- 实际行为
- 环境信息
- 截图或录屏

### Bug 报告示例

```markdown
**问题描述**
用户登录后，侧边栏菜单不显示

**重现步骤**
1. 打开登录页面
2. 输入正确的用户名和密码
3. 点击登录按钮
4. 观察侧边栏

**预期行为**
侧边栏应该显示用户权限对应的菜单项

**实际行为**
侧边栏为空白

**环境信息**
- OS: macOS 13.0
- Browser: Chrome 119.0
- Node.js: 18.17.0
```

## ✨ 功能请求

### 请求新功能

使用 [功能请求模板](https://github.com/your-repo/issues/new?template=feature_request.md) 创建 Issue。

**包含信息**:
- 功能描述
- 使用场景
- 解决的问题
- 可能的实现方案
- 替代方案

## 🔍 代码审查

### 审查清单

**功能性**
- [ ] 功能是否按预期工作
- [ ] 是否有边界情况处理
- [ ] 错误处理是否完善

**代码质量**
- [ ] 代码是否清晰易读
- [ ] 是否遵循项目规范
- [ ] 是否有适当的注释

**性能**
- [ ] 是否有性能问题
- [ ] 是否有内存泄漏
- [ ] 是否有不必要的重渲染

**测试**
- [ ] 是否有足够的测试覆盖
- [ ] 测试是否通过
- [ ] 是否有集成测试

**文档**
- [ ] 是否更新了相关文档
- [ ] API 变更是否有文档
- [ ] 是否有使用示例

## 🎯 最佳实践

### 开发建议

1. **小步提交**
   - 每次提交解决一个问题
   - 提交消息清晰明确

2. **测试驱动**
   - 先写测试，再写代码
   - 保持高测试覆盖率

3. **文档同步**
   - 代码变更时更新文档
   - 添加必要的注释

4. **性能考虑**
   - 避免不必要的重渲染
   - 合理使用缓存

5. **用户体验**
   - 考虑加载状态
   - 提供友好的错误提示

### 沟通建议

1. **提前沟通**
   - 大功能开发前先讨论
   - 不确定时及时询问

2. **详细描述**
   - PR 描述要详细
   - 说明变更原因和影响

3. **积极反馈**
   - 及时回应审查意见
   - 主动寻求帮助

## 📞 联系我们

- **GitHub Issues**: [项目 Issues](https://github.com/your-repo/issues)
- **邮箱**: <EMAIL>
- **讨论区**: [GitHub Discussions](https://github.com/your-repo/discussions)

## 🙏 致谢

感谢所有为项目做出贡献的开发者！

---

再次感谢您的贡献！每一个贡献都让这个项目变得更好。
