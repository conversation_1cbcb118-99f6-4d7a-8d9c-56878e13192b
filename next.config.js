/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性功能
  experimental: {
    // 启用 Turbopack（开发环境）
    turbo: {
      rules: {
        "*.svg": {
          loaders: ["@svgr/webpack"],
          as: "*.js",
        },
      },
    },
  },

  // 编译配置
  compiler: {
    // 移除 console.log（生产环境）
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error", "warn"],
          }
        : false,
  },

  // 性能优化
  poweredByHeader: false,
  compress: true,
  productionBrowserSourceMaps: false,

  // 图片优化
  images: {
    // 启用图片优化
    formats: ["image/webp", "image/avif"],
    // 图片域名白名单
    domains: ["localhost", "images.unsplash.com", "via.placeholder.com"],
    // 图片尺寸配置
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 输出配置
  output: "standalone",

  // 静态资源优化
  assetPrefix: process.env.NODE_ENV === "production" ? process.env.CDN_URL : "",

  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 性能优化配置
    if (!dev && !isServer) {
      // 代码分割优化
      config.optimization.splitChunks = {
        chunks: "all",
        cacheGroups: {
          // 第三方库单独打包
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: "vendors",
            chunks: "all",
            priority: 10,
          },
          // Ant Design 单独打包
          antd: {
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            name: "antd",
            chunks: "all",
            priority: 20,
          },
          // React 相关库单独打包
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: "react",
            chunks: "all",
            priority: 30,
          },
          // 图表库单独打包
          // charts: {
          //   test: /[\\/]node_modules[\\/](echarts|@ant-design\/plots)[\\/]/,
          //   name: 'charts',
          //   chunks: 'all',
          //   priority: 15,
          // },
          // 工具库单独打包
          utils: {
            test: /[\\/]node_modules[\\/](lodash|dayjs|axios)[\\/]/,
            name: "utils",
            chunks: "all",
            priority: 15,
          },
          // 公共代码
          common: {
            name: "common",
            minChunks: 2,
            chunks: "all",
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      };

      // 压缩配置
      config.optimization.minimize = true;

      // 模块连接优化
      config.optimization.concatenateModules = true;

      // Tree Shaking 优化
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    // 别名配置
    config.resolve.alias = {
      ...config.resolve.alias,
      "@": require("path").resolve(__dirname, "src"),
    };

    // SVG 处理
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    // 性能分析
    if (process.env.ANALYZE === "true") {
      const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: "static",
          openAnalyzer: false,
          reportFilename: "bundle-analyzer-report.html",
        })
      );
    }

    // 开发环境优化
    if (dev) {
      // 热更新优化
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: /node_modules/,
      };
    }

    return config;
  },

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: "/",
        destination: "/main/dashboard",
        permanent: false,
      },
    ];
  },

  // 重写配置
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "/api/:path*",
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      // {
      //   source: "/(.*)",
      //   headers: [
      //     // 安全头部
      //     {
      //       key: "X-Frame-Options",
      //       value: "DENY",
      //     },
      //     {
      //       key: "X-Content-Type-Options",
      //       value: "nosniff",
      //     },
      //     {
      //       key: "Referrer-Policy",
      //       value: "origin-when-cross-origin",
      //     },
      //     {
      //       key: "X-XSS-Protection",
      //       value: "1; mode=block",
      //     },
      //     {
      //       key: "Strict-Transport-Security",
      //       value: "max-age=31536000; includeSubDomains; preload",
      //     },
      //     {
      //       key: "Content-Security-Policy",
      //       value:
      //         "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;",
      //     },
      //     // 缓存头部
      //     {
      //       key: "Cache-Control",
      //       value: "public, max-age=31536000, immutable",
      //     },
      //   ],
      // },
      {
        source: "/api/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "no-store, max-age=0",
          },
        ],
      },
      {
        source: "/_next/static/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        source: "/api/:path*",
        headers: [
          { key: "Access-Control-Allow-Origin", value: "*" },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "Content-Type, Authorization",
          },
        ],
      },
    ];
  },

  // TypeScript 配置
  typescript: {
    // 生产构建时忽略类型错误（不推荐）
    ignoreBuildErrors: false,
  },

  // ESLint 配置
  eslint: {
    // 生产构建时忽略 ESLint 错误（不推荐）
    ignoreDuringBuilds: false,
  },

  // 国际化配置
  i18n: {
    locales: ["zh-CN", "en-US"],
    defaultLocale: "zh-CN",
  },
};

module.exports = nextConfig;
