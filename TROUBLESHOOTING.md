# 🔧 故障排除指南

## 图片404问题解决方案

### 问题描述
部署后访问上传的图片出现404错误。

### 常见原因
1. **路径配置不匹配**：nginx配置的路径与实际文件路径不一致
2. **容器挂载问题**：docker容器间的文件共享配置错误
3. **权限问题**：文件权限设置不正确
4. **nginx配置错误**：nginx无法正确处理静态文件请求

### 解决步骤

#### 1. 快速诊断
```bash
# 运行诊断脚本
bash scripts/fix-image-404.sh
```

#### 2. 手动检查
```bash
# 检查容器状态
docker-compose ps

# 检查nginx容器中的uploads目录
docker-compose exec nginx ls -la /var/www/uploads/

# 检查app容器中的uploads目录
docker-compose exec app ls -la /app/uploads/

# 检查nginx配置
docker-compose exec nginx nginx -t
```

#### 3. 创建测试文件
```bash
# 在app容器中创建测试文件
docker-compose exec app mkdir -p /app/uploads/test
docker-compose exec app sh -c 'echo "Test content" > /app/uploads/test/test.txt'

# 测试访问
curl -I http://localhost/uploads/test/test.txt
```

#### 4. 检查配置文件

**nginx.conf 关键配置：**
```nginx
location /uploads/ {
    alias /var/www/uploads/;
    try_files $uri =404;
}
```

**docker-compose.yml 关键配置：**
```yaml
services:
  app:
    volumes:
      - uploads_data:/app/uploads
  nginx:
    volumes:
      - uploads_data:/var/www/uploads

volumes:
  uploads_data:
    driver: local
```

**环境变量配置：**
```env
UPLOAD_DIR=/app/uploads
```

#### 5. 重新部署
```bash
# 完整重新部署
bash scripts/deploy-and-test.sh
```

### 测试工具

#### 1. 测试上传页面
访问：`http://localhost:3000/test-upload`

#### 2. 测试API
```bash
# 获取配置信息
curl http://localhost:3000/api/test-upload

# 测试文件上传
curl -X POST -F "file=@test.jpg" http://localhost:3000/api/test-upload
```

### 常见错误及解决方案

#### 错误1：nginx: [emerg] host not found in upstream
**原因**：nginx配置中的upstream指向了错误的服务名
**解决**：确保upstream配置为 `server app:3000;`

#### 错误2：404 Not Found
**原因**：文件路径不匹配或文件不存在
**解决**：
1. 检查nginx alias配置
2. 确认文件确实存在于指定路径
3. 检查文件权限

#### 错误3：Permission denied
**原因**：文件权限问题
**解决**：
```bash
docker-compose exec nginx chown -R nginx:nginx /var/www/uploads
docker-compose exec app chown -R node:node /app/uploads
```

#### 错误4：Volume mount failed
**原因**：docker volume挂载失败
**解决**：
```bash
docker-compose down
docker volume rm $(docker volume ls -q)
docker-compose up -d --build
```

### 预防措施

1. **使用命名卷**：避免使用绑定挂载，使用docker命名卷
2. **统一路径配置**：确保所有配置文件中的路径一致
3. **添加健康检查**：在docker-compose中添加健康检查
4. **监控日志**：定期检查nginx和应用日志

### 监控和日志

```bash
# 查看nginx日志
docker-compose logs -f nginx

# 查看应用日志
docker-compose logs -f app

# 查看实时访问日志
docker-compose exec nginx tail -f /var/log/nginx/access.log

# 查看错误日志
docker-compose exec nginx tail -f /var/log/nginx/error.log
```

### 联系支持

如果以上步骤都无法解决问题，请提供以下信息：
1. 运行 `bash scripts/fix-image-404.sh` 的完整输出
2. nginx错误日志
3. 应用错误日志
4. docker-compose配置文件
5. 具体的错误信息和复现步骤
