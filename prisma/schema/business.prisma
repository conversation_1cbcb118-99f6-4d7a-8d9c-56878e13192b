model Business {
    id          Int      @id @default(autoincrement()) @map("business_id")
    name        String
    description String?
    address     String?
    phone       String?
    email       String?
    type        Int      @default(1) // 1 合同类型, 2 合同类型
    status      Int      @default(1) // 1 active, 2 inactive
    createdAt   DateTime @default(now()) @map("created_at")
    updatedAt   DateTime? @updatedAt @map("updated_at")
    shops       Shop[]

    @@map("businesses")
}
