/*
  Warnings:

  - The values [FINISHED] on the enum `OrderStatus` will be removed. If these variants are still used in the database, this will fail.
  - Added the required column `shop_id` to the `consume_orders` table without a default value. This is not possible if the table is not empty.
  - Made the column `car_id` on table `consume_orders` required. This step will fail if there are existing NULL values in that column.
  - Made the column `station_id` on table `consume_orders` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "public"."OrderStatus_new" AS ENUM ('PENDING', 'PAID', 'USING', 'COMPLETED', 'CANCELED');
ALTER TABLE "public"."orders" ALTER COLUMN "status" TYPE "public"."OrderStatus_new" USING ("status"::text::"public"."OrderStatus_new");
ALTER TYPE "public"."OrderStatus" RENAME TO "OrderStatus_old";
ALTER TYPE "public"."OrderStatus_new" RENAME TO "OrderStatus";
DROP TYPE "public"."OrderStatus_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_car_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_station_id_fkey";

-- AlterTable
ALTER TABLE "public"."consume_orders" ADD COLUMN     "shop_id" INTEGER NOT NULL,
ALTER COLUMN "car_id" SET NOT NULL,
ALTER COLUMN "station_id" SET NOT NULL;

-- AlterTable
ALTER TABLE "public"."orders" ADD COLUMN     "completed_at" TIMESTAMP(3);

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("station_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_shop_id_fkey" FOREIGN KEY ("shop_id") REFERENCES "public"."shops"("shop_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_car_id_fkey" FOREIGN KEY ("car_id") REFERENCES "public"."cars"("car_id") ON DELETE RESTRICT ON UPDATE CASCADE;
