/*
  Warnings:

  - The values [WAITING,WASHING,CO<PERSON>LETED,CANC<PERSON>ED] on the enum `OrderStatus` will be removed. If these variants are still used in the database, this will fail.
  - The primary key for the `orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `created_at` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `order_id` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `order_number` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `shop_id` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `shop_user_id` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `station_id` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `orders` table. All the data in the column will be lost.
  - You are about to alter the column `amount` on the `orders` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Decimal(10,2)`.
  - A unique constraint covering the columns `[orderNo]` on the table `orders` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `shop_user_id` to the `cars` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `orders` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `orderNo` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `orders` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "public"."OrderType" AS ENUM ('RECHARGE', 'CONSUME', 'REFUND', 'WASH');

-- CreateEnum
CREATE TYPE "public"."PayMethod" AS ENUM ('WECHAT', 'ALIPAY', 'BALANCE');

-- AlterEnum
BEGIN;
CREATE TYPE "public"."OrderStatus_new" AS ENUM ('PENDING', 'PAID', 'FAILED', 'REFUNDED');
ALTER TABLE "public"."orders" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "public"."orders" ALTER COLUMN "status" TYPE "public"."OrderStatus_new" USING ("status"::text::"public"."OrderStatus_new");
ALTER TYPE "public"."OrderStatus" RENAME TO "OrderStatus_old";
ALTER TYPE "public"."OrderStatus_new" RENAME TO "OrderStatus";
DROP TYPE "public"."OrderStatus_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_shop_user_id_fkey";

-- DropIndex
DROP INDEX "public"."orders_order_number_key";

-- AlterTable
ALTER TABLE "public"."cars" ADD COLUMN     "shop_user_id" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_pkey",
DROP COLUMN "created_at",
DROP COLUMN "order_id",
DROP COLUMN "order_number",
DROP COLUMN "shop_id",
DROP COLUMN "shop_user_id",
DROP COLUMN "station_id",
DROP COLUMN "updated_at",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "id" TEXT NOT NULL,
ADD COLUMN     "orderNo" VARCHAR(30) NOT NULL,
ADD COLUMN     "payMethod" "public"."PayMethod",
ADD COLUMN     "shopId" INTEGER,
ADD COLUMN     "shopUserId" INTEGER,
ADD COLUMN     "stationId" INTEGER,
ADD COLUMN     "type" "public"."OrderType" NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "amount" SET DATA TYPE DECIMAL(10,2),
ALTER COLUMN "status" DROP DEFAULT,
ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");

-- CreateTable
CREATE TABLE "public"."recharge_orders" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "bonus" DECIMAL(10,2),
    "campaign" VARCHAR(50),
    "shopUserId" INTEGER,

    CONSTRAINT "recharge_orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."consume_orders" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "packageId" VARCHAR(36),
    "stationId" INTEGER,
    "carId" INTEGER,

    CONSTRAINT "consume_orders_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "recharge_orders_orderId_key" ON "public"."recharge_orders"("orderId");

-- CreateIndex
CREATE UNIQUE INDEX "consume_orders_orderId_key" ON "public"."consume_orders"("orderId");

-- CreateIndex
CREATE UNIQUE INDEX "orders_orderNo_key" ON "public"."orders"("orderNo");

-- AddForeignKey
ALTER TABLE "public"."cars" ADD CONSTRAINT "cars_shop_user_id_fkey" FOREIGN KEY ("shop_user_id") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_shopUserId_fkey" FOREIGN KEY ("shopUserId") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_shopId_fkey" FOREIGN KEY ("shopId") REFERENCES "public"."shops"("shop_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_stationId_fkey" FOREIGN KEY ("stationId") REFERENCES "public"."stations"("station_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."recharge_orders" ADD CONSTRAINT "recharge_orders_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "public"."orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."recharge_orders" ADD CONSTRAINT "recharge_orders_shopUserId_fkey" FOREIGN KEY ("shopUserId") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "public"."orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_stationId_fkey" FOREIGN KEY ("stationId") REFERENCES "public"."stations"("station_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_carId_fkey" FOREIGN KEY ("carId") REFERENCES "public"."cars"("car_id") ON DELETE SET NULL ON UPDATE CASCADE;
