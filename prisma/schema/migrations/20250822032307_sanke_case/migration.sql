/*
  Warnings:

  - The primary key for the `businesses` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `createdAt` on the `businesses` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `businesses` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `businesses` table. All the data in the column will be lost.
  - The primary key for the `orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `createdAt` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `orderNumber` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `orders` table. All the data in the column will be lost.
  - The primary key for the `shops` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `businessHours` on the `shops` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `shops` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `shops` table. All the data in the column will be lost.
  - You are about to drop the column `thumbnailImg` on the `shops` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `shops` table. All the data in the column will be lost.
  - The primary key for the `stations` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `createdAt` on the `stations` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `stations` table. All the data in the column will be lost.
  - You are about to drop the column `shopId` on the `stations` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `stations` table. All the data in the column will be lost.
  - The primary key for the `users` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `createdAt` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `id` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLoginTime` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[order_number]` on the table `orders` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `order_number` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `shop_id` to the `stations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `stations` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."shops" DROP CONSTRAINT "shops_businessId_fkey";

-- DropForeignKey
ALTER TABLE "public"."stations" DROP CONSTRAINT "stations_shopId_fkey";

-- DropIndex
DROP INDEX "public"."orders_orderNumber_key";

-- AlterTable
ALTER TABLE "public"."businesses" DROP CONSTRAINT "businesses_pkey",
DROP COLUMN "createdAt",
DROP COLUMN "id",
DROP COLUMN "updatedAt",
ADD COLUMN     "business_id" SERIAL NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3),
ADD CONSTRAINT "businesses_pkey" PRIMARY KEY ("business_id");

-- AlterTable
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_pkey",
DROP COLUMN "createdAt",
DROP COLUMN "id",
DROP COLUMN "orderNumber",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "order_id" SERIAL NOT NULL,
ADD COLUMN     "order_number" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" INTEGER NOT NULL,
ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("order_id");

-- AlterTable
ALTER TABLE "public"."shops" DROP CONSTRAINT "shops_pkey",
DROP COLUMN "businessHours",
DROP COLUMN "createdAt",
DROP COLUMN "id",
DROP COLUMN "thumbnailImg",
DROP COLUMN "updatedAt",
ADD COLUMN     "business_hours" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "shop_id" SERIAL NOT NULL,
ADD COLUMN     "thumbnail_img" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3),
ADD CONSTRAINT "shops_pkey" PRIMARY KEY ("shop_id");

-- AlterTable
ALTER TABLE "public"."stations" DROP CONSTRAINT "stations_pkey",
DROP COLUMN "createdAt",
DROP COLUMN "id",
DROP COLUMN "shopId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "shop_id" INTEGER NOT NULL,
ADD COLUMN     "station_id" SERIAL NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD CONSTRAINT "stations_pkey" PRIMARY KEY ("station_id");

-- AlterTable
ALTER TABLE "public"."users" DROP CONSTRAINT "users_pkey",
DROP COLUMN "createdAt",
DROP COLUMN "id",
DROP COLUMN "lastLoginTime",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "last_login_time" TIMESTAMP(3),
ADD COLUMN     "updated_at" TIMESTAMP(3),
ADD COLUMN     "user_id" SERIAL NOT NULL,
ADD CONSTRAINT "users_pkey" PRIMARY KEY ("user_id");

-- CreateTable
CREATE TABLE "public"."carousel_images" (
    "carousel_id" SERIAL NOT NULL,
    "shop_id" INTEGER NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "type" INTEGER NOT NULL DEFAULT 1,
    "pageId" INTEGER NOT NULL DEFAULT 1,

    CONSTRAINT "carousel_images_pkey" PRIMARY KEY ("carousel_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "orders_order_number_key" ON "public"."orders"("order_number");

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."shops" ADD CONSTRAINT "shops_businessId_fkey" FOREIGN KEY ("businessId") REFERENCES "public"."businesses"("business_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."stations" ADD CONSTRAINT "stations_shop_id_fkey" FOREIGN KEY ("shop_id") REFERENCES "public"."shops"("shop_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."carousel_images" ADD CONSTRAINT "carousel_images_shop_id_fkey" FOREIGN KEY ("shop_id") REFERENCES "public"."shops"("shop_id") ON DELETE RESTRICT ON UPDATE CASCADE;
