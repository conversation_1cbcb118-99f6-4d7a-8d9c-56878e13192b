/*
  Warnings:

  - The values [FAILED,REFUNDED] on the enum `OrderStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [USE] on the enum `StationStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `station_id` on the `orders` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "public"."OrderStatus_new" AS ENUM ('PENDING', 'PAID', 'USING', 'FINISHED', 'CANCELED');
ALTER TABLE "public"."orders" ALTER COLUMN "status" TYPE "public"."OrderStatus_new" USING ("status"::text::"public"."OrderStatus_new");
ALTER TYPE "public"."OrderStatus" RENAME TO "OrderStatus_old";
ALTER TYPE "public"."OrderStatus_new" RENAME TO "OrderStatus";
DROP TYPE "public"."OrderStatus_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "public"."StationStatus_new" AS ENUM ('IN_USE', 'IDLE', 'MAINTENANCE');
ALTER TABLE "public"."stations" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "public"."stations" ALTER COLUMN "status" TYPE "public"."StationStatus_new" USING ("status"::text::"public"."StationStatus_new");
ALTER TYPE "public"."StationStatus" RENAME TO "StationStatus_old";
ALTER TYPE "public"."StationStatus_new" RENAME TO "StationStatus";
DROP TYPE "public"."StationStatus_old";
ALTER TABLE "public"."stations" ALTER COLUMN "status" SET DEFAULT 'IDLE';
COMMIT;

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_shop_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_shop_user_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_station_id_fkey";

-- AlterTable
ALTER TABLE "public"."orders" DROP COLUMN "station_id";
