-- AlterEnum
ALTER TYPE "public"."PayMethod" ADD VALUE 'FREE_WASH';

-- AlterTable
ALTER TABLE "public"."consume_orders" ADD COLUMN     "discount" DOUBLE PRECISION,
ADD COLUMN     "is_reviewed" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "payAmount" INTEGER,
ADD COLUMN     "price" INTEGER;

-- AlterTable
ALTER TABLE "public"."orders" ADD COLUMN     "bonus" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "public"."reviews" (
    "review_id" SERIAL NOT NULL,
    "shop_id" INTEGER NOT NULL,
    "shop_user_id" INTEGER NOT NULL,
    "rating" INTEGER NOT NULL,
    "comment" TEXT,
    "images" JSONB,
    "order_id" VARCHAR(36),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "reviews_pkey" PRIMARY KEY ("review_id")
);

-- CreateTable
CREATE TABLE "public"."user_memberships" (
    "id" SERIAL NOT NULL,
    "shop_user_id" INTEGER NOT NULL,
    "levelId" INTEGER NOT NULL,
    "balance" INTEGER NOT NULL DEFAULT 0,
    "freeWashRemain" INTEGER NOT NULL DEFAULT 0,
    "expireAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."membership_levels" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "discount" DOUBLE PRECISION NOT NULL,
    "minAmount" INTEGER NOT NULL,
    "freeWash" INTEGER NOT NULL DEFAULT 0,
    "duration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "membership_levels_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."_MembershipLevelToShopUser" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_MembershipLevelToShopUser_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "reviews_shop_id_shop_user_id_order_id_key" ON "public"."reviews"("shop_id", "shop_user_id", "order_id");

-- CreateIndex
CREATE INDEX "_MembershipLevelToShopUser_B_index" ON "public"."_MembershipLevelToShopUser"("B");

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_shop_id_fkey" FOREIGN KEY ("shop_id") REFERENCES "public"."shops"("shop_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_shop_user_id_fkey" FOREIGN KEY ("shop_user_id") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("order_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_memberships" ADD CONSTRAINT "user_memberships_shop_user_id_fkey" FOREIGN KEY ("shop_user_id") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_memberships" ADD CONSTRAINT "user_memberships_levelId_fkey" FOREIGN KEY ("levelId") REFERENCES "public"."membership_levels"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_MembershipLevelToShopUser" ADD CONSTRAINT "_MembershipLevelToShopUser_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."membership_levels"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_MembershipLevelToShopUser" ADD CONSTRAINT "_MembershipLevelToShopUser_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE CASCADE ON UPDATE CASCADE;
