/*
  Warnings:

  - You are about to drop the column `imageUrl` on the `carousel_images` table. All the data in the column will be lost.
  - You are about to drop the column `businessId` on the `shops` table. All the data in the column will be lost.
  - You are about to drop the column `thumbnailImg` on the `stations` table. All the data in the column will be lost.
  - Added the required column `image_url` to the `carousel_images` table without a default value. This is not possible if the table is not empty.
  - Added the required column `business_id` to the `shops` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."shops" DROP CONSTRAINT "shops_businessId_fkey";

-- AlterTable
ALTER TABLE "public"."carousel_images" DROP COLUMN "imageUrl",
ADD COLUMN     "image_url" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."shops" DROP COLUMN "businessId",
ADD COLUMN     "business_id" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "public"."stations" DROP COLUMN "thumbnailImg",
ADD COLUMN     "thumbnail_img" TEXT;

-- AddForeignKey
ALTER TABLE "public"."shops" ADD CONSTRAINT "shops_business_id_fkey" FOREIGN KEY ("business_id") REFERENCES "public"."businesses"("business_id") ON DELETE RESTRICT ON UPDATE CASCADE;
