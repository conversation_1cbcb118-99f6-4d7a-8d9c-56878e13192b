/*
  Warnings:

  - A unique constraint covering the columns `[phone]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `price` to the `stations` table without a default value. This is not possible if the table is not empty.
  - Made the column `phone` on table `users` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "public"."shops" ADD COLUMN     "thumbnail_img" TEXT;

-- AlterTable
ALTER TABLE "public"."stations" ADD COLUMN     "price" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "thumbnail_img" TEXT;

-- AlterTable
ALTER TABLE "public"."users" ALTER COLUMN "phone" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "users_phone_key" ON "public"."users"("phone");
