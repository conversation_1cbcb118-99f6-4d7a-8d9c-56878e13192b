/*
  Warnings:

  - A unique constraint covering the columns `[shop_user_id,order_id]` on the table `reviews` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "public"."reviews_shop_id_shop_user_id_order_id_key";

-- AlterTable
ALTER TABLE "public"."reviews" ADD COLUMN     "is_hidden" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "public"."shops" ADD COLUMN     "reviewCount" INTEGER NOT NULL DEFAULT 0;

-- CreateIndex
CREATE INDEX "reviews_shop_id_idx" ON "public"."reviews"("shop_id");

-- CreateIndex
CREATE UNIQUE INDEX "reviews_shop_user_id_order_id_key" ON "public"."reviews"("shop_user_id", "order_id");
