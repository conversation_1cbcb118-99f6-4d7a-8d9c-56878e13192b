/*
  Warnings:

  - The primary key for the `consume_orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `consume_orders` table. All the data in the column will be lost.
  - The primary key for the `orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `orders` table. All the data in the column will be lost.
  - The primary key for the `recharge_orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `recharge_orders` table. All the data in the column will be lost.
  - The required column `consume_order_id` was added to the `consume_orders` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `order_id` was added to the `orders` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `recharge_order_id` was added to the `recharge_orders` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- DropForeignKey
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_order_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."recharge_orders" DROP CONSTRAINT "recharge_orders_order_id_fkey";

-- AlterTable
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_pkey",
DROP COLUMN "id",
ADD COLUMN     "consume_order_id" TEXT NOT NULL,
ADD CONSTRAINT "consume_orders_pkey" PRIMARY KEY ("consume_order_id");

-- AlterTable
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_pkey",
DROP COLUMN "id",
ADD COLUMN     "order_id" TEXT NOT NULL,
ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("order_id");

-- AlterTable
ALTER TABLE "public"."recharge_orders" DROP CONSTRAINT "recharge_orders_pkey",
DROP COLUMN "id",
ADD COLUMN     "recharge_order_id" TEXT NOT NULL,
ADD CONSTRAINT "recharge_orders_pkey" PRIMARY KEY ("recharge_order_id");

-- AddForeignKey
ALTER TABLE "public"."recharge_orders" ADD CONSTRAINT "recharge_orders_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("order_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("order_id") ON DELETE RESTRICT ON UPDATE CASCADE;
