/*
  Warnings:

  - You are about to drop the column `carId` on the `consume_orders` table. All the data in the column will be lost.
  - You are about to drop the column `orderId` on the `consume_orders` table. All the data in the column will be lost.
  - You are about to drop the column `packageId` on the `consume_orders` table. All the data in the column will be lost.
  - You are about to drop the column `stationId` on the `consume_orders` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `payMethod` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `shopId` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `shopUserId` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `stationId` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `orderId` on the `recharge_orders` table. All the data in the column will be lost.
  - You are about to drop the column `shopUserId` on the `recharge_orders` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[order_id]` on the table `consume_orders` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[order_id]` on the table `recharge_orders` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `order_id` to the `consume_orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `order_id` to the `recharge_orders` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_carId_fkey";

-- DropForeignKey
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_orderId_fkey";

-- DropForeignKey
ALTER TABLE "public"."consume_orders" DROP CONSTRAINT "consume_orders_stationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_shopId_fkey";

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_shopUserId_fkey";

-- DropForeignKey
ALTER TABLE "public"."orders" DROP CONSTRAINT "orders_stationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."recharge_orders" DROP CONSTRAINT "recharge_orders_orderId_fkey";

-- DropForeignKey
ALTER TABLE "public"."recharge_orders" DROP CONSTRAINT "recharge_orders_shopUserId_fkey";

-- DropIndex
DROP INDEX "public"."consume_orders_orderId_key";

-- DropIndex
DROP INDEX "public"."recharge_orders_orderId_key";

-- AlterTable
ALTER TABLE "public"."consume_orders" DROP COLUMN "carId",
DROP COLUMN "orderId",
DROP COLUMN "packageId",
DROP COLUMN "stationId",
ADD COLUMN     "car_id" INTEGER,
ADD COLUMN     "order_id" TEXT NOT NULL,
ADD COLUMN     "package_id" VARCHAR(36),
ADD COLUMN     "station_id" INTEGER;

-- AlterTable
ALTER TABLE "public"."orders" DROP COLUMN "createdAt",
DROP COLUMN "payMethod",
DROP COLUMN "shopId",
DROP COLUMN "shopUserId",
DROP COLUMN "stationId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "pay_method" "public"."PayMethod",
ADD COLUMN     "shop_id" INTEGER,
ADD COLUMN     "shop_user_id" INTEGER,
ADD COLUMN     "station_id" INTEGER,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "public"."recharge_orders" DROP COLUMN "orderId",
DROP COLUMN "shopUserId",
ADD COLUMN     "order_id" TEXT NOT NULL,
ADD COLUMN     "shop_user_id" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "consume_orders_order_id_key" ON "public"."consume_orders"("order_id");

-- CreateIndex
CREATE UNIQUE INDEX "recharge_orders_order_id_key" ON "public"."recharge_orders"("order_id");

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_shop_user_id_fkey" FOREIGN KEY ("shop_user_id") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_shop_id_fkey" FOREIGN KEY ("shop_id") REFERENCES "public"."shops"("shop_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("station_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."recharge_orders" ADD CONSTRAINT "recharge_orders_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."recharge_orders" ADD CONSTRAINT "recharge_orders_shop_user_id_fkey" FOREIGN KEY ("shop_user_id") REFERENCES "public"."shop_users"("shop_user_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("station_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consume_orders" ADD CONSTRAINT "consume_orders_car_id_fkey" FOREIGN KEY ("car_id") REFERENCES "public"."cars"("car_id") ON DELETE SET NULL ON UPDATE CASCADE;
