/*
  Warnings:

  - The `status` column on the `orders` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `shops` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `stations` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `shop_id` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `station_id` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Made the column `phone` on table `shop_users` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "public"."OrderStatus" AS ENUM ('PENDING', 'WAITING', 'WASHING', 'COMPLETED', 'CANCELED');

-- CreateEnum
CREATE TYPE "public"."ShopStatus" AS ENUM ('PAUSED', 'ACTIVE', 'CLOSED');

-- CreateEnum
CREATE TYPE "public"."StationStatus" AS ENUM ('USE', 'IDLE', 'MAINTENANCE');

-- AlterTable
ALTER TABLE "public"."orders" ADD COLUMN     "shop_id" INTEGER NOT NULL,
ADD COLUMN     "station_id" INTEGER NOT NULL,
DROP COLUMN "status",
ADD COLUMN     "status" "public"."OrderStatus" NOT NULL DEFAULT 'PENDING';

-- AlterTable
ALTER TABLE "public"."shop_users" ADD COLUMN     "last_login_at" TIMESTAMP(3),
ALTER COLUMN "phone" SET NOT NULL,
ALTER COLUMN "password" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."shops" DROP COLUMN "status",
ADD COLUMN     "status" "public"."ShopStatus" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "public"."stations" DROP COLUMN "status",
ADD COLUMN     "status" "public"."StationStatus" NOT NULL DEFAULT 'IDLE';

-- CreateTable
CREATE TABLE "public"."cars" (
    "car_id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "plate_number" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "status" INTEGER NOT NULL DEFAULT 1,
    "phone" TEXT,

    CONSTRAINT "cars_pkey" PRIMARY KEY ("car_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cars_plate_number_key" ON "public"."cars"("plate_number");
