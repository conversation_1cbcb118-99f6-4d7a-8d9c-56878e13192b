model Banner {
  id          Int       @id @default(autoincrement()) @map("banner_id")
  title       String
  imageUrl    String 
  linkType    String    @default("none") // goods, activity, external, none
  linkValue   String?
  platform    String    @default("all")  // all, h5, miniapp, pc
  sort        Int       @default(0)
  status      Boolean   @default(true)
  startTime   DateTime?
  endTime     DateTime?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  position    String?   @default("home") // home, shop, activity

  @@map("banners")
}
