model Car {
    id          Int      @id @default(autoincrement()) @map("car_id")
    name        String
    description String?
    plateNumber String   @unique @map("plate_number")
    createdAt   DateTime @default(now()) @map("created_at")
    updatedAt   DateTime @updatedAt @map("updated_at")
    status      Int      @default(1) // 1 active, 2 inactive
    phone       String?

    shopUserId Int @map("shop_user_id")

    consumeOrder ConsumeOrder[]
    shopUser     ShopUser       @relation(fields: [shopUserId], references: [id])

    @@map("cars")
}
