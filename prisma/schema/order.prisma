model Order {
    id          String      @id @default(cuid()) @map("order_id")
    orderNo     String      @unique @db.VarChar(30) // 订单号
    type        OrderType // 订单类型
    status      OrderStatus // 状态
    amount      Decimal     @db.Decimal(10, 2) // 金额
    bonus       Int         @default(0) // 赠送金额
    payMethod   PayMethod?  @map("pay_method") // 支付方式 
    createdAt   DateTime    @default(now()) @map("created_at")
    updatedAt   DateTime    @updatedAt @map("updated_at")
    completedAt DateTime?   @map("completed_at") // 完成时间  

    // 关联
    rechargeOrder RechargeOrder?
    consumeOrder  ConsumeOrder?

    shopUserId Int? @map("shop_user_id")
    shopId     Int? @map("shop_id")

    reviews Review[]

    @@map("orders")
}

enum OrderType {
    RECHARGE // 充值
    CONSUME // 消费
    REFUND // 退款
    WASH // 洗车
}

enum OrderStatus {
    PENDING // 待支付
    PAID // 已支付
    USING // 正在洗车
    COMPLETED // 完成
    CANCELED // 已取消
}

enum PayMethod {
    WECHAT
    ALIPAY
    BALANCE
    FREE_WASH // 免费洗车
}

model RechargeOrder {
    id         String    @id @default(cuid()) @map("recharge_order_id")
    orderId    String    @unique @map("order_id")
    order      Order     @relation(fields: [orderId], references: [id])
    bonus      Decimal?  @db.Decimal(10, 2) // 赠送金额
    campaign   String?   @db.VarChar(50) // 活动ID
    shopUser   ShopUser? @relation(fields: [shopUserId], references: [id])
    shopUserId Int?      @map("shop_user_id")

    @@map("recharge_orders")
}

model ConsumeOrder {
    id        String  @id @default(cuid()) @map("consume_order_id")
    orderId   String  @unique @map("order_id")
    order     Order   @relation(fields: [orderId], references: [id])
    packageId String? @map("package_id") @db.VarChar(36) // 套餐ID 

    // 关联
    stationId Int      @map("station_id")
    station   Station? @relation(fields: [stationId], references: [id])
    carId     Int      @map("car_id")
    car       Car?     @relation(fields: [carId], references: [id])

    price     Int? // 原价
    discount  Float? // 折扣比例
    payAmount Int? // 实际支付金额

    isReviewed Boolean @default(false) @map("is_reviewed") // 是否已评价 

    @@map("consume_orders")
}
