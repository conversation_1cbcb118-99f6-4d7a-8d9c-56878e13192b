//  id: number;
//   username: string;
//   nickname: string;
//   email: string;
//   phone?: string;
//   avatar?: string;
//   status: 'active' | 'inactive' | 'banned';
//   roles: string[];
//   createdAt: string;
//   updatedAt: string;
//   lastLoginTime?: string;

/// 后台管理员表
model AdminUser {
  /// 主键ID
  id          Int       @id @default(autoincrement()) @map("admin_user_id")
  /// 用户名（唯一，登录用）
  username    String    @unique
  /// 登录密码（加密存储）
  password    String
  /// 管理员邮箱（可选，唯一）
  email       String?   @unique
  /// 角色：super_admin / editor / ops
  roles       String    @default("user")
  /// 状态：1=正常，0=禁用
  status      Int       @default(1)
  /// 最后一次登录时间
  lastLoginAt DateTime? @map("last_login_at")
  /// 创建时间
  createdAt   DateTime  @default(now()) @map("created_at")
  /// 更新时间
  updatedAt   DateTime  @updatedAt @map("updated_at")
  /// 权限列表
  permissions String[]  @default([])

  @@map("admin_users")
}

model ShopUser {
  id              Int              @id @default(autoincrement()) @map("shop_user_id")
  /// 手机号
  phone           String           @unique
  /// 邮箱
  email           String?          @unique
  /// 登录密码
  password        String?
  /// 昵称
  nickname        String?
  /// 头像
  avatar          String?
  /// 性别：0=未知，1=男，2=女
  gender          Int              @default(0)
  /// 生日
  birthday        DateTime?
  /// 余额
  balance         Float            @default(0.00)
  /// 积分
  points          Int              @default(0)
  /// 状态：1=正常，0=禁用
  status          Int              @default(1)
  createdAt       DateTime         @default(now()) @map("created_at")
  updatedAt       DateTime         @updatedAt @map("updated_at")
  /// 最后一次登录时间
  lastLoginAt     DateTime?        @map("last_login_at")
  openId          String?          @unique @map("open_id")
  rechargeOrder   RechargeOrder[]
  cars            Car[]
  reviews         Review[]
  userMemberships UserMembership[]

  membershipLevel MembershipLevel[]

  @@map("shop_users")
}

model UserMembership {
  id             Int       @id @default(autoincrement())
  shopUserId     Int       @map("shop_user_id")
  levelId        Int
  balance        Int       @default(0) // 当前余额
  freeWashRemain Int       @default(0) // 剩余免费洗车次数
  expireAt       DateTime? // 到期时间
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  shopUser ShopUser        @relation(fields: [shopUserId], references: [id])
  level    MembershipLevel @relation(fields: [levelId], references: [id])

  @@map("user_memberships")
}

model MembershipLevel {
  id        Int      @id @default(autoincrement())
  name      String // 等级名称：普通、银卡、金卡、钻石
  discount  Float // 折扣，1=无折扣，0.9=9折
  minAmount Int // 升级门槛（累计充值金额）
  freeWash  Int      @default(0) // 赠送洗车次数
  duration  Int? // 有效期（月），null 表示永久
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  shopUsers       ShopUser[]
  userMemberships UserMembership[]

  @@map("membership_levels")
}
