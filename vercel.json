{"version": 2, "name": "mp-react-nextjs", "alias": ["your-domain.com"], "regions": ["hkg1", "sin1"], "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/admin", "destination": "/dashboard", "permanent": true}], "rewrites": [{"source": "/api/v1/:path*", "destination": "/api/:path*"}]}