import { create } from "zustand";
import { persist } from "zustand/middleware";
import { defHttp } from "@/lib/axios";

// 用户信息接口
export interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  email?: string;
  phone?: string;
  roles: string[];
  permissions: string[];
  lastLoginTime?: string;
}

// 菜单项接口
export interface MenuItem {
  id: number;
  title: string;
  path: string;
  icon?: string;
  component?: string;
  parentId?: number;
  sort: number;
  type: "menu" | "button";
  permission?: string;
  children?: MenuItem[];
}

// 登录参数接口
export interface LoginParams {
  username: string;
  password: string;
  captcha?: string;
  remember?: boolean;
}

// 用户状态接口
interface UserState {
  // 状态
  userInfo: UserInfo | null;
  token: string;
  menus: MenuItem[];
  permissions: string[];
  isLoggedIn: boolean;
  loading: boolean;

  // 方法
  login: (params: LoginParams) => Promise<void>;
  logout: () => void;
  fetchUserInfo: () => Promise<void>;
  fetchMenus: () => Promise<void>;
  updateUserInfo: (userInfo: Partial<UserInfo>) => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  setLoading: (loading: boolean) => void;
  setToken: (token: string | undefined) => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // 初始状态
      userInfo: null,
      token: "",
      menus: [],
      permissions: [],
      isLoggedIn: false,
      loading: false,

      // 登录
      login: async (params: LoginParams) => {
        try {
          set({ loading: true });

          const response = await defHttp.post<{
            token: string;
            userInfo: UserInfo;
          }>({ url: "/auth/login", params });

          console.log("response", response);
          const { token, userInfo } = response;

          // 存储token到localStorage
          if (typeof window !== "undefined") {
            localStorage.setItem("token", token);
          }

          set({
            token,
            userInfo,
            isLoggedIn: true,
            loading: false,
            permissions: userInfo.permissions || [],
          });

          // 获取菜单和权限
          await get().fetchMenus();
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },

      // 登出
      logout: () => {
        // 清除本地存储
        if (typeof window !== "undefined") {
          localStorage.removeItem("token");
          localStorage.removeItem("userInfo");
        }

        // 重置状态
        set({
          userInfo: null,
          token: "",
          menus: [],
          permissions: [],
          isLoggedIn: false,
          loading: false,
        });

        // 跳转到登录页
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      },

      // 获取用户信息
      fetchUserInfo: async () => {
        try {
          const response = await defHttp.get<UserInfo>({
            url: "/auth/userinfo",
          });
          const userInfo = response;

          set({
            userInfo,
            permissions: userInfo.permissions || [],
          });
        } catch (error) {
          console.error("获取用户信息失败:", error);
          get().logout();
        }
      },

      // 获取菜单
      fetchMenus: async () => {
        try {
          const response = await defHttp.get<MenuItem[]>({
            url: "/auth/menus",
          });
          const menus = response;

          set({ menus });
        } catch (error) {
          console.error("获取菜单失败:", error);
        }
      },

      // 更新用户信息
      updateUserInfo: (newUserInfo: Partial<UserInfo>) => {
        const { userInfo } = get();
        if (userInfo) {
          const updatedUserInfo = { ...userInfo, ...newUserInfo };
          set({ userInfo: updatedUserInfo });
        }
      },

      // 检查权限
      hasPermission: (permission: string) => {
        const { permissions } = get();
        console.log("permissions", permissions, "permission", permission);
        if (permissions.includes("*")) return true;
        return permissions.includes(permission) || permissions.includes("*");
      },

      // 检查角色
      hasRole: (role: string) => {
        const { userInfo } = get();
        return (
          userInfo?.roles?.includes(role) ||
          userInfo?.roles?.includes("admin") ||
          false
        );
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },
      //  设置token
      setToken: (token: string | undefined) => {
        set({ token });
      },
    }),
    {
      name: "user-store",
      // 只持久化部分状态
      partialize: (state) => ({
        userInfo: state.userInfo,
        token: state.token,
        isLoggedIn: state.isLoggedIn,
      }),
    }
  )
);

// 导出常用的选择器
export const useUser = () => useUserStore((state) => state.userInfo);
export const useToken = () => useUserStore((state) => state.token);
export const useMenus = () => useUserStore((state) => state.menus);
export const usePermissions = () => useUserStore((state) => state.permissions);
export const useIsLoggedIn = () => useUserStore((state) => state.isLoggedIn);
export const useUserLoading = () => useUserStore((state) => state.loading);
