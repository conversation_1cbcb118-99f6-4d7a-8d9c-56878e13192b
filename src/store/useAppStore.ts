import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { theme } from 'antd';

// 主题模式
export type ThemeMode = 'light' | 'dark' | 'auto';

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// 布局模式
export type LayoutMode = 'side' | 'top' | 'mix';

// 系统配置接口
export interface AppConfig {
  // 主题配置
  themeMode: ThemeMode;
  primaryColor: string;
  borderRadius: number;
  
  // 布局配置
  layoutMode: LayoutMode;
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  headerHeight: number;
  
  // 功能配置
  showBreadcrumb: boolean;
  showTabs: boolean;
  showFooter: boolean;
  enableWatermark: boolean;
  
  // 语言配置
  language: Language;
  
  // 其他配置
  pageSize: number;
  animationEnabled: boolean;
}

// 应用状态接口
interface AppState extends AppConfig {
  // 运行时状态
  loading: boolean;
  sidebarVisible: boolean; // 移动端侧边栏显示状态
  
  // 方法
  setThemeMode: (mode: ThemeMode) => void;
  setPrimaryColor: (color: string) => void;
  setBorderRadius: (radius: number) => void;
  setLayoutMode: (mode: LayoutMode) => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setSidebarVisible: (visible: boolean) => void;
  setLanguage: (language: Language) => void;
  updateConfig: (config: Partial<AppConfig>) => void;
  resetConfig: () => void;
  setLoading: (loading: boolean) => void;
}

// 默认配置
const defaultConfig: AppConfig = {
  themeMode: 'light',
  primaryColor: '#1890ff',
  borderRadius: 6,
  layoutMode: 'side',
  sidebarCollapsed: false,
  sidebarWidth: 240,
  headerHeight: 64,
  showBreadcrumb: true,
  showTabs: true,
  showFooter: true,
  enableWatermark: false,
  language: 'zh-CN',
  pageSize: 20,
  animationEnabled: true,
};

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      ...defaultConfig,
      loading: false,
      sidebarVisible: false,

      // 设置主题模式
      setThemeMode: (mode: ThemeMode) => {
        set({ themeMode: mode });
      },

      // 设置主色调
      setPrimaryColor: (color: string) => {
        set({ primaryColor: color });
      },

      // 设置圆角大小
      setBorderRadius: (radius: number) => {
        set({ borderRadius: radius });
      },

      // 设置布局模式
      setLayoutMode: (mode: LayoutMode) => {
        set({ layoutMode: mode });
      },

      // 切换侧边栏折叠状态
      toggleSidebar: () => {
        const { sidebarCollapsed } = get();
        set({ sidebarCollapsed: !sidebarCollapsed });
      },

      // 设置侧边栏折叠状态
      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed });
      },

      // 设置移动端侧边栏显示状态
      setSidebarVisible: (visible: boolean) => {
        set({ sidebarVisible: visible });
      },

      // 设置语言
      setLanguage: (language: Language) => {
        set({ language });
      },

      // 更新配置
      updateConfig: (config: Partial<AppConfig>) => {
        set((state) => ({ ...state, ...config }));
      },

      // 重置配置
      resetConfig: () => {
        set({ ...defaultConfig });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },
    }),
    {
      name: 'app-store',
      // 排除运行时状态
      partialize: (state) => {
        const { loading, sidebarVisible, ...persistedState } = state;
        return persistedState;
      },
    }
  )
);

// 导出常用的选择器
export const useThemeMode = () => useAppStore((state) => state.themeMode);
export const usePrimaryColor = () => useAppStore((state) => state.primaryColor);
export const useBorderRadius = () => useAppStore((state) => state.borderRadius);
export const useLayoutMode = () => useAppStore((state) => state.layoutMode);
export const useSidebarCollapsed = () => useAppStore((state) => state.sidebarCollapsed);
export const useSidebarVisible = () => useAppStore((state) => state.sidebarVisible);
export const useLanguage = () => useAppStore((state) => state.language);
export const useAppLoading = () => useAppStore((state) => state.loading);

// 获取Ant Design主题配置
export const useAntdTheme = () => {
  const primaryColor = usePrimaryColor();
  const borderRadius = useBorderRadius();
  const themeMode = useThemeMode();
  
  return {
    algorithm: themeMode === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: primaryColor,
      borderRadius: borderRadius,
    },
  };
};
