import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { ThemeConfig } from 'antd';
import { theme } from 'antd';
import {
  lightTheme,
  darkTheme,
  primaryColors,
  themeUtils,
  defaultThemeConfig,
  type ThemeMode
} from '@/config/theme';

// 主题状态接口
interface ThemeState {
  // 主题模式
  mode: ThemeMode;

  // 主色调
  primaryColor: string;

  // 圆角大小
  borderRadius: number;

  // 紧凑模式
  compactMode: boolean;

  // 当前实际主题
  actualTheme: 'light' | 'dark';

  // Ant Design 主题配置
  themeConfig: ThemeConfig;

  // 动作
  setMode: (mode: ThemeMode) => void;
  setPrimaryColor: (color: string) => void;
  setBorderRadius: (radius: number) => void;
  setCompactMode: (compact: boolean) => void;
  resetTheme: () => void;
  updateThemeConfig: () => void;
}

// 生成主题配置
const generateThemeConfig = (
  actualTheme: 'light' | 'dark',
  primaryColor: string,
  borderRadius: number,
  compactMode: boolean
): ThemeConfig => {
  const baseTheme = actualTheme === 'dark' ? darkTheme : lightTheme;

  return {
    ...baseTheme,
    token: {
      ...baseTheme.token,
      colorPrimary: primaryColor,
      colorInfo: primaryColor,
      borderRadius,
      borderRadiusLG: borderRadius + 2,
      borderRadiusSM: Math.max(borderRadius - 2, 2),
      borderRadiusXS: Math.max(borderRadius - 4, 1),
      ...(compactMode && {
        padding: 12,
        paddingLG: 16,
        paddingSM: 8,
        paddingXS: 4,
        paddingXXS: 2,
        margin: 12,
        marginLG: 16,
        marginSM: 8,
        marginXS: 4,
        marginXXS: 2,
        controlHeight: 28,
        controlHeightLG: 36,
        controlHeightSM: 20,
      }),
    },
    components: {
      ...baseTheme.components,
      ...(compactMode && {
        Button: {
          ...baseTheme.components?.Button,
          controlHeight: 28,
          controlHeightLG: 36,
          controlHeightSM: 20,
          borderRadius,
        },
        Input: {
          ...baseTheme.components?.Input,
          controlHeight: 28,
          controlHeightLG: 36,
          controlHeightSM: 20,
          borderRadius,
        },
        Select: {
          controlHeight: 28,
          controlHeightLG: 36,
          controlHeightSM: 20,
          borderRadius,
        },
        DatePicker: {
          controlHeight: 28,
          controlHeightLG: 36,
          controlHeightSM: 20,
          borderRadius,
        },
        Table: {
          ...baseTheme.components?.Table,
          padding: 8,
          paddingSM: 4,
        },
        Card: {
          ...baseTheme.components?.Card,
          paddingLG: 16,
        },
      }),
    },
    algorithm: actualTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
  };
};

// 创建主题状态管理
export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => {
      // 初始化实际主题
      const initialActualTheme = themeUtils.getActualTheme(defaultThemeConfig.mode);

      return {
        // 初始状态
        mode: defaultThemeConfig.mode,
        primaryColor: defaultThemeConfig.primaryColor,
        borderRadius: defaultThemeConfig.borderRadius,
        compactMode: defaultThemeConfig.compactMode,
        actualTheme: initialActualTheme,
        themeConfig: generateThemeConfig(
          initialActualTheme,
          defaultThemeConfig.primaryColor,
          defaultThemeConfig.borderRadius,
          defaultThemeConfig.compactMode
        ),

        // 设置主题模式
        setMode: (mode: ThemeMode) => {
          const actualTheme = themeUtils.getActualTheme(mode);
          const state = get();

          set({
            mode,
            actualTheme,
            themeConfig: generateThemeConfig(
              actualTheme,
              state.primaryColor,
              state.borderRadius,
              state.compactMode
            ),
          });
        },

        // 设置主色调
        setPrimaryColor: (color: string) => {
          const state = get();

          set({
            primaryColor: color,
            themeConfig: generateThemeConfig(
              state.actualTheme,
              color,
              state.borderRadius,
              state.compactMode
            ),
          });
        },

        // 设置圆角大小
        setBorderRadius: (radius: number) => {
          const state = get();

          set({
            borderRadius: radius,
            themeConfig: generateThemeConfig(
              state.actualTheme,
              state.primaryColor,
              radius,
              state.compactMode
            ),
          });
        },

        // 设置紧凑模式
        setCompactMode: (compact: boolean) => {
          const state = get();

          set({
            compactMode: compact,
            themeConfig: generateThemeConfig(
              state.actualTheme,
              state.primaryColor,
              state.borderRadius,
              compact
            ),
          });
        },

        // 重置主题
        resetTheme: () => {
          const actualTheme = themeUtils.getActualTheme(defaultThemeConfig.mode);

          set({
            mode: defaultThemeConfig.mode,
            primaryColor: defaultThemeConfig.primaryColor,
            borderRadius: defaultThemeConfig.borderRadius,
            compactMode: defaultThemeConfig.compactMode,
            actualTheme,
            themeConfig: generateThemeConfig(
              actualTheme,
              defaultThemeConfig.primaryColor,
              defaultThemeConfig.borderRadius,
              defaultThemeConfig.compactMode
            ),
          });
        },

        // 更新主题配置
        updateThemeConfig: () => {
          const state = get();
          const actualTheme = themeUtils.getActualTheme(state.mode);

          set({
            actualTheme,
            themeConfig: generateThemeConfig(
              actualTheme,
              state.primaryColor,
              state.borderRadius,
              state.compactMode
            ),
          });
        },
      };
    },
    {
      name: 'theme-storage',
      partialize: (state) => ({
        mode: state.mode,
        primaryColor: state.primaryColor,
        borderRadius: state.borderRadius,
        compactMode: state.compactMode,
      }),
    }
  )
);

// 主题工具 Hook
export const useTheme = () => {
  const store = useThemeStore();

  return {
    ...store,

    // 获取主色调选项
    getPrimaryColorOptions: () => {
      return Object.entries(primaryColors).map(([name, color]) => ({
        name,
        color,
        label: name.charAt(0).toUpperCase() + name.slice(1),
      }));
    },

    // 检查是否为深色主题
    isDark: store.actualTheme === 'dark',

    // 检查是否为浅色主题
    isLight: store.actualTheme === 'light',

    // 切换主题模式
    toggleMode: () => {
      const newMode = store.mode === 'light' ? 'dark' : 'light';
      store.setMode(newMode);
    },

    // 切换紧凑模式
    toggleCompactMode: () => {
      store.setCompactMode(!store.compactMode);
    },
  };
};

// 系统主题监听 Hook
export const useSystemThemeWatcher = () => {
  const { mode, updateThemeConfig } = useThemeStore();

  React.useEffect(() => {
    if (mode !== 'auto') return;

    const unwatch = themeUtils.watchSystemTheme(() => {
      updateThemeConfig();
    });

    return unwatch;
  }, [mode, updateThemeConfig]);
};

// 导入 React（用于 useEffect）
import React from 'react';
