// 性能监控工具

import React from 'react';

// 性能指标类型
export interface PerformanceMetrics {
  // 页面加载性能
  pageLoad: {
    fcp: number;      // First Contentful Paint
    lcp: number;      // Largest Contentful Paint
    fid: number;      // First Input Delay
    cls: number;      // Cumulative Layout Shift
    ttfb: number;     // Time to First Byte
  };

  // 资源加载性能
  resources: {
    totalSize: number;
    loadTime: number;
    cacheHitRate: number;
  };

  // 运行时性能
  runtime: {
    memoryUsage: number;
    renderTime: number;
    apiResponseTime: number;
  };
}

// 性能监控类
class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initObservers();
  }

  // 初始化性能观察器
  private initObservers() {
    if (typeof window === 'undefined') return;

    // 监控 Web Vitals
    this.observeWebVitals();

    // 监控资源加载
    this.observeResources();

    // 监控长任务
    this.observeLongTasks();
  }

  // 监控 Web Vitals
  private observeWebVitals() {
    // FCP (First Contentful Paint)
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcp) {
        this.updateMetric('pageLoad.fcp', fcp.startTime);
      }
    });
    fcpObserver.observe({ entryTypes: ['paint'] });
    this.observers.push(fcpObserver);

    // LCP (Largest Contentful Paint)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.updateMetric('pageLoad.lcp', lastEntry.startTime);
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.push(lcpObserver);

    // FID (First Input Delay)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        this.updateMetric('pageLoad.fid', entry.processingStart - entry.startTime);
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });
    this.observers.push(fidObserver);

    // CLS (Cumulative Layout Shift)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          this.updateMetric('pageLoad.cls', clsValue);
        }
      });
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });
    this.observers.push(clsObserver);
  }

  // 监控资源加载
  private observeResources() {
    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      let totalSize = 0;
      let totalLoadTime = 0;
      let cacheHits = 0;

      entries.forEach((entry: any) => {
        if (entry.transferSize !== undefined) {
          totalSize += entry.transferSize;
          totalLoadTime += entry.duration;

          // 检查是否从缓存加载
          if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
            cacheHits++;
          }
        }
      });

      this.updateMetric('resources.totalSize', totalSize);
      this.updateMetric('resources.loadTime', totalLoadTime);
      this.updateMetric('resources.cacheHitRate', cacheHits / entries.length);
    });

    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);
  }

  // 监控长任务
  private observeLongTasks() {
    const longTaskObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        console.warn(`Long task detected: ${entry.duration}ms`, entry);
      });
    });

    longTaskObserver.observe({ entryTypes: ['longtask'] });
    this.observers.push(longTaskObserver);
  }

  // 更新指标
  private updateMetric(path: string, value: number) {
    const keys = path.split('.');
    let current = this.metrics as any;

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
  }

  // 获取性能指标
  getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  // 获取内存使用情况
  getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
      };
    }
    return null;
  }

  // 测量函数执行时间
  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  // 测量异步函数执行时间
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();

    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  // 清理观察器
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 缓存管理工具
export class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  // 设置缓存
  set(key: string, data: any, ttl: number = 5 * 60 * 1000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  // 获取缓存
  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  // 删除缓存
  delete(key: string) {
    this.cache.delete(key);
  }

  // 清空缓存
  clear() {
    this.cache.clear();
  }

  // 获取缓存大小
  size() {
    return this.cache.size;
  }

  // 清理过期缓存
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 创建全局缓存管理实例
export const cacheManager = new CacheManager();

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 图片懒加载工具
export class LazyImageLoader {
  private observer: IntersectionObserver | null = null;

  constructor() {
    this.initObserver();
  }

  private initObserver() {
    if (typeof window === 'undefined') return;

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;

            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              this.observer?.unobserve(img);
            }
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );
  }

  // 观察图片元素
  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.observe(img);
    }
  }

  // 停止观察
  unobserve(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.unobserve(img);
    }
  }

  // 清理观察器
  cleanup() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}

// 创建全局图片懒加载实例
export const lazyImageLoader = new LazyImageLoader();

// 代码分割工具
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const LazyComponent = React.lazy(importFunc);

  return (props: React.ComponentProps<T>) => {
    const fallbackElement = fallback ? React.createElement(fallback) : React.createElement('div', {}, 'Loading...');

    return React.createElement(
      React.Suspense,
      { fallback: fallbackElement },
      React.createElement(LazyComponent, props)
    );
  };
}


