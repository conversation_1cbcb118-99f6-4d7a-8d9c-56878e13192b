
import redis from "@/lib/redis";

// 生成订单号
async function generateOrderNo(prefix: string) {
  const date = new Date();
  const today = `${date.getFullYear()}${(date.getMonth() + 1)
    .toString()
    .padStart(2, "0")}${date.getDate().toString().padStart(2, "0")}`;
  const key = `order:${prefix}:${today}`;
  const seq = await redis.incr(key);
  await redis.expire(key, 86400);
  return `${prefix}${today}${seq.toString().padStart(6, "0")}`;
}
export {generateOrderNo}