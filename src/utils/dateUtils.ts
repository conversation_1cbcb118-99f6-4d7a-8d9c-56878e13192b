import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

export default dayjs;

// 默认时区（中国北京时间）
const DEFAULT_TZ = "Asia/Shanghai";

/**
 * 格式化日期为北京时间显示
 * @param date - Date 对象或日期字符串
 * @param format - 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的北京时间字符串
 */
export const formatBeijingTime = (
  date: Date | string | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  if (!date) return '';
  
  return dayjs(date).tz(DEFAULT_TZ).format(format);
};

/**
 * 格式化日期为北京时间显示（相对时间）
 * @param date - Date 对象或日期字符串
 * @returns 相对时间字符串，如 "2小时前"
 */
export const formatBeijingTimeRelative = (
  date: Date | string | null | undefined
): string => {
  if (!date) return '';
  
  return dayjs(date).tz(DEFAULT_TZ).fromNow();
};

/**
 * 获取北京时间的 dayjs 对象
 * @param date - Date 对象或日期字符串，默认为当前时间
 * @returns dayjs 对象，已设置为北京时区
 */
export const getBeijingTime = (date?: Date | string) => {
  return date ? dayjs(date).tz(DEFAULT_TZ) : dayjs().tz(DEFAULT_TZ);
};

/**
 * 将北京时间字符串转换为 Date 对象
 * @param dateString - 北京时间字符串，如 '2024-01-01 20:00:00'
 * @returns Date 对象
 */
export const parseBeijingTime = (dateString: string): Date => {
  return dayjs.tz(dateString, DEFAULT_TZ).toDate();
};

/**
 * 常用的日期格式化预设
 */
export const DateFormats = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATETIME_SHORT: 'MM-DD HH:mm',
  YEAR_MONTH: 'YYYY-MM',
  MONTH_DAY: 'MM-DD',
} as const;

/**
 * 示例用法：
 * 
 * // 在组件中使用
 * import { formatBeijingTime, DateFormats } from '@/lib/date-utils';
 * 
 * const MyComponent = ({ createdAt }: { createdAt: Date }) => {
 *   return (
 *     <div>
 *       <p>创建时间: {formatBeijingTime(createdAt)}</p>
 *       <p>创建日期: {formatBeijingTime(createdAt, DateFormats.DATE)}</p>
 *       <p>相对时间: {formatBeijingTimeRelative(createdAt)}</p>
 *     </div>
 *   );
 * };
 */
