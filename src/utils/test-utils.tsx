// Test utilities for React Testing Library
import React, { ReactElement } from 'react'
import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { useUserStore } from '@/store/useUserStore'
import { useAppStore } from '@/store/useAppStore'
import { useThemeStore } from '@/store/useThemeStore'

// Mock stores initial state
const mockUserState = {
  userInfo: {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '/avatar.jpg',
    permissions: ['read', 'write', 'delete'],
  },
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  updateUserInfo: jest.fn(),
}

const mockAppState = {
  sidebarCollapsed: false,
  toggleSidebar: jest.fn(),
  setSidebarCollapsed: jest.fn(),
}

const mockThemeState = {
  theme: 'light' as const,
  primaryColor: '#1890ff',
  toggleTheme: jest.fn(),
  setPrimaryColor: jest.fn(),
}

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode
  userState?: Partial<typeof mockUserState>
  appState?: Partial<typeof mockAppState>
  themeState?: Partial<typeof mockThemeState>
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  userState = {},
  appState = {},
  themeState = {},
}) => {
  // Mock store states
  React.useEffect(() => {
    // Mock useUserStore
    jest.mocked(useUserStore).mockReturnValue({
      ...mockUserState,
      ...userState,
    } as any)

    // Mock useAppStore
    jest.mocked(useAppStore).mockReturnValue({
      ...mockAppState,
      ...appState,
    } as any)

    // Mock useThemeStore
    jest.mocked(useThemeStore).mockReturnValue({
      ...mockThemeState,
      ...themeState,
    } as any)
  }, [userState, appState, themeState])

  return (
    <ConfigProvider locale={zhCN}>
      {children}
    </ConfigProvider>
  )
}

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  userState?: Partial<typeof mockUserState>
  appState?: Partial<typeof mockAppState>
  themeState?: Partial<typeof mockThemeState>
}

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const { userState, appState, themeState, ...renderOptions } = options

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper
      userState={userState}
      appState={appState}
      themeState={themeState}
    >
      {children}
    </TestWrapper>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock API functions
export const mockApi = {
  // Mock successful API response
  success: (data: any) => ({
    success: true,
    data,
    message: 'Success',
    code: 200,
  }),

  // Mock error API response
  error: (message = 'Error', code = 400) => ({
    success: false,
    data: null,
    message,
    code,
  }),

  // Mock paginated response
  paginated: (data: any[], page = 1, pageSize = 10, total?: number) => ({
    success: true,
    data: {
      list: data,
      pagination: {
        current: page,
        pageSize,
        total: total || data.length,
        showSizeChanger: true,
        showQuickJumper: true,
      },
    },
    message: 'Success',
    code: 200,
  }),
}

// Mock data generators
export const mockData = {
  // Generate mock user
  user: (overrides: any = {}) => ({
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '/avatar.jpg',
    status: 'active',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    ...overrides,
  }),

  // Generate mock users list
  users: (count = 5) => 
    Array.from({ length: count }, (_, i) => 
      mockData.user({
        id: String(i + 1),
        username: `user${i + 1}`,
        email: `user${i + 1}@example.com`,
      })
    ),

  // Generate mock role
  role: (overrides: any = {}) => ({
    id: '1',
    name: 'admin',
    description: 'Administrator role',
    permissions: ['read', 'write', 'delete'],
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    ...overrides,
  }),

  // Generate mock roles list
  roles: (count = 3) =>
    Array.from({ length: count }, (_, i) =>
      mockData.role({
        id: String(i + 1),
        name: ['admin', 'editor', 'viewer'][i] || `role${i + 1}`,
        description: `Role ${i + 1} description`,
      })
    ),

  // Generate mock article
  article: (overrides: any = {}) => ({
    id: '1',
    title: 'Test Article',
    content: 'This is a test article content.',
    author: 'Test Author',
    status: 'published',
    categoryId: '1',
    tags: ['test', 'article'],
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    ...overrides,
  }),

  // Generate mock articles list
  articles: (count = 5) =>
    Array.from({ length: count }, (_, i) =>
      mockData.article({
        id: String(i + 1),
        title: `Test Article ${i + 1}`,
        author: `Author ${i + 1}`,
      })
    ),

  // Generate mock category
  category: (overrides: any = {}) => ({
    id: '1',
    name: 'Test Category',
    description: 'Test category description',
    parentId: null,
    sort: 1,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    ...overrides,
  }),

  // Generate mock categories list
  categories: (count = 3) =>
    Array.from({ length: count }, (_, i) =>
      mockData.category({
        id: String(i + 1),
        name: `Category ${i + 1}`,
        description: `Category ${i + 1} description`,
      })
    ),
}

// Test helpers
export const testHelpers = {
  // Wait for element to appear
  waitForElement: async (getByTestId: any, testId: string, timeout = 1000) => {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Element with testId "${testId}" not found within ${timeout}ms`))
      }, timeout)

      const checkElement = () => {
        try {
          const element = getByTestId(testId)
          clearTimeout(timer)
          resolve(element)
        } catch {
          setTimeout(checkElement, 100)
        }
      }

      checkElement()
    })
  },

  // Simulate async operation
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock console methods
  mockConsole: () => {
    const originalConsole = { ...console }
    const mockMethods = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
    }

    Object.assign(console, mockMethods)

    return {
      restore: () => Object.assign(console, originalConsole),
      mocks: mockMethods,
    }
  },

  // Create mock file
  createMockFile: (name = 'test.txt', size = 1024, type = 'text/plain') => {
    const file = new File(['test content'], name, { type })
    Object.defineProperty(file, 'size', { value: size })
    return file
  },
}

// Re-export everything from React Testing Library
export * from '@testing-library/react'
export { customRender as render }

// Export default render for convenience
export default customRender
