export const getGeo = async (
  address: string
): Promise<{ longitude: string | null; latitude: string | null }> => {
  try {
    const params = new URLSearchParams({
      key: process.env.AMAP_KEY as string,
      address,
    });

    const url = `https://restapi.amap.com/v3/geocode/geo?${params.toString()}`;

    const response = await fetch(url);

    if (!response.ok) {
      console.error(
        "请求高德地图接口失败:",
        response.status,
        response.statusText
      );
      return { longitude: null, latitude: null };
    }

    const data = await response.json();

    if (data.status === "1" && data.geocodes?.length > 0) {
      const location = data.geocodes[0].location;
      const [longitude, latitude] = location.split(",");
      return { longitude, latitude };
    }

    return { longitude: null, latitude: null };
  } catch (error) {
    console.error("获取地理位置失败:", error);
    return { longitude: null, latitude: null };
  }
};
