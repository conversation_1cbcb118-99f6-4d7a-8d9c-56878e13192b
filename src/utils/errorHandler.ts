// 全局错误处理系统

import { message, notification } from "antd";

// 错误类型枚举
export enum ErrorType {
  NETWORK = "NETWORK",
  VALIDATION = "VALIDATION",
  AUTHENTICATION = "AUTHENTICATION",
  AUTHORIZATION = "AUTHORIZATION",
  SERVER = "SERVER",
  CLIENT = "CLIENT",
  UNKNOWN = "UNKNOWN",
}

// 错误级别枚举
export enum ErrorLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType;
  level: ErrorLevel;
  message: string;
  code?: string | number;
  details?: any;
  timestamp: number;
  url?: string;
  userId?: string;
  stack?: string;
  serverInfo?: {
    userAgent: string;
    ip: string;
    referer: string;
  };
}

// 错误处理配置
export interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableReporting: boolean;
  enableNotification: boolean;
  maxRetries: number;
  retryDelay: number;
  reportUrl?: string;
}

// 默认配置
const defaultConfig: ErrorHandlerConfig = {
  enableLogging: true,
  enableReporting: process.env.NODE_ENV === "production",
  enableNotification: true,
  maxRetries: 3,
  retryDelay: 1000,
  reportUrl: "/api/errors",
};

// 错误处理器类
class ErrorHandler {
  private config: ErrorHandlerConfig;
  private errorQueue: ErrorInfo[] = [];
  private retryCount = new Map<string, number>();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.setupGlobalHandlers();
  }

  // 设置全局错误处理器
  private setupGlobalHandlers() {
    // 捕获未处理的Promise拒绝
    if (typeof window !== "undefined") {
      window.addEventListener("unhandledrejection", (event) => {
        this.handleError({
          type: ErrorType.CLIENT,
          level: ErrorLevel.HIGH,
          message: event.reason?.message || "Unhandled Promise Rejection",
          details: event.reason,
          timestamp: Date.now(),
          url: window.location.href,
        });
      });

      // 捕获JavaScript错误
      window.addEventListener("error", (event) => {
        this.handleError({
          type: ErrorType.CLIENT,
          level: ErrorLevel.HIGH,
          message: event.message,
          details: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
          },
          timestamp: Date.now(),
          url: window.location.href,
          stack: event.error?.stack,
        });
      });
    }
  }

  // 处理错误
  handleError(errorInfo: Partial<ErrorInfo>): void {
    const error: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      level: ErrorLevel.MEDIUM,
      message: "Unknown error occurred",
      timestamp: Date.now(),
      ...errorInfo,
    };

    // 记录错误
    if (this.config.enableLogging) {
      this.logError(error);
    }

    // 显示通知
    if (this.config.enableNotification) {
      this.showNotification(error);
    }

    // 上报错误
    if (this.config.enableReporting) {
      this.reportError(error);
    }

    // 添加到错误队列
    this.errorQueue.push(error);

    // 保持队列大小
    if (this.errorQueue.length > 100) {
      this.errorQueue.shift();
    }
  }

  // 记录错误
  private logError(error: ErrorInfo): void {
    const logLevel = this.getLogLevel(error.level);
    const logMessage = `[${error.type}] ${error.message}`;

    console[logLevel](logMessage, {
      code: error.code,
      details: error.details,
      timestamp: new Date(error.timestamp).toISOString(),
      url: error.url,
      stack: error.stack,
    });
  }

  // 获取日志级别
  private getLogLevel(level: ErrorLevel): "log" | "warn" | "error" {
    switch (level) {
      case ErrorLevel.LOW:
        return "log";
      case ErrorLevel.MEDIUM:
        return "warn";
      case ErrorLevel.HIGH:
      case ErrorLevel.CRITICAL:
        return "error";
      default:
        return "log";
    }
  }

  // 显示通知
  private showNotification(error: ErrorInfo): void {
    const userMessage = this.getUserFriendlyMessage(error);

    switch (error.level) {
      case ErrorLevel.LOW:
        message.info(userMessage);
        break;
      case ErrorLevel.MEDIUM:
        message.warning(userMessage);
        break;
      case ErrorLevel.HIGH:
        message.error(userMessage);
        break;
      case ErrorLevel.CRITICAL:
        notification.error({
          message: "严重错误",
          description: userMessage,
          duration: 0, // 不自动关闭
        });
        break;
    }
  }

  // 获取用户友好的错误消息
  private getUserFriendlyMessage(error: ErrorInfo): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return "网络连接失败，请检查网络设置";
      case ErrorType.AUTHENTICATION:
        return "登录已过期，请重新登录";
      case ErrorType.AUTHORIZATION:
        return "您没有权限执行此操作";
      case ErrorType.VALIDATION:
        return error.message || "输入数据格式不正确";
      case ErrorType.SERVER:
        return "服务器错误，请稍后重试";
      default:
        return error.message || "操作失败，请稍后重试";
    }
  }

  // 上报错误
  private async reportError(error: ErrorInfo): Promise<void> {
    if (!this.config.reportUrl) return;

    const errorKey = `${error.type}-${error.message}`;
    const currentRetries = this.retryCount.get(errorKey) || 0;

    if (currentRetries >= this.config.maxRetries) {
      return;
    }

    try {
      await fetch(this.config.reportUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(error),
      });

      // 重置重试计数
      this.retryCount.delete(errorKey);
    } catch (reportError) {
      // 增加重试计数
      this.retryCount.set(errorKey, currentRetries + 1);

      // 延迟重试
      setTimeout(() => {
        this.reportError(error);
      }, this.config.retryDelay * Math.pow(2, currentRetries));
    }
  }

  // 获取错误历史
  getErrorHistory(): ErrorInfo[] {
    return [...this.errorQueue];
  }

  // 清空错误历史
  clearErrorHistory(): void {
    this.errorQueue = [];
  }

  // 更新配置
  updateConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler();

// 便捷的错误处理函数
export const handleError = (error: any, context?: string): void => {
  let errorInfo: Partial<ErrorInfo> = {
    timestamp: Date.now(),
    url: typeof window !== "undefined" ? window.location.href : undefined,
  };

  if (error instanceof Error) {
    errorInfo = {
      ...errorInfo,
      message: error.message,
      stack: error.stack,
      type: ErrorType.CLIENT,
      level: ErrorLevel.MEDIUM,
    };
  } else if (typeof error === "string") {
    errorInfo = {
      ...errorInfo,
      message: error,
      type: ErrorType.CLIENT,
      level: ErrorLevel.MEDIUM,
    };
  } else if (error?.response) {
    // HTTP错误
    const status = error.response.status;
    errorInfo = {
      ...errorInfo,
      message: error.response.data?.message || error.message || "HTTP Error",
      code: status,
      details: error.response.data,
      type: status >= 500 ? ErrorType.SERVER : ErrorType.CLIENT,
      level: status >= 500 ? ErrorLevel.HIGH : ErrorLevel.MEDIUM,
    };

    // 特殊状态码处理
    if (status === 401) {
      errorInfo.type = ErrorType.AUTHENTICATION;
    } else if (status === 403) {
      errorInfo.type = ErrorType.AUTHORIZATION;
    } else if (status === 422) {
      errorInfo.type = ErrorType.VALIDATION;
    }
  } else {
    errorInfo = {
      ...errorInfo,
      message: "Unknown error occurred",
      details: error,
      type: ErrorType.UNKNOWN,
      level: ErrorLevel.MEDIUM,
    };
  }

  if (context) {
    errorInfo.message = `[${context}] ${errorInfo.message}`;
  }

  errorHandler.handleError(errorInfo);
};

// 网络错误处理
export const handleNetworkError = (error: any): void => {
  handleError({
    ...error,
    type: ErrorType.NETWORK,
    level: ErrorLevel.HIGH,
    message: "网络连接失败",
  });
};

// 验证错误处理
export const handleValidationError = (message: string, details?: any): void => {
  handleError({
    type: ErrorType.VALIDATION,
    level: ErrorLevel.MEDIUM,
    message,
    details,
  });
};

// 认证错误处理
export const handleAuthError = (message?: string): void => {
  handleError({
    type: ErrorType.AUTHENTICATION,
    level: ErrorLevel.HIGH,
    message: message || "认证失败",
  });
};

// 权限错误处理
export const handleAuthorizationError = (message?: string): void => {
  handleError({
    type: ErrorType.AUTHORIZATION,
    level: ErrorLevel.HIGH,
    message: message || "权限不足",
  });
};

// 服务器错误处理
export const handleServerError = (error: any): void => {
  handleError({
    ...error,
    type: ErrorType.SERVER,
    level: ErrorLevel.HIGH,
  });
};
