import { 
  errorHandler, 
  handleError, 
  handleNetworkError, 
  handleValidationError, 
  handleAuthError, 
  handleServerError,
  ErrorType,
  ErrorLevel,
} from '@/utils/errorHandler'

// Mock Ant Design message and notification
jest.mock('antd', () => ({
  message: {
    info: jest.fn(),
    warning: jest.fn(),
    error: jest.fn(),
  },
  notification: {
    error: jest.fn(),
  },
}))

// Mock fetch for error reporting
global.fetch = jest.fn()

describe('Error Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    errorHandler.clearErrorHistory()
    
    // Reset fetch mock
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true }),
    })
  })

  describe('errorHandler', () => {
    it('should handle basic error correctly', () => {
      const error = {
        type: ErrorType.CLIENT,
        level: ErrorLevel.MEDIUM,
        message: 'Test error',
        timestamp: Date.now(),
      }

      errorHandler.handleError(error)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject(error)
    })

    it('should limit error history size', () => {
      // Add more than 100 errors
      for (let i = 0; i < 105; i++) {
        errorHandler.handleError({
          type: ErrorType.CLIENT,
          level: ErrorLevel.LOW,
          message: `Error ${i}`,
          timestamp: Date.now(),
        })
      }

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(100)
    })

    it('should clear error history', () => {
      errorHandler.handleError({
        type: ErrorType.CLIENT,
        level: ErrorLevel.MEDIUM,
        message: 'Test error',
        timestamp: Date.now(),
      })

      expect(errorHandler.getErrorHistory()).toHaveLength(1)

      errorHandler.clearErrorHistory()
      expect(errorHandler.getErrorHistory()).toHaveLength(0)
    })

    it('should update configuration', () => {
      errorHandler.updateConfig({
        enableLogging: false,
        enableNotification: false,
      })

      // This test would need access to internal config to verify
      // For now, we just ensure the method doesn't throw
      expect(() => {
        errorHandler.handleError({
          type: ErrorType.CLIENT,
          level: ErrorLevel.MEDIUM,
          message: 'Test error',
          timestamp: Date.now(),
        })
      }).not.toThrow()
    })
  })

  describe('handleError', () => {
    it('should handle Error object', () => {
      const error = new Error('Test error message')
      error.stack = 'Error stack trace'

      handleError(error)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.CLIENT,
        level: ErrorLevel.MEDIUM,
        message: 'Test error message',
        stack: 'Error stack trace',
      })
    })

    it('should handle string error', () => {
      handleError('String error message')

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.CLIENT,
        level: ErrorLevel.MEDIUM,
        message: 'String error message',
      })
    })

    it('should handle HTTP error response', () => {
      const httpError = {
        response: {
          status: 404,
          data: {
            message: 'Not found',
            details: { resource: 'user' },
          },
        },
      }

      handleError(httpError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.CLIENT,
        level: ErrorLevel.MEDIUM,
        message: 'Not found',
        code: 404,
      })
    })

    it('should handle 401 authentication error', () => {
      const authError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      }

      handleError(authError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.AUTHENTICATION,
        code: 401,
      })
    })

    it('should handle 403 authorization error', () => {
      const authzError = {
        response: {
          status: 403,
          data: { message: 'Forbidden' },
        },
      }

      handleError(authzError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.AUTHORIZATION,
        code: 403,
      })
    })

    it('should handle 422 validation error', () => {
      const validationError = {
        response: {
          status: 422,
          data: { message: 'Validation failed' },
        },
      }

      handleError(validationError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.VALIDATION,
        code: 422,
      })
    })

    it('should handle 500 server error', () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }

      handleError(serverError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.SERVER,
        level: ErrorLevel.HIGH,
        code: 500,
      })
    })

    it('should add context to error message', () => {
      handleError('Test error', 'TestComponent')

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0].message).toBe('[TestComponent] Test error')
    })
  })

  describe('Specific error handlers', () => {
    it('should handle network error', () => {
      const networkError = new Error('Network connection failed')
      handleNetworkError(networkError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.NETWORK,
        level: ErrorLevel.HIGH,
        message: '网络连接失败',
      })
    })

    it('should handle validation error', () => {
      handleValidationError('Invalid email format', { field: 'email' })

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.VALIDATION,
        level: ErrorLevel.MEDIUM,
        message: 'Invalid email format',
        details: { field: 'email' },
      })
    })

    it('should handle auth error', () => {
      handleAuthError('Token expired')

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.AUTHENTICATION,
        level: ErrorLevel.HIGH,
        message: 'Token expired',
      })
    })

    it('should handle auth error with default message', () => {
      handleAuthError()

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.AUTHENTICATION,
        level: ErrorLevel.HIGH,
        message: '认证失败',
      })
    })

    it('should handle server error', () => {
      const serverError = {
        message: 'Database connection failed',
        code: 500,
        details: { database: 'users' },
      }

      handleServerError(serverError)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(1)
      expect(history[0]).toMatchObject({
        type: ErrorType.SERVER,
        level: ErrorLevel.HIGH,
        message: 'Database connection failed',
        code: 500,
        details: { database: 'users' },
      })
    })
  })

  describe('Error reporting', () => {
    it('should report error to server', async () => {
      // Enable error reporting
      errorHandler.updateConfig({ enableReporting: true })

      const error = {
        type: ErrorType.CLIENT,
        level: ErrorLevel.HIGH,
        message: 'Critical error',
        timestamp: Date.now(),
      }

      errorHandler.handleError(error)

      // Wait for async error reporting
      await new Promise(resolve => setTimeout(resolve, 100))

      expect(global.fetch).toHaveBeenCalledWith('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      })
    })

    it('should retry failed error reports', async () => {
      // Mock fetch to fail first time, succeed second time
      ;(global.fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ ok: true })

      errorHandler.updateConfig({ enableReporting: true })

      const error = {
        type: ErrorType.CLIENT,
        level: ErrorLevel.HIGH,
        message: 'Critical error',
        timestamp: Date.now(),
      }

      errorHandler.handleError(error)

      // Wait for retry
      await new Promise(resolve => setTimeout(resolve, 1500))

      expect(global.fetch).toHaveBeenCalledTimes(2)
    })
  })

  describe('Global error handlers', () => {
    it('should handle unhandled promise rejection', () => {
      const event = new Event('unhandledrejection') as any
      event.reason = new Error('Unhandled promise rejection')

      window.dispatchEvent(event)

      // The global handler should have been set up during import
      // This test verifies the handler doesn't throw
      expect(true).toBe(true)
    })

    it('should handle global JavaScript error', () => {
      const event = new ErrorEvent('error', {
        message: 'Global JavaScript error',
        filename: 'test.js',
        lineno: 10,
        colno: 5,
        error: new Error('Global error'),
      })

      window.dispatchEvent(event)

      // The global handler should have been set up during import
      // This test verifies the handler doesn't throw
      expect(true).toBe(true)
    })
  })
})
