import { z, ZodSchema } from "zod";

/**
 * 通用请求参数解析器
 * 自动支持 GET query / POST body
 */
export async function validateRequest<T extends ZodSchema<any>>(
  req: Request,
  schema: T
): Promise<z.infer<T>> {
  let data: any = {};

  if (req.method === "GET" || req.method === "DELETE") {
    // ✅ 解析 URL search params
    const { searchParams } = new URL(req.url);
    data = Object.fromEntries(searchParams.entries());
  } else {
    // ✅ 解析 JSON body
    try {
      data = await req.json();
    } catch (err) {
      throw new Response("Invalid JSON", { status: 400 });
    }
  }

  try {
    // ✅ 校验数据
    return schema.parse(data);
  } catch (err: any) {
    console.error("参数校验失败:", err);
    throw new Response(JSON.stringify({ error: err.errors }), { status: 400 });
  }
}
