import { type ClassValue, clsx } from 'clsx';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('zh-cn');

/**
 * 合并className
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * 格式化日期时间
 */
export const formatDate = {
  // 标准日期时间格式
  datetime: (date: string | Date | number) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
  
  // 日期格式
  date: (date: string | Date | number) => dayjs(date).format('YYYY-MM-DD'),
  
  // 时间格式
  time: (date: string | Date | number) => dayjs(date).format('HH:mm:ss'),
  
  // 相对时间
  relative: (date: string | Date | number) => dayjs(date).fromNow(),
  
  // 自定义格式
  custom: (date: string | Date | number, format: string) => dayjs(date).format(format),
};

/**
 * 数字格式化
 */
export const formatNumber = {
  // 千分位分隔符
  thousands: (num: number) => num.toLocaleString(),
  
  // 货币格式
  currency: (num: number, currency = '¥') => `${currency}${num.toLocaleString()}`,
  
  // 百分比
  percent: (num: number, decimals = 2) => `${(num * 100).toFixed(decimals)}%`,
  
  // 文件大小
  fileSize: (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  },
  
  // 简化大数字
  abbreviate: (num: number) => {
    if (num >= 1e9) return `${(num / 1e9).toFixed(1)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(1)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(1)}K`;
    return num.toString();
  },
};

/**
 * 字符串工具
 */
export const stringUtils = {
  // 首字母大写
  capitalize: (str: string) => str.charAt(0).toUpperCase() + str.slice(1),
  
  // 驼峰转短横线
  kebabCase: (str: string) => str.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`),
  
  // 短横线转驼峰
  camelCase: (str: string) => str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase()),
  
  // 截断文本
  truncate: (str: string, length: number, suffix = '...') => {
    if (str.length <= length) return str;
    return str.slice(0, length) + suffix;
  },
  
  // 生成随机字符串
  random: (length = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // 移除HTML标签
  stripHtml: (html: string) => html.replace(/<[^>]*>/g, ''),
};

/**
 * 数组工具
 */
export const arrayUtils = {
  // 数组去重
  unique: <T>(arr: T[]) => [...new Set(arr)],
  
  // 根据属性去重
  uniqueBy: <T>(arr: T[], key: keyof T) => {
    const seen = new Set();
    return arr.filter((item) => {
      const value = item[key];
      if (seen.has(value)) return false;
      seen.add(value);
      return true;
    });
  },
  
  // 数组分组
  groupBy: <T>(arr: T[], key: keyof T) => {
    return arr.reduce((groups, item) => {
      const group = item[key] as string;
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  },
  
  // 数组排序
  sortBy: <T>(arr: T[], key: keyof T, order: 'asc' | 'desc' = 'asc') => {
    return [...arr].sort((a, b) => {
      const aVal = a[key];
      const bVal = b[key];
      if (aVal < bVal) return order === 'asc' ? -1 : 1;
      if (aVal > bVal) return order === 'asc' ? 1 : -1;
      return 0;
    });
  },
  
  // 数组分页
  paginate: <T>(arr: T[], page: number, pageSize: number) => {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    return arr.slice(start, end);
  },
};

/**
 * 对象工具
 */
export const objectUtils = {
  // 深拷贝
  deepClone: <T>(obj: T): T => JSON.parse(JSON.stringify(obj)),
  
  // 对象扁平化
  flatten: (obj: Record<string, any>, prefix = ''): Record<string, any> => {
    const result: Record<string, any> = {};
    for (const key in obj) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(result, objectUtils.flatten(obj[key], newKey));
      } else {
        result[newKey] = obj[key];
      }
    }
    return result;
  },
  
  // 移除空值
  removeEmpty: (obj: Record<string, any>) => {
    const result: Record<string, any> = {};
    for (const key in obj) {
      const value = obj[key];
      if (value !== null && value !== undefined && value !== '') {
        result[key] = value;
      }
    }
    return result;
  },
  
  // 选择属性
  pick: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
    const result = {} as Pick<T, K>;
    keys.forEach((key) => {
      if (key in obj) {
        result[key] = obj[key];
      }
    });
    return result;
  },
  
  // 排除属性
  omit: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
    const result = { ...obj };
    keys.forEach((key) => {
      delete result[key];
    });
    return result;
  },
};

/**
 * URL工具
 */
export const urlUtils = {
  // 解析查询参数
  parseQuery: (search: string) => {
    const params = new URLSearchParams(search);
    const result: Record<string, string> = {};
    params.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  },
  
  // 构建查询字符串
  buildQuery: (params: Record<string, any>) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  },
  
  // 合并URL和查询参数
  buildUrl: (baseUrl: string, params?: Record<string, any>) => {
    if (!params) return baseUrl;
    const query = urlUtils.buildQuery(params);
    return query ? `${baseUrl}?${query}` : baseUrl;
  },
};

/**
 * 验证工具
 */
export const validateUtils = {
  // 邮箱验证
  email: (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
  
  // 手机号验证
  phone: (phone: string) => /^1[3-9]\d{9}$/.test(phone),
  
  // 身份证验证
  idCard: (idCard: string) => /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard),
  
  // URL验证
  url: (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
  
  // 密码强度验证
  passwordStrength: (password: string) => {
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z\d]/.test(password)) score++;
    return score;
  },
};

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), wait);
    }
  };
}

/**
 * 休眠函数
 */
export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
