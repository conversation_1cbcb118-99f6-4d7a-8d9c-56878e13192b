import type { ErrorMessageMode } from "./type/axios";
// import router from '/@/router';
// import { PageEnum } from '/@/enums/pageEnum';
import { useUserStore } from "@/store/useUserStore";
import type { MessageInstance } from "antd/es/message/interface";

const errorMap = {
  errMsg401: "用户没有权限（令牌、用户名、密码错误）!",
  errMsg403: "用户得到授权，但是访问是被禁止的。!",
  errMsg404: "网络请求错误,未找到该资源!",
  errMsg405: "网络请求错误,请求方法未允许!",
  errMsg408: "网络请求超时!",
  errMsg500: "服务器错误,请联系管理员!",
  errMsg501: "网络未实现!",
  errMsg502: "网络错误!",
  errMsg503: "服务不可用，服务器暂时过载或维护!",
  errMsg504: "网络超时!",
  errMsg505: "http版本不支持该请求!",
};

export function checkStatus(
  status: number,
  msg: string,
  messageApi: MessageInstance
): void {
  let errMessage = "";

  switch (status) {
    case 400:
      errMessage = `${msg}`;
      break;
    // 401: Not logged in
    // Jump to the login page if not logged in, and carry the path of the current page
    // Return to the current page after successful login. This step needs to be operated on the login page.
    case 401:
      const { setToken } = useUserStore();
      setToken(undefined);
      errMessage = msg || errorMap.errMsg401;
      // if (stp === SessionTimeoutProcessingEnum.PAGE_COVERAGE) {
      //   userStore.setSessionTimeout(true);
      // } else {
      //   userStore.logout(true);
      // }
      break;
    case 403:
      errMessage = errorMap.errMsg403;
      break;
    // 404请求不存在
    case 404:
      errMessage = errorMap.errMsg404;
      break;
    case 405:
      errMessage = errorMap.errMsg405;
      break;
    case 408:
      errMessage = errorMap.errMsg408;
      break;
    case 500:
      errMessage = errorMap.errMsg500;
      break;
    case 501:
      errMessage = errorMap.errMsg501;
      break;
    case 502:
      errMessage = errorMap.errMsg502;
      break;
    case 503:
      errMessage = errorMap.errMsg503;
      break;
    case 504:
      errMessage = errorMap.errMsg504;
      break;
    case 505:
      errMessage = errorMap.errMsg505;
      break;
    default:
  }

  if (errMessage) {
    messageApi.error(errMessage);
  }
}
