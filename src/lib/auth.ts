import jwt from "jsonwebtoken";
import { NextRequest } from "next/server";

export function validateToken(request: NextRequest) {
  const authorization = request.headers.get("authorization");

  if (!authorization || !authorization.startsWith("Bearer ")) {
    return null;
  }

  const token = authorization.replace("Bearer ", "");

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
      id: number;
    };
    return decoded;
  } catch (error) {
    return null;
  }
}
