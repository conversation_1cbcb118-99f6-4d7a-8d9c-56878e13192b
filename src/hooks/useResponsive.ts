"use client";

import { useState, useEffect } from "react";
import { Grid } from "antd";

const { useBreakpoint } = Grid;

// 断点定义
export const breakpoints = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
} as const;

// 设备类型
export type DeviceType = "mobile" | "tablet" | "desktop";

// 屏幕尺寸类型
export type ScreenSize = "xs" | "sm" | "md" | "lg" | "xl" | "xxl";

// 响应式Hook
export function useResponsive() {
  const screens = useBreakpoint();
  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  });

  // 监听窗口大小变化
  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // 初始化
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 获取当前设备类型
  const getDeviceType = (): DeviceType => {
    if (screens.xs && !screens.sm) return "mobile";
    if (screens.sm && !screens.lg) return "tablet";
    return "desktop";
  };

  // 获取当前屏幕尺寸
  const getScreenSize = (): ScreenSize => {
    if (screens.xxl) return "xxl";
    if (screens.xl) return "xl";
    if (screens.lg) return "lg";
    if (screens.md) return "md";
    if (screens.sm) return "sm";
    return "xs";
  };

  // 判断是否为移动设备
  const isMobile = getDeviceType() === "mobile";

  // 判断是否为平板设备
  const isTablet = getDeviceType() === "tablet";

  // 判断是否为桌面设备
  const isDesktop = getDeviceType() === "desktop";

  // 判断是否为小屏设备（手机+平板）
  const isSmallScreen = isMobile || isTablet;

  // 判断是否为大屏设备
  const isLargeScreen = screens.lg || false;

  return {
    // 原始断点信息
    screens,

    // 窗口尺寸
    windowSize,

    // 设备类型
    deviceType: getDeviceType(),
    screenSize: getScreenSize(),

    // 设备判断
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,

    // 具体断点判断
    isXs: screens.xs || false,
    isSm: screens.sm || false,
    isMd: screens.md || false,
    isLg: screens.lg || false,
    isXl: screens.xl || false,
    isXxl: screens.xxl || false,
  };
}

// 响应式值Hook
export function useResponsiveValue<T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
  default: T;
}): T {
  const { screenSize } = useResponsive();

  // 按优先级返回对应值
  return values[screenSize] ?? values.default;
}

// 响应式配置Hook
export function useResponsiveConfig() {
  const { isMobile, isTablet, isDesktop, isSmallScreen } = useResponsive();

  return {
    // 表格配置
    table: {
      size: isMobile ? "small" : "middle",
      scroll: { x: isSmallScreen ? 800 : undefined },
      pagination: {
        size: isMobile ? "small" : "default",
        showSizeChanger: !isMobile,
        showQuickJumper: !isMobile,
        showTotal: !isMobile,
        pageSize: isMobile ? 5 : 10,
      },
    },

    // 表单配置
    form: {
      layout: isMobile ? "vertical" : "horizontal",
      labelCol: isMobile ? undefined : { span: 6 },
      wrapperCol: isMobile ? undefined : { span: 18 },
      size: isMobile ? "small" : "middle",
    },

    // 按钮配置
    button: {
      size: isMobile ? "small" : "middle",
      block: isMobile,
    },

    // 输入框配置
    input: {
      size: isMobile ? "small" : "middle",
    },

    // 卡片配置
    card: {
      size: isMobile ? "small" : "default",
      bodyStyle: isMobile ? { padding: "12px" } : undefined,
    },

    // 模态框配置
    modal: {
      width: isMobile ? "95%" : isTablet ? "80%" : "60%",
      centered: isMobile,
    },

    // 抽屉配置
    drawer: {
      width: isMobile ? "100%" : isTablet ? "80%" : "60%",
      placement: isMobile ? "bottom" : "right",
    },

    // 栅格配置
    grid: {
      gutter: isMobile ? [8, 8] : [16, 16],
      xs: 24,
      sm: 12,
      md: 8,
      lg: 6,
      xl: 4,
      xxl: 3,
    },

    // 布局配置
    layout: {
      siderWidth: isMobile ? 0 : isTablet ? 200 : 250,
      siderCollapsedWidth: isMobile ? 0 : 80,
      headerHeight: isMobile ? 48 : 64,
      contentPadding: isMobile ? "12px" : "24px",
    },
  };
}

// 响应式样式Hook
export function useResponsiveStyles() {
  const { isMobile, isTablet, isSmallScreen } = useResponsive();

  return {
    // 容器样式
    container: {
      padding: isMobile ? "8px" : "16px",
      margin: isMobile ? "4px" : "8px",
    },

    // 文字样式
    text: {
      fontSize: isMobile ? "12px" : "14px",
      lineHeight: isMobile ? "1.4" : "1.5",
    },

    // 标题样式
    title: {
      fontSize: isMobile ? "16px" : "20px",
      marginBottom: isMobile ? "8px" : "16px",
    },

    // 间距样式
    spacing: {
      small: isMobile ? "4px" : "8px",
      medium: isMobile ? "8px" : "16px",
      large: isMobile ? "12px" : "24px",
    },

    // 阴影样式
    shadow: {
      card: isMobile ? "none" : "0 2px 8px rgba(0, 0, 0, 0.1)",
      modal: isMobile ? "none" : "0 4px 12px rgba(0, 0, 0, 0.15)",
    },

    // 边框样式
    border: {
      radius: isMobile ? "4px" : "6px",
      width: "1px",
    },

    // 隐藏/显示样式
    display: {
      mobileOnly: { display: isMobile ? "block" : "none" },
      tabletOnly: { display: isTablet ? "block" : "none" },
      desktopOnly: { display: isSmallScreen ? "none" : "block" },
      smallScreenOnly: { display: isSmallScreen ? "block" : "none" },
      largeScreenOnly: { display: isSmallScreen ? "none" : "block" },
    },
  };
}
