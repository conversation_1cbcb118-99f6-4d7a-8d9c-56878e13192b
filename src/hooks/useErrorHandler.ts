'use client';

import { useState, useCallback, useEffect } from 'react';
import { message, notification } from 'antd';
import {
  errorHandler,
  handleError,
  handleNetworkError,
  handleValidationError,
  handleAuthError,
  handleAuthorizationError,
  handleServerError,
  ErrorType,
  ErrorLevel,
  ErrorInfo,
} from '@/utils/errorHandler';

// 错误状态接口
interface ErrorState {
  error: Error | null;
  isError: boolean;
  errorMessage: string;
  errorCode?: string | number;
  errorType?: ErrorType;
}

// 错误处理Hook
export function useErrorHandler() {
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    errorMessage: '',
  });

  // 清除错误
  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isError: false,
      errorMessage: '',
    });
  }, []);

  // 设置错误
  const setError = useCallback((error: Error | string, type?: ErrorType, code?: string | number) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;

    setErrorState({
      error: errorObj,
      isError: true,
      errorMessage: errorObj.message,
      errorCode: code,
      errorType: type,
    });

    // 使用全局错误处理器
    handleError(errorObj);
  }, []);

  // 处理异步操作错误
  const handleAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    options?: {
      onError?: (error: Error) => void;
      showError?: boolean;
      errorMessage?: string;
    }
  ): Promise<T | null> => {
    try {
      clearError();
      const result = await asyncFn();
      return result;
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error(String(error));

      if (options?.onError) {
        options.onError(errorObj);
      } else {
        setError(errorObj);
      }

      if (options?.showError !== false) {
        const errorMessage = options?.errorMessage || errorObj.message;
        message.error(errorMessage);
      }

      return null;
    }
  }, [setError, clearError]);

  return {
    ...errorState,
    setError,
    clearError,
    handleAsync,
  };
}

// 网络请求错误处理Hook
export function useNetworkErrorHandler() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const handleRequest = useCallback(async <T>(
    requestFn: () => Promise<T>,
    options?: {
      onSuccess?: (data: T) => void;
      onError?: (error: Error) => void;
      showLoading?: boolean;
      showError?: boolean;
      retryCount?: number;
    }
  ): Promise<T | null> => {
    const {
      onSuccess,
      onError,
      showLoading = true,
      showError = true,
      retryCount = 0,
    } = options || {};

    let attempt = 0;
    const maxAttempts = retryCount + 1;

    while (attempt < maxAttempts) {
      try {
        if (showLoading) setLoading(true);
        setError(null);

        const result = await requestFn();

        if (onSuccess) {
          onSuccess(result);
        }

        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        attempt++;

        if (attempt >= maxAttempts) {
          setError(error);

          if (onError) {
            onError(error);
          }

          if (showError) {
            handleNetworkError(error);
          }

          return null;
        }

        // 重试延迟
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      } finally {
        if (showLoading) setLoading(false);
      }
    }

    return null;
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    handleRequest,
    clearError,
  };
}

// 表单验证错误处理Hook
export function useFormErrorHandler() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const setFieldError = useCallback((field: string, error: string) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const handleValidationErrors = useCallback((errors: Record<string, string>) => {
    setFieldErrors(errors);

    // 显示第一个错误
    const firstError = Object.values(errors)[0];
    if (firstError) {
      handleValidationError(firstError, errors);
    }
  }, []);

  const getFieldError = useCallback((field: string) => {
    return fieldErrors[field];
  }, [fieldErrors]);

  const hasErrors = Object.keys(fieldErrors).length > 0;

  return {
    fieldErrors,
    hasErrors,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    handleValidationErrors,
    getFieldError,
  };
}

// 权限错误处理Hook
export function useAuthErrorHandler() {
  const handleAuthenticationError = useCallback((message?: string) => {
    handleAuthError(message);

    // 可以在这里添加重定向到登录页面的逻辑
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  }, []);

  const handlePermissionError = useCallback((message?: string) => {
    handleAuthorizationError(message);
  }, []);

  return {
    handleAuthenticationError,
    handleAuthorizationError: handlePermissionError,
  };
}

// 错误重试Hook
export function useRetry() {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const retry = useCallback(async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T | null> => {
    setIsRetrying(true);

    for (let i = 0; i <= maxRetries; i++) {
      try {
        setRetryCount(i);
        const result = await fn();
        setIsRetrying(false);
        setRetryCount(0);
        return result;
      } catch (error) {
        if (i === maxRetries) {
          setIsRetrying(false);
          handleError(error as Error, `Retry failed after ${maxRetries} attempts`);
          return null;
        }

        // 指数退避延迟
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }

    setIsRetrying(false);
    return null;
  }, []);

  return {
    retry,
    retryCount,
    isRetrying,
  };
}

// 错误监控Hook
export function useErrorMonitor() {
  const [errorHistory, setErrorHistory] = useState<ErrorInfo[]>([]);

  useEffect(() => {
    // 定期获取错误历史
    const interval = setInterval(() => {
      const history = errorHandler.getErrorHistory();
      setErrorHistory(history);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const clearHistory = useCallback(() => {
    errorHandler.clearErrorHistory();
    setErrorHistory([]);
  }, []);

  const getErrorStats = useCallback(() => {
    const stats = {
      total: errorHistory.length,
      byType: {} as Record<ErrorType, number>,
      byLevel: {} as Record<ErrorLevel, number>,
      recent: errorHistory.filter(error =>
        Date.now() - error.timestamp < 5 * 60 * 1000 // 最近5分钟
      ).length,
    };

    errorHistory.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1;
    });

    return stats;
  }, [errorHistory]);

  return {
    errorHistory,
    clearHistory,
    getErrorStats,
  };
}

// 全局错误通知Hook
export function useGlobalErrorNotification() {
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      notification.error({
        message: '页面错误',
        description: event.message,
        duration: 5,
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      notification.error({
        message: '未处理的Promise错误',
        description: event.reason?.message || '发生了未知错误',
        duration: 5,
      });
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);
}
