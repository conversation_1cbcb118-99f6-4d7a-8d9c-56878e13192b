import { renderHook, act } from '@testing-library/react'
import { 
  useE<PERSON>r<PERSON><PERSON><PERSON>, 
  useNetworkError<PERSON>and<PERSON>, 
  useFormErrorHandler,
  useRetry,
} from '@/hooks/useErrorHandler'
import { ErrorType } from '@/utils/errorHandler'

// Mock Ant Design message
jest.mock('antd', () => ({
  message: {
    error: jest.fn(),
  },
}))

// Mock error handler
jest.mock('@/utils/errorHandler', () => ({
  handleError: jest.fn(),
  ErrorType: {
    CLIENT: 'CLIENT',
    NETWORK: 'NETWORK',
    VALIDATION: 'VALIDATION',
  },
  ErrorLevel: {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
  },
}))

describe('useErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with no error', () => {
    const { result } = renderHook(() => useErrorHandler())

    expect(result.current.isError).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.errorMessage).toBe('')
  })

  it('should set error correctly', () => {
    const { result } = renderHook(() => useErrorHandler())

    act(() => {
      result.current.setError('Test error message')
    })

    expect(result.current.isError).toBe(true)
    expect(result.current.error).toBeInstanceOf(Error)
    expect(result.current.errorMessage).toBe('Test error message')
  })

  it('should clear error correctly', () => {
    const { result } = renderHook(() => useErrorHandler())

    act(() => {
      result.current.setError('Test error')
    })

    expect(result.current.isError).toBe(true)

    act(() => {
      result.current.clearError()
    })

    expect(result.current.isError).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.errorMessage).toBe('')
  })

  it('should handle async operation success', async () => {
    const { result } = renderHook(() => useErrorHandler())

    const asyncFn = jest.fn().mockResolvedValue('success result')

    let asyncResult: any
    await act(async () => {
      asyncResult = await result.current.handleAsync(asyncFn)
    })

    expect(asyncResult).toBe('success result')
    expect(result.current.isError).toBe(false)
    expect(asyncFn).toHaveBeenCalled()
  })

  it('should handle async operation failure', async () => {
    const { result } = renderHook(() => useErrorHandler())

    const asyncFn = jest.fn().mockRejectedValue(new Error('Async error'))

    let asyncResult: any
    await act(async () => {
      asyncResult = await result.current.handleAsync(asyncFn)
    })

    expect(asyncResult).toBe(null)
    expect(result.current.isError).toBe(true)
    expect(result.current.errorMessage).toBe('Async error')
  })

  it('should handle async operation with custom error handler', async () => {
    const { result } = renderHook(() => useErrorHandler())

    const asyncFn = jest.fn().mockRejectedValue(new Error('Async error'))
    const customErrorHandler = jest.fn()

    await act(async () => {
      await result.current.handleAsync(asyncFn, {
        onError: customErrorHandler,
      })
    })

    expect(customErrorHandler).toHaveBeenCalledWith(expect.any(Error))
    expect(result.current.isError).toBe(false) // Custom handler prevents setting error
  })
})

describe('useNetworkErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with no loading and no error', () => {
    const { result } = renderHook(() => useNetworkErrorHandler())

    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBe(null)
  })

  it('should handle successful request', async () => {
    const { result } = renderHook(() => useNetworkErrorHandler())

    const requestFn = jest.fn().mockResolvedValue('success data')
    const onSuccess = jest.fn()

    let requestResult: any
    await act(async () => {
      requestResult = await result.current.handleRequest(requestFn, {
        onSuccess,
      })
    })

    expect(requestResult).toBe('success data')
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBe(null)
    expect(onSuccess).toHaveBeenCalledWith('success data')
  })

  it('should handle failed request', async () => {
    const { result } = renderHook(() => useNetworkErrorHandler())

    const requestFn = jest.fn().mockRejectedValue(new Error('Network error'))
    const onError = jest.fn()

    let requestResult: any
    await act(async () => {
      requestResult = await result.current.handleRequest(requestFn, {
        onError,
      })
    })

    expect(requestResult).toBe(null)
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBeInstanceOf(Error)
    expect(onError).toHaveBeenCalledWith(expect.any(Error))
  })

  it('should handle request with retry', async () => {
    const { result } = renderHook(() => useNetworkErrorHandler())

    const requestFn = jest.fn()
      .mockRejectedValueOnce(new Error('First failure'))
      .mockRejectedValueOnce(new Error('Second failure'))
      .mockResolvedValueOnce('success on third try')

    let requestResult: any
    await act(async () => {
      requestResult = await result.current.handleRequest(requestFn, {
        retryCount: 2,
      })
    })

    expect(requestResult).toBe('success on third try')
    expect(requestFn).toHaveBeenCalledTimes(3)
  })

  it('should clear error', () => {
    const { result } = renderHook(() => useNetworkErrorHandler())

    // Simulate error state
    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBe(null)
  })
})

describe('useFormErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with no errors', () => {
    const { result } = renderHook(() => useFormErrorHandler())

    expect(result.current.fieldErrors).toEqual({})
    expect(result.current.hasErrors).toBe(false)
  })

  it('should set field error', () => {
    const { result } = renderHook(() => useFormErrorHandler())

    act(() => {
      result.current.setFieldError('email', 'Invalid email format')
    })

    expect(result.current.fieldErrors.email).toBe('Invalid email format')
    expect(result.current.hasErrors).toBe(true)
  })

  it('should clear field error', () => {
    const { result } = renderHook(() => useFormErrorHandler())

    act(() => {
      result.current.setFieldError('email', 'Invalid email format')
    })

    expect(result.current.hasErrors).toBe(true)

    act(() => {
      result.current.clearFieldError('email')
    })

    expect(result.current.fieldErrors.email).toBeUndefined()
    expect(result.current.hasErrors).toBe(false)
  })

  it('should clear all errors', () => {
    const { result } = renderHook(() => useFormErrorHandler())

    act(() => {
      result.current.setFieldError('email', 'Invalid email')
      result.current.setFieldError('password', 'Password too short')
    })

    expect(result.current.hasErrors).toBe(true)

    act(() => {
      result.current.clearAllErrors()
    })

    expect(result.current.fieldErrors).toEqual({})
    expect(result.current.hasErrors).toBe(false)
  })

  it('should handle validation errors', () => {
    const { result } = renderHook(() => useFormErrorHandler())

    const validationErrors = {
      email: 'Invalid email format',
      password: 'Password is required',
      username: 'Username already exists',
    }

    act(() => {
      result.current.handleValidationErrors(validationErrors)
    })

    expect(result.current.fieldErrors).toEqual(validationErrors)
    expect(result.current.hasErrors).toBe(true)
  })

  it('should get field error', () => {
    const { result } = renderHook(() => useFormErrorHandler())

    act(() => {
      result.current.setFieldError('email', 'Invalid email format')
    })

    expect(result.current.getFieldError('email')).toBe('Invalid email format')
    expect(result.current.getFieldError('password')).toBeUndefined()
  })
})

describe('useRetry', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useRetry())

    expect(result.current.retryCount).toBe(0)
    expect(result.current.isRetrying).toBe(false)
  })

  it('should succeed on first try', async () => {
    const { result } = renderHook(() => useRetry())

    const successFn = jest.fn().mockResolvedValue('success')

    let retryResult: any
    await act(async () => {
      retryResult = await result.current.retry(successFn, 3, 1000)
    })

    expect(retryResult).toBe('success')
    expect(result.current.retryCount).toBe(0)
    expect(result.current.isRetrying).toBe(false)
    expect(successFn).toHaveBeenCalledTimes(1)
  })

  it('should retry on failure and eventually succeed', async () => {
    const { result } = renderHook(() => useRetry())

    const retryFn = jest.fn()
      .mockRejectedValueOnce(new Error('First failure'))
      .mockRejectedValueOnce(new Error('Second failure'))
      .mockResolvedValueOnce('success on third try')

    let retryResult: any
    const retryPromise = act(async () => {
      retryResult = await result.current.retry(retryFn, 3, 100)
    })

    // Fast-forward timers for delays
    jest.advanceTimersByTime(300)

    await retryPromise

    expect(retryResult).toBe('success on third try')
    expect(retryFn).toHaveBeenCalledTimes(3)
  })

  it('should fail after max retries', async () => {
    const { result } = renderHook(() => useRetry())

    const failFn = jest.fn().mockRejectedValue(new Error('Always fails'))

    let retryResult: any
    const retryPromise = act(async () => {
      retryResult = await result.current.retry(failFn, 2, 100)
    })

    // Fast-forward timers for delays
    jest.advanceTimersByTime(300)

    await retryPromise

    expect(retryResult).toBe(null)
    expect(failFn).toHaveBeenCalledTimes(3) // Initial + 2 retries
  })

  it('should use exponential backoff for delays', async () => {
    const { result } = renderHook(() => useRetry())

    const failFn = jest.fn().mockRejectedValue(new Error('Always fails'))

    const retryPromise = act(async () => {
      await result.current.retry(failFn, 2, 100)
    })

    // Check that delays are exponential (100ms, 200ms)
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 100)
    jest.advanceTimersByTime(100)
    
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 200)
    jest.advanceTimersByTime(200)

    await retryPromise
  })
})
