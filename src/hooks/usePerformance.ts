'use client';

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { debounce, throttle, performanceMonitor, cacheManager } from '@/utils/performance';

// 防抖Hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// 节流Hook
export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useRef<number>(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
}

// 防抖回调Hook
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const debouncedCallback = useMemo(
    () => debounce(callback, delay),
    [callback, delay]
  );

  return debouncedCallback as T;
}

// 节流回调Hook
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  limit: number
): T {
  const throttledCallback = useMemo(
    () => throttle(callback, limit),
    [callback, limit]
  );

  return throttledCallback as T;
}

// 性能监控Hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState(performanceMonitor.getMetrics());

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(performanceMonitor.getMetrics());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    metrics,
    memoryUsage: performanceMonitor.getMemoryUsage(),
    measureFunction: performanceMonitor.measureFunction.bind(performanceMonitor),
    measureAsyncFunction: performanceMonitor.measureAsyncFunction.bind(performanceMonitor),
  };
}

// 缓存Hook
export function useCache<T>(key: string, defaultValue?: T) {
  const [value, setValue] = useState<T>(() => {
    const cached = cacheManager.get(key);
    return cached !== null ? cached : defaultValue;
  });

  const setCachedValue = useCallback((newValue: T, ttl?: number) => {
    setValue(newValue);
    cacheManager.set(key, newValue, ttl);
  }, [key]);

  const clearCache = useCallback(() => {
    setValue(defaultValue as T);
    cacheManager.delete(key);
  }, [key, defaultValue]);

  return [value, setCachedValue, clearCache] as const;
}

// 虚拟滚动Hook
export function useVirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
    }));
  }, [items, startIndex, endIndex]);

  const totalHeight = items.length * itemHeight;

  const handleScroll = useThrottledCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, 16); // 60fps

  return {
    visibleItems,
    totalHeight,
    startIndex,
    handleScroll,
  };
}

// 懒加载Hook
export function useLazyLoad(threshold = 0.1, rootMargin = '50px') {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isLoaded) {
          setIsIntersecting(true);
          setIsLoaded(true);
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin, isLoaded]);

  return { ref, isIntersecting, isLoaded };
}

// 异步数据Hook
export function useAsyncData<T>(
  asyncFunction: () => Promise<T>,
  dependencies: any[] = [],
  options: {
    cache?: boolean;
    cacheKey?: string;
    cacheTTL?: number;
    immediate?: boolean;
  } = {}
) {
  const {
    cache = false,
    cacheKey,
    cacheTTL,
    immediate = true,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 检查缓存
      if (cache && cacheKey) {
        const cachedData = cacheManager.get(cacheKey);
        if (cachedData) {
          setData(cachedData);
          setLoading(false);
          return cachedData;
        }
      }

      const result = await performanceMonitor.measureAsyncFunction(
        `AsyncData ${cacheKey || 'unknown'}`,
        asyncFunction
      );

      setData(result);

      // 设置缓存
      if (cache && cacheKey) {
        cacheManager.set(cacheKey, result, cacheTTL);
      }

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [asyncFunction, cache, cacheKey, cacheTTL, ...dependencies]);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  const refresh = useCallback(() => {
    if (cache && cacheKey) {
      cacheManager.delete(cacheKey);
    }
    return execute();
  }, [execute, cache, cacheKey]);

  return {
    data,
    loading,
    error,
    execute,
    refresh,
  };
}

// 窗口大小Hook（优化版）
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  });

  const handleResize = useThrottledCallback(() => {
    setWindowSize({
      width: window.innerWidth,
      height: window.innerHeight,
    });
  }, 100);

  useEffect(() => {
    // 初始化
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  return windowSize;
}

// 滚动位置Hook（优化版）
export function useScrollPosition() {
  const [scrollPosition, setScrollPosition] = useState({
    x: 0,
    y: 0,
  });

  const handleScroll = useThrottledCallback(() => {
    setScrollPosition({
      x: window.pageXOffset,
      y: window.pageYOffset,
    });
  }, 16); // 60fps

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return scrollPosition;
}

// 组件可见性Hook
export function useVisibility(threshold = 0.1) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return { ref, isVisible };
}
