import { useTranslations, useLocale } from "next-intl";
import type { Locale } from "@/components/i18n/config";

// 通用翻译Hook
export function useI18n() {
  const locale = useLocale() as Locale;
  const t = useTranslations();

  return {
    locale,
    t,
    // 常用翻译快捷方式
    common: useTranslations("common"),
    nav: useTranslations("nav"),
    auth: useTranslations("auth"),
    dashboard: useTranslations("dashboard"),
    users: useTranslations("users"),
    roles: useTranslations("roles"),
    articles: useTranslations("articles"),
    settings: useTranslations("settings"),
    theme: useTranslations("theme"),
    upload: useTranslations("upload"),
    pagination: useTranslations("pagination"),
    validation: useTranslations("validation"),
    messages: useTranslations("messages"),
  };
}

// 通用翻译Hook
export function useCommonTranslations() {
  return useTranslations("common");
}

// 导航翻译Hook
export function useNavTranslations() {
  return useTranslations("nav");
}

// 认证翻译Hook
export function useAuthTranslations() {
  return useTranslations("auth");
}

// 仪表盘翻译Hook
export function useDashboardTranslations() {
  return useTranslations("dashboard");
}

// 用户管理翻译Hook
export function useUsersTranslations() {
  return useTranslations("users");
}

// 角色管理翻译Hook
export function useRolesTranslations() {
  return useTranslations("roles");
}

// 文章管理翻译Hook
export function useArticlesTranslations() {
  return useTranslations("articles");
}

// 系统设置翻译Hook
export function useSettingsTranslations() {
  return useTranslations("settings");
}

// 主题翻译Hook
export function useThemeTranslations() {
  return useTranslations("theme");
}

// 上传翻译Hook
export function useUploadTranslations() {
  return useTranslations("upload");
}

// 分页翻译Hook
export function usePaginationTranslations() {
  return useTranslations("pagination");
}

// 验证翻译Hook
export function useValidationTranslations() {
  return useTranslations("validation");
}

// 消息翻译Hook
export function useMessagesTranslations() {
  return useTranslations("messages");
}

// 格式化翻译文本（支持参数替换）
export function useFormatMessage() {
  const t = useTranslations();

  return (key: string, values?: Record<string, any>) => {
    return t(key, values);
  };
}

// 获取当前语言信息
export function useCurrentLocale() {
  const locale = useLocale() as Locale;

  return {
    locale,
    isZh: locale === "zh",
    isEn: locale === "en",
  };
}

// 日期格式化Hook
export function useDateFormat() {
  const locale = useLocale() as Locale;

  const formatDate = (
    date: Date | string | number,
    options?: Intl.DateTimeFormatOptions
  ) => {
    const dateObj =
      typeof date === "string" || typeof date === "number"
        ? new Date(date)
        : date;

    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    };

    return new Intl.DateTimeFormat(locale === "zh" ? "zh-CN" : "en-US", {
      ...defaultOptions,
      ...options,
    }).format(dateObj);
  };

  const formatDateOnly = (date: Date | string | number) => {
    return formatDate(date, {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  const formatTimeOnly = (date: Date | string | number) => {
    return formatDate(date, {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const formatRelativeTime = (date: Date | string | number) => {
    const dateObj =
      typeof date === "string" || typeof date === "number"
        ? new Date(date)
        : date;
    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - dateObj.getTime()) / 1000
    );

    const rtf = new Intl.RelativeTimeFormat(
      locale === "zh" ? "zh-CN" : "en-US",
      {
        numeric: "auto",
      }
    );

    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, "second");
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), "minute");
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), "hour");
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), "day");
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), "month");
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), "year");
    }
  };

  return {
    formatDate,
    formatDateOnly,
    formatTimeOnly,
    formatRelativeTime,
  };
}

// 数字格式化Hook
export function useNumberFormat() {
  const locale = useLocale() as Locale;

  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(
      locale === "zh" ? "zh-CN" : "en-US",
      options
    ).format(number);
  };

  const formatCurrency = (amount: number, currency = "CNY") => {
    return formatNumber(amount, {
      style: "currency",
      currency,
    });
  };

  const formatPercent = (value: number) => {
    return formatNumber(value / 100, {
      style: "percent",
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    });
  };

  const formatCompactNumber = (number: number) => {
    return formatNumber(number, {
      notation: "compact",
      compactDisplay: "short",
    });
  };

  return {
    formatNumber,
    formatCurrency,
    formatPercent,
    formatCompactNumber,
  };
}

// 表单验证消息Hook
export function useValidationMessages() {
  const validation = useValidationTranslations();

  return {
    required: (field?: string) =>
      field ? `${field}${validation("required")}` : validation("required"),
    email: validation("email"),
    phone: validation("phone"),
    url: validation("url"),
    number: validation("number"),
    integer: validation("integer"),
    positive: validation("positive"),
    minLength: (min: number) => validation("minLength", { min }),
    maxLength: (max: number) => validation("maxLength", { max }),
    min: (min: number) => validation("min", { min }),
    max: (max: number) => validation("max", { max }),
    pattern: validation("pattern"),
  };
}
