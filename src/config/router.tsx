import {
  UserOutlined,
  SettingOutlined,
  DashboardOutlined,
  FileTextOutlined,
  BugOutlined,
  PictureOutlined,
  ShopOutlined,
  TeamOutlined,
  RedEnvelopeOutlined,
  CarOutlined,
  ContactsOutlined,
} from "@ant-design/icons";

// 菜单数据（使用国际化）
export const routerConfig = [
  // {
  //   key: '/',
  //   icon: <HomeOutlined />,
  //   label: '首页',
  // },
  {
    key: "/main/dashboard",
    icon: <DashboardOutlined />,
    label: "首页",
    url: "/main/dashboard",
  },
  {
    key: "/main/banner",
    icon: <PictureOutlined />,
    label: "轮播管理",
    url: "/main/banner",
  },
  {
    key: "/main/user1",
    icon: <UserOutlined />,
    label: "用户管理",
    permission: ["users:view"],
    url: "/main/users",
    children: [
      {
        key: "/main/users",
        label: "用户列表",
        permission: ["users:list"],
        url: "/main/users",
      },
      {
        key: "/main/system/roles",
        label: "角色管理",
        permission: ["roles:view"],
        url: "/main/system/roles",
      },
    ],
  },
  {
    key: "/main/business1",
    icon: <ContactsOutlined />,
    label: "商户管理",
    permission: ["business:view"],
    url: "/main/business",
    children: [
      {
        key: "/main/business",
        label: "商户列表",
        permission: ["business:list"],
        url: "/main/business",
      },
    ],
  },
  {
    key: "/main/shop1",
    icon: <ShopOutlined />,
    label: "店铺管理",
    permission: ["shop:view"],
    url: "/main/shop",
    children: [
      {
        key: "/main/shop",
        label: "店铺列表",
        permission: ["shop:list"],
        url: "/main/shop",
      },
    ],
  },
  {
    key: "/main/shopUser1",
    icon: <TeamOutlined />,
    label: "商店用户管理",
    permission: ["shopUser:view"],
    url: "/main/shopUser",
    children: [
      {
        key: "/main/shopUser",
        label: "商店用户列表",
        permission: ["shopUser:list"],
        url: "/main/shopUser",
      },
    ],
  },
  {
    key: "/main/order1",
    icon: <RedEnvelopeOutlined />,
    label: "订单管理",
    permission: ["order:view"],
    url: "/main/order",
    children: [
      {
        key: "/main/order",
        label: "订单列表",
        permission: ["order:list"],
        url: "/main/order",
      },
    ],
  },
  {
    key: "/main/car1",
    icon: <CarOutlined />,
    label: "车辆管理",
    permission: ["car:view"],
    url: "/main/car",
    children: [
      {
        key: "/main/car",
        label: "车辆列表",
        permission: ["car:list"],
        url: "/main/car",
      },
    ],
  },
  {
    key: "/main/content",
    icon: <FileTextOutlined />,
    label: "content",
    children: [
      {
        key: "/main/content/articles",
        label: "articles",
      },
      {
        key: "/main/content/categories",
        label: "categories",
      },
    ],
  },
  {
    key: "/main/examples",
    icon: <BugOutlined />,
    label: "示例页面1",
    children: [
      {
        key: "/main/examples/table",
        label: "表格示例",
      },
      {
        key: "/examples/forms",
        label: "表单示例",
      },
      {
        key: "/examples/responsive",
        label: "响应式示例",
      },
      {
        key: "/examples/performance",
        label: "性能优化",
      },
      {
        key: "/examples/errors",
        label: "错误处理",
      },
    ],
  },
  {
    key: "/system",
    icon: <SettingOutlined />,
    label: "system",
    children: [
      {
        key: "/system/settings",
        label: "settings",
      },
      {
        key: "/system/logs",
        label: "logs",
      },
    ],
  },
];
