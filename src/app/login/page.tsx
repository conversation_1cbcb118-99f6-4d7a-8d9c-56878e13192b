'use client';

import React, { useState } from 'react';
import { Form, Input, Button, Card, Checkbox, message, Typography, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/store/useUserStore';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
  remember: boolean;
}

export default function LoginPage() {
  const router = useRouter();
  const { login: userLogin, loading } = useUserStore();

  const onFinish = async (values: LoginForm) => {
    try {
      // 记住我功能
      if (values.remember) {
        localStorage.setItem('rememberMe', 'true');
        localStorage.setItem('username', values.username);
      } else {
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('username');
      }

      // 使用store的login方法
      await userLogin({
        username: values.username,
        password: values.password,
        remember: values.remember,
      });

      message.success('登录成功');

      // 获取重定向地址
      const searchParams = new URLSearchParams(window.location.search);
      const redirect = searchParams.get('redirect') || '/main/dashboard';
      router.push(redirect);
    } catch (error) {
      console.error('登录错误:', error);
      message.error('登录失败，请稍后重试');
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: '400px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px',
        }}
        styles={{
          body: { padding: '40px' }
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            background: 'linear-gradient(135deg, #1890ff, #36cfc9)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            fontSize: '24px',
            color: 'white',
          }}>
            🚀
          </div>
          <Title level={2} style={{ margin: 0, color: '#262626' }}>
            CCXC
          </Title>
          <Text type="secondary">企业级管理后台系统</Text>
        </div>

        <Form
          name="login"
          initialValues={{
            remember: typeof window !== 'undefined' ? localStorage.getItem('rememberMe') === 'true' : false,
            username: typeof window !== 'undefined' ? localStorage.getItem('username') || '' : '',
          }}
          onFinish={onFinish}
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少2个字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <a href="#" style={{ color: '#1890ff' }}>
                忘记密码？
              </a>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<LoginOutlined />}
              style={{
                width: '100%',
                height: '44px',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: 'bold',
              }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{
          marginTop: '24px',
          padding: '16px',
          background: '#f6f8fa',
          borderRadius: '8px',
          fontSize: '12px',
          color: '#666',
        }}>
          <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
            🔑 测试账号：
          </div>
          <Space direction="vertical" size="small">
            <div>管理员：admin / 123456</div>
            <div>普通用户：user / 123456</div>
          </Space>
        </div>
      </Card>
    </div>
  );
}
