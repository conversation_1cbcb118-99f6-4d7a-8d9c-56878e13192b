import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir } from "fs/promises";
import { existsSync } from "fs";
import path from "path";
import { validateToken } from "@/lib/auth";

// 文件上传配置
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ],
  uploadDir: process.env.UPLOAD_DIR || "public/uploads",
  baseUrl: "/uploads",
};

// 文件类型映射
const FILE_TYPE_MAP: Record<string, string> = {
  "image/jpeg": "jpg",
  "image/jpg": "jpg",
  "image/png": "png",
  "image/gif": "gif",
  "image/webp": "webp",
  "application/pdf": "pdf",
  "application/msword": "doc",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "docx",
  "application/vnd.ms-excel": "xls",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
};

// 生成唯一文件名
function generateFileName(originalName: string, mimeType: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension =
    FILE_TYPE_MAP[mimeType] || path.extname(originalName).slice(1);
  return `${timestamp}_${random}.${extension}`;
}

// 确保上传目录存在
async function ensureUploadDir(uploadPath: string) {
  if (!existsSync(uploadPath)) {
    await mkdir(uploadPath, { recursive: true });
  }
}

// 获取文件分类目录
function getFileCategory(mimeType: string): string {
  if (mimeType.startsWith("image/")) {
    return "images";
  } else if (mimeType.includes("pdf")) {
    return "documents";
  } else if (mimeType.includes("word") || mimeType.includes("excel")) {
    return "documents";
  } else {
    return "others";
  }
}

// POST - 上传文件
export async function POST(request: NextRequest) {
  try {
    const user = validateToken(request);
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "请选择要上传的文件",
          data: null,
        },
        { status: 400 }
      );
    }

    // 验证文件大小
    if (file.size > UPLOAD_CONFIG.maxFileSize) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: `文件大小不能超过${
            UPLOAD_CONFIG.maxFileSize / 1024 / 1024
          }MB`,
          data: null,
        },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!UPLOAD_CONFIG.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, code: 400, message: "不支持的文件类型", data: null },
        { status: 400 }
      );
    }

    // 生成文件名和路径
    const fileName = generateFileName(file.name, file.type);
    const category = getFileCategory(file.type);
    const dateFolder = new Date().toISOString().slice(0, 7); // YYYY-MM

    const relativePath = `${category}/${dateFolder}`;
    const uploadPath = path.join(
      process.cwd(),
      UPLOAD_CONFIG.uploadDir,
      relativePath
    );
    const filePath = path.join(uploadPath, fileName);

    // 确保目录存在
    await ensureUploadDir(uploadPath);

    // 保存文件
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // 构建文件URL
    const fileUrl = `${UPLOAD_CONFIG.baseUrl}/${relativePath}/${fileName}`;

    // 返回文件信息
    const fileInfo = {
      id: Date.now().toString(),
      name: file.name,
      fileName,
      size: file.size,
      type: file.type,
      category,
      url: fileUrl,
      path: relativePath,
      uploadedBy: user?.id,
      uploadedAt: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      code: 200,
      message: "文件上传成功",
      data: fileInfo,
    });
  } catch (error) {
    console.error("文件上传错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "文件上传失败", data: null },
      { status: 500 }
    );
  }
}

// GET - 获取上传配置信息
export async function GET(request: NextRequest) {
  try {
    const config = {
      maxFileSize: UPLOAD_CONFIG.maxFileSize,
      maxFileSizeMB: UPLOAD_CONFIG.maxFileSize / 1024 / 1024,
      allowedTypes: UPLOAD_CONFIG.allowedTypes,
      allowedExtensions: Object.values(FILE_TYPE_MAP),
    };

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取上传配置成功",
      data: config,
    });
  } catch (error) {
    console.error("获取上传配置错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
