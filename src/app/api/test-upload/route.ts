import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir } from "fs/promises";
import { existsSync } from "fs";
import path from "path";

// 测试上传API - 用于诊断上传问题
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: "请选择要上传的文件",
          debug: {
            formDataKeys: Array.from(formData.keys()),
          },
        },
        { status: 400 }
      );
    }

    // 获取上传目录配置
    const uploadDir = process.env.UPLOAD_DIR || "/app/uploads";
    const testDir = path.join(uploadDir, "test");

    // 确保目录存在
    if (!existsSync(testDir)) {
      await mkdir(testDir, { recursive: true });
    }

    // 生成文件名
    const timestamp = Date.now();
    const fileName = `test_${timestamp}_${file.name}`;
    const filePath = path.join(testDir, fileName);

    // 保存文件
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // 构建访问URL
    const fileUrl = `/uploads/test/${fileName}`;

    return NextResponse.json({
      success: true,
      message: "测试文件上传成功",
      data: {
        fileName,
        originalName: file.name,
        size: file.size,
        type: file.type,
        url: fileUrl,
        fullPath: filePath,
      },
      debug: {
        uploadDir,
        testDir,
        filePath,
        fileExists: existsSync(filePath),
        env: {
          NODE_ENV: process.env.NODE_ENV,
          UPLOAD_DIR: process.env.UPLOAD_DIR,
        },
      },
    });
  } catch (error) {
    console.error("测试上传错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "测试上传失败",
        error: error instanceof Error ? error.message : "未知错误",
        debug: {
          uploadDir: process.env.UPLOAD_DIR,
          cwd: process.cwd(),
        },
      },
      { status: 500 }
    );
  }
}

// GET - 获取上传目录信息
export async function GET() {
  try {
    const uploadDir = process.env.UPLOAD_DIR || "/app/uploads";
    const testDir = path.join(uploadDir, "test");

    return NextResponse.json({
      success: true,
      message: "获取上传配置成功",
      data: {
        uploadDir,
        testDir,
        uploadDirExists: existsSync(uploadDir),
        testDirExists: existsSync(testDir),
        cwd: process.cwd(),
        env: {
          NODE_ENV: process.env.NODE_ENV,
          UPLOAD_DIR: process.env.UPLOAD_DIR,
        },
      },
    });
  } catch (error) {
    console.error("获取上传配置错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "获取上传配置失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
