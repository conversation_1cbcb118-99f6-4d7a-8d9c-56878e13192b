import { NextRequest, NextResponse } from 'next/server';
import { ErrorInfo } from '@/utils/errorHandler';

// 错误报告存储（实际项目中应该使用数据库）
const errorReports: ErrorInfo[] = [];

// POST - 上报错误
export async function POST(request: NextRequest) {
  try {
    const errorInfo: ErrorInfo = await request.json();
    
    // 验证错误信息
    if (!errorInfo.message || !errorInfo.type || !errorInfo.level) {
      return NextResponse.json(
        { success: false, message: '错误信息不完整' },
        { status: 400 }
      );
    }

    // 添加服务器端信息
    const enhancedError: ErrorInfo = {
      ...errorInfo,
      timestamp: Date.now(),
      serverInfo: {
        userAgent: request.headers.get('user-agent') || '',
        ip: request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown',
        referer: request.headers.get('referer') || '',
      },
    };

    // 存储错误报告
    errorReports.push(enhancedError);

    // 保持错误报告数量在合理范围内
    if (errorReports.length > 1000) {
      errorReports.splice(0, errorReports.length - 1000);
    }

    // 在生产环境中，这里可以：
    // 1. 发送到错误监控服务（如 Sentry）
    // 2. 存储到数据库
    // 3. 发送邮件通知
    // 4. 记录到日志文件

    console.error('Client Error Report:', enhancedError);

    return NextResponse.json({
      success: true,
      message: '错误报告已收到',
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    });
  } catch (error) {
    console.error('Error reporting failed:', error);
    return NextResponse.json(
      { success: false, message: '错误报告失败' },
      { status: 500 }
    );
  }
}

// GET - 获取错误报告（仅开发环境）
export async function GET(request: NextRequest) {
  // 仅在开发环境允许查看错误报告
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { success: false, message: '仅开发环境可用' },
      { status: 403 }
    );
  }

  const { searchParams } = new URL(request.url);
  const limit = parseInt(searchParams.get('limit') || '50');
  const type = searchParams.get('type');
  const level = searchParams.get('level');

  let filteredErrors = [...errorReports];

  // 按类型过滤
  if (type) {
    filteredErrors = filteredErrors.filter(error => error.type === type);
  }

  // 按级别过滤
  if (level) {
    filteredErrors = filteredErrors.filter(error => error.level === level);
  }

  // 按时间排序（最新的在前）
  filteredErrors.sort((a, b) => b.timestamp - a.timestamp);

  // 限制数量
  filteredErrors = filteredErrors.slice(0, limit);

  return NextResponse.json({
    success: true,
    data: {
      errors: filteredErrors,
      total: errorReports.length,
      filtered: filteredErrors.length,
    },
  });
}
