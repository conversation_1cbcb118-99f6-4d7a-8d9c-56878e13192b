import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";
import { z } from "zod";
import { cleanObject } from "@/utils/cleanObject";
import { validateRequest } from "@/lib/validate";
import redis from "@/lib/redis";
import { redisKey } from "@/config/redisKey";

const createReviewSchema = z.object({
    shopId: z.coerce.number().min(1, "店铺ID不能为空"),
    shopUserId: z.coerce.number().min(1, "用户ID不能为空"),
    rating: z.coerce.number().min(1, "评分不能为空"),
    comment: z.string().optional(),
    images: z.array(z.string()).optional(),
    orderId: z.string().optional(),
    isHidden: z.boolean().optional(),
});

export type CreateReviewBody = z.infer<typeof createReviewSchema>;

const getReviewSchema = z.object({
    page: z.string().transform(Number).default(1),
    pageSize: z.string().transform(Number).default(10),
    keyword: z.string().optional(),
    status: z.string().optional(),
    shopId: z.coerce.number().min(1, "店铺ID不能为空"),
    shopUserId: z.string().optional(),
});

// GET - 获取评论列表
export async function GET(request: NextRequest) {
    try {
        const validationResult = await validateRequest(request, getReviewSchema);

        const { page, pageSize, keyword, status, shopId, shopUserId } =
            validationResult;
        const cacheKey = redisKey.shop.reviewList(shopId, page, pageSize);

        const cachedData = await redis.get(cacheKey);
        if (cachedData) {
            return NextResponse.json(JSON.parse(cachedData));
        }
        // 过滤数据
        const where = cleanObject({
            comment: {
                contains: keyword,
            },
            isHidden: status ? (status as any) : undefined,
            shopId: shopId ? Number(shopId) : undefined,
            shopUserId: shopUserId ? Number(shopUserId) : undefined,
        });

        const filteredReviews = await prisma.review.findMany({
            where,
            include: {
                shopUser: true,
                shop: true,
                order: true,
            },
            skip: (page - 1) * pageSize,
            take: pageSize,
        });
        const res = NextResponse.json({
            success: true,
            code: 200,
            message: "获取评论列表成功",
            data: {
                list: filteredReviews,
                pagination: {
                    current: page,
                    pageSize,
                    total: filteredReviews.length,
                    totalPages: Math.ceil(filteredReviews.length / pageSize),
                },
            },
        });
        await redis.set(cacheKey, JSON.stringify(res), "EX", 60);

        return res
    } catch (error) {
        console.error("获取评论列表错误:", error);
        return NextResponse.json(
            { success: false, code: 500, message: "服务器内部错误", data: null },
            { status: 500 }
        );
    }
}

// POST - 创建新评论
export async function POST(request: NextRequest) {
    try {
        const validationResult = await validateRequest(request, createReviewSchema);
        const { shopId, shopUserId, rating, comment, images, orderId, isHidden } =
            validationResult;

        const baseUrl =
            process.env.NEXT_PUBLIC_API_URL || // 优先使用环境变量
            `${request.nextUrl.protocol}//${request.nextUrl.host}`;
        // 创建新评论
        const newReview = {
            shopId,
            shopUserId,
            rating,
            comment,
            images: images?.map((item) => `${baseUrl}${item}`),
            orderId,
            isHidden,
        };

        // 1. 验证订单
        // 检查订单是否已存在
        const order = await prisma.order.findUnique({
            where: {
                id: orderId,
            },
            include: {
                consumeOrder: true,
            },
        });
        if (!order || order.shopUserId !== shopUserId || order.shopId !== shopId) {
            return NextResponse.json(
                { message: "无效订单", code: 400 },
                { status: 400 }
            );
        }

        if (order.status !== "COMPLETED") {
            return NextResponse.json(
                { message: "订单未完成，无法评价", code: 400 },
                { status: 400 }
            );
        }

        if (order.consumeOrder?.isReviewed) {
            return NextResponse.json(
                { message: "该订单已评价", code: 400 },
                { status: 400 }
            );
        }

        // 2. 创建评价并更新订单与店铺统计（事务）
        const result = await prisma.$transaction(async (tx) => {
            const review = await tx.review.create({
                data: newReview,
            });

            await tx.order.update({
                where: {
                    id: orderId,
                },
                data: {
                    consumeOrder: {
                        update: {
                            isReviewed: true,
                        },
                    },
                },
            });

            // 重新计算平均值（或使用增量计算）
            const stats = await tx.review.groupBy({
                by: ["shopId"],
                _avg: {
                    rating: true,
                },
                _count: true,
            });

            await tx.shop.update({
                where: {
                    id: shopId,
                },
                data: {
                    avgRating: stats[0]._avg.rating ?? 0,
                    reviewCount: stats[0]._count,
                },
            });

            return review;
        });
        // 3. 清理缓存

        // 更新缓存：删除热门评论、最新评论缓存
        await redis.del(redisKey.shop.hotReviews(shopId));
        await redis.del(redisKey.shop.latestReviews(shopId));
        await redis.del(redisKey.shop.reviewCount(shopId));
        return NextResponse.json({
            success: true,
            code: 200,
            message: "创建评论成功",
            data: result,
        });

    } catch (error) {
        console.error("创建评论错误:", error);
        return NextResponse.json(
            { success: false, code: 500, message: "服务器内部错误", data: null },
            { status: 500 }
        );
    }
}
