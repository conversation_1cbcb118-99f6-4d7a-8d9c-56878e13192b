
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";
import { z } from "zod";
import { cleanObject } from "@/utils/cleanObject";
import { validateRequest } from "@/lib/validate";
import redis from "@/lib/redis";

const getReviewSchema = z.object({
    page: z.string().transform(Number).default(1),
    pageSize: z.string().transform(Number).default(10),
    keyword: z.string().optional(),
    status: z.string().optional(),
    shopId: z.coerce.number().min(1, "店铺ID不能为空"),
    shopUserId: z.string().optional(),
    type: z.enum(["ALL", "NEWEST", "BAD"]).default("ALL"),
});
// GET - 获取评论列表 ALL NEWEST  BAD
export async function GET(request: NextRequest) {
    try {
        const validationResult = await validateRequest(request, getReviewSchema);
        const { page, pageSize, keyword, status, shopId, shopUserId } =
            validationResult;
        // 过滤数据 
        const where = cleanObject({
            comment: {
                contains: keyword,
            },
            isHidden: status ? (status as any) : undefined,
            shopId: shopId ? Number(shopId) : undefined,
            shopUserId: shopUserId ? Number(shopUserId) : undefined,
        });
        const filteredReviews = await prisma.review.findMany({
            where,
            include: {
                shopUser: true,
                shop: true,
                order: true,
            },
            skip: (page - 1) * pageSize,
            take: pageSize,

            // 最新评论 
            orderBy: validationResult.type === "NEWEST" ? {
                createdAt: "desc",
            } : validationResult.type === "BAD" ? {
                rating: "asc",
            } : undefined,
        });

        return NextResponse.json({
            success: true,
            code: 200,
            message: "获取评论列表成功",
            data: {
                list: filteredReviews,
                pagination: {
                    current: page,
                    pageSize,
                    total: filteredReviews.length,
                    totalPages: Math.ceil(filteredReviews.length / pageSize),
                },
            },
        });
    } catch (error) {
        console.error("获取评论列表错误:", error);
        return NextResponse.json(
            { success: false, code: 500, message: "服务器内部错误", data: null },
            { status: 500 }
        );
    }
}
