import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";
import { cleanObject } from "@/utils/cleanObject";
import { validateRequest } from "@/lib/validate";
import { z } from "zod";

const getOrderSchema = z.object({
  keyword: z.string().optional(),
  status: z.enum(["PENDING", "PAID", "FAILED", "REFUNDED"]).optional(),
  type: z.enum(["WASH", "RECHARGE"]).optional(),
  shopUserId: z.string().optional(),
});

// get
export async function GET(req: NextRequest) {
  try {
    const validationResult = await validateRequest(req, getOrderSchema);
    const { keyword = "", status, type, shopUserId } = validationResult;

    // 过滤数据
    // 关联查询
    const filteredOrders = await prisma.order.findFirst({
      where: {
        orderNo: {
          contains: keyword,
        },
        status: status ? (status as any) : undefined,
        type: type ? (type as any) : undefined,
        shopUserId: shopUserId ? Number(shopUserId) : undefined,
      },
      include: {
        consumeOrder: {
          include: {
            car: true,
            station: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取订单列表成功",
      data: filteredOrders,
    });
  } catch (error) {
    console.error("获取订单列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
