import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";
import redis from "@/lib/redis";
import dayjs from "dayjs";

export async function POST(req: NextRequest) {
  try {
    const { id, payMethod } = await req.json();
    if (!id) {
      return NextResponse.json({ code: 400, msg: "参数错误" });
    }
    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
      include: {
        consumeOrder: true,
      },
    });
    if (!targetOrder) {
      return NextResponse.json({ code: 400, msg: "订单不存在" });
    }
    if (targetOrder.status !== "PENDING") {
      return NextResponse.json({ code: 400, msg: "订单状态错误" });
    }
    await prisma.order.update({
      where: {
        id,
      },
      data: {
        status: "COMPLETED",
        payMethod,
        completedAt: new Date(),
      },
    });

    // 更新洗车站为可用
    if (targetOrder.consumeOrder?.stationId) {
      await prisma.station.update({
        where: {
          id: targetOrder.consumeOrder?.stationId,
        },
        data: {
          status: "IDLE",
        },
      });
    }
    return NextResponse.json({ code: 200, msg: "订单完成" });
  } catch (error: any) {
    console.error("完成消费订单失败:", error);
    return NextResponse.json({ code: 500, msg: "完成消费订单失败" });
  }
}
