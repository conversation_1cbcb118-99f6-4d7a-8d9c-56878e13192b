import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";
import { generateOrderNo } from "@/utils/generateOrderNo";

export async function POST(req: NextRequest) {
  try {
    const { shopUserId, amount, payMethod, bonus, campaign } = await req.json();

    if (!shopUserId || !amount) {
      return NextResponse.json({ code: 400, msg: "参数错误" });
    }

    const orderNo = await generateOrderNo("CZ");

    const order = await prisma.order.create({
      data: {
        orderNo,
        shopUserId,
        type: "RECHARGE",
        amount,
        payMethod,
        status: "PAID",
        rechargeOrder: {
          create: {
            bonus: bonus || 0,
            campaign: campaign || null,
          },
        },
      },
      include: { rechargeOrder: true },
    });
    await prisma.shopUser.update({
      where: {
        id: shopUserId,
      },
      data: {
        balance: {
          increment: amount,
        },
      },
    });

    return NextResponse.json({
      code: 200,
      msg: "充值订单创建成功",
      data: order,
    });
  } catch (error: any) {
    console.error("创建充值订单失败:", error);
    return NextResponse.json({ code: 500, msg: "创建充值订单失败" });
  }
}

// get
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const keyword = searchParams.get("keyword") || "";
    const status = searchParams.get("status") || "";
    const carId = searchParams.get("carId") || "";
    const stationId = searchParams.get("stationId") || ""; // 商户用户ID
    const shopUserId = searchParams.get("shopUserId") || "";  // 商户用户ID

    // 过滤数据
    // 关联查询
    const filteredOrders = await prisma.order.findMany({
      where: {
        type: "RECHARGE",
        orderNo: {
          contains: keyword,
        },
        status: status ? (status as any) : undefined,
      },
      include: {
        rechargeOrder: {
          include: {
            shopUser: {
              where: {
                id: shopUserId ? Number(shopUserId) : undefined,
              },
            },
          },
        },
        consumeOrder: {
          include: {
            car: true,
            station: true,
          },
          where: {
            stationId: stationId ? Number(stationId) : undefined,
            carId: carId ? Number(carId) : undefined,
          },
        },
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 分页
    const total = filteredOrders.length;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取订单列表成功",
      data: {
        list: filteredOrders,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取订单列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
