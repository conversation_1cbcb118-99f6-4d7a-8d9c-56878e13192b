import { NextRequest, NextResponse } from 'next/server';

// 权限数据类型
interface Permission {
  id: string;
  name: string;
  code: string;
  category: string;
  description: string;
  type: 'menu' | 'button' | 'api';
  parentId?: string;
  sort: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 模拟权限数据
const mockPermissions: Permission[] = [
  // 仪表盘权限
  {
    id: 'dashboard',
    name: '仪表盘',
    code: 'dashboard:read',
    category: '仪表盘',
    description: '查看仪表盘数据和统计信息',
    type: 'menu',
    sort: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  
  // 用户管理权限
  {
    id: 'users',
    name: '用户管理',
    code: 'users:read',
    category: '用户管理',
    description: '查看用户列表和用户信息',
    type: 'menu',
    sort: 2,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'users_create',
    name: '创建用户',
    code: 'users:create',
    category: '用户管理',
    description: '创建新用户账号',
    type: 'button',
    parentId: 'users',
    sort: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'users_update',
    name: '编辑用户',
    code: 'users:update',
    category: '用户管理',
    description: '编辑用户信息',
    type: 'button',
    parentId: 'users',
    sort: 2,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'users_delete',
    name: '删除用户',
    code: 'users:delete',
    category: '用户管理',
    description: '删除用户账号',
    type: 'button',
    parentId: 'users',
    sort: 3,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  
  // 角色管理权限
  {
    id: 'roles',
    name: '角色管理',
    code: 'roles:read',
    category: '角色管理',
    description: '查看角色列表和角色信息',
    type: 'menu',
    sort: 3,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'roles_create',
    name: '创建角色',
    code: 'roles:create',
    category: '角色管理',
    description: '创建新角色',
    type: 'button',
    parentId: 'roles',
    sort: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'roles_update',
    name: '编辑角色',
    code: 'roles:update',
    category: '角色管理',
    description: '编辑角色信息',
    type: 'button',
    parentId: 'roles',
    sort: 2,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'roles_delete',
    name: '删除角色',
    code: 'roles:delete',
    category: '角色管理',
    description: '删除角色',
    type: 'button',
    parentId: 'roles',
    sort: 3,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'roles_assign',
    name: '分配权限',
    code: 'roles:assign',
    category: '角色管理',
    description: '为角色分配权限',
    type: 'button',
    parentId: 'roles',
    sort: 4,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  
  // 内容管理权限
  {
    id: 'content',
    name: '内容管理',
    code: 'content:read',
    category: '内容管理',
    description: '查看内容列表',
    type: 'menu',
    sort: 4,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'content_create',
    name: '创建内容',
    code: 'content:create',
    category: '内容管理',
    description: '创建新内容',
    type: 'button',
    parentId: 'content',
    sort: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'content_update',
    name: '编辑内容',
    code: 'content:update',
    category: '内容管理',
    description: '编辑内容',
    type: 'button',
    parentId: 'content',
    sort: 2,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'content_delete',
    name: '删除内容',
    code: 'content:delete',
    category: '内容管理',
    description: '删除内容',
    type: 'button',
    parentId: 'content',
    sort: 3,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'content_publish',
    name: '发布内容',
    code: 'content:publish',
    category: '内容管理',
    description: '发布或下架内容',
    type: 'button',
    parentId: 'content',
    sort: 4,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  
  // 系统设置权限
  {
    id: 'system',
    name: '系统设置',
    code: 'system:read',
    category: '系统设置',
    description: '查看系统设置',
    type: 'menu',
    sort: 5,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'system_update',
    name: '修改设置',
    code: 'system:update',
    category: '系统设置',
    description: '修改系统设置',
    type: 'button',
    parentId: 'system',
    sort: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'system_logs',
    name: '系统日志',
    code: 'system:logs',
    category: '系统设置',
    description: '查看系统日志',
    type: 'menu',
    parentId: 'system',
    sort: 2,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

// 验证token
function validateToken(request: NextRequest) {
  const authorization = request.headers.get('authorization');
  
  if (!authorization || !authorization.startsWith('Bearer ')) {
    return null;
  }

  const token = authorization.replace('Bearer ', '');
  
  try {
    if (token.startsWith('mock-token-')) {
      const parts = token.split('-');
      const userId = parseInt(parts[2]);
      return { id: userId };
    }
    return null;
  } catch (error) {
    return null;
  }
}

// 检查用户权限
function hasPermission(userId: number, permission: string): boolean {
  if (userId === 1) return true;
  
  const userPermissions: Record<number, string[]> = {
    2: ['roles:read', 'roles:assign'], // 管理员可以查看和分配权限
    3: ['roles:read'], // 编辑员只能查看
    4: [], // 普通用户无权限
  };
  
  return userPermissions[userId]?.includes(permission) || false;
}

// 构建权限树结构
function buildPermissionTree(permissions: Permission[]): Permission[] {
  const permissionMap = new Map<string, Permission & { children?: Permission[] }>();
  const rootPermissions: (Permission & { children?: Permission[] })[] = [];

  // 创建权限映射
  permissions.forEach(permission => {
    permissionMap.set(permission.id, { ...permission, children: [] });
  });

  // 构建树结构
  permissions.forEach(permission => {
    const permissionNode = permissionMap.get(permission.id)!;
    
    if (permission.parentId) {
      const parent = permissionMap.get(permission.parentId);
      if (parent) {
        parent.children!.push(permissionNode);
      }
    } else {
      rootPermissions.push(permissionNode);
    }
  });

  return rootPermissions;
}

// GET - 获取权限列表
export async function GET(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    if (!hasPermission(user.id, 'roles:read')) {
      return NextResponse.json(
        { success: false, code: 403, message: '权限不足', data: null },
        { status: 403 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const tree = searchParams.get('tree') === 'true';
    const category = searchParams.get('category') || '';
    const type = searchParams.get('type') || '';

    // 过滤权限
    let filteredPermissions = mockPermissions.filter(p => p.status === 'active');

    if (category) {
      filteredPermissions = filteredPermissions.filter(p => p.category === category);
    }

    if (type) {
      filteredPermissions = filteredPermissions.filter(p => p.type === type);
    }

    // 如果需要树结构
    if (tree) {
      const permissionTree = buildPermissionTree(filteredPermissions);
      return NextResponse.json({
        success: true,
        code: 200,
        message: '获取权限树成功',
        data: permissionTree,
      });
    }

    // 按分类分组
    const groupedPermissions = filteredPermissions.reduce((groups, permission) => {
      const category = permission.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(permission);
      return groups;
    }, {} as Record<string, Permission[]>);

    return NextResponse.json({
      success: true,
      code: 200,
      message: '获取权限列表成功',
      data: {
        list: filteredPermissions,
        grouped: groupedPermissions,
        categories: [...new Set(filteredPermissions.map(p => p.category))],
      },
    });
  } catch (error) {
    console.error('获取权限列表错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
