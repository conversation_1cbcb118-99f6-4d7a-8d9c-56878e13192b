import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";

// GET - 获取车辆列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const keyword = searchParams.get("keyword") || "";
    const status = searchParams.get("status") || "";
    const shopUserId = searchParams.get("shopUserId") || "";

    const filteredCars = await prisma.car.findMany({
      where: {
        plateNumber: {
          contains: keyword,
        },
        status: status ? Number(status) : undefined,
        shopUserId: shopUserId ? Number(shopUserId) : undefined,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 分页
    const total = filteredCars.length;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取车辆列表成功",
      data: {
        list: filteredCars,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取车辆列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新车辆
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, plateNumber, status = 1, shopUserId } = body;

    // 验证必填字段
    if (!name || !plateNumber || !shopUserId) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "车辆名称、车牌号和商户用户ID为必填项",
          data: null,
        },
        { status: 400 }
      );
    }

    // 检查车牌号是否已存在
    const existingCar = await prisma.car.findFirst({
      where: {
        plateNumber: plateNumber,
      },
    });
    if (existingCar) {
      return NextResponse.json(
        { success: false, code: 400, message: "车牌号已存在", data: null },
        { status: 400 }
      );
    }

    // 创建新车辆
    const newCar = {
      name,
      description,
      plateNumber,
      status: Number(status),
    };
    await prisma.car.create({
      data: {
        ...newCar,
        shopUserId: Number(shopUserId),
      },
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "创建车辆成功",
      data: newCar,
    });
  } catch (error) {
    console.error("创建车辆错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
