import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";
import { z } from "zod";
import { validateRequest } from "@/lib/validate";
import { cleanObject } from "@/utils/cleanObject";

// GET - 获取单个车辆信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const carId = parseInt(id);
    const targetCar = await prisma.car.findUnique({
      where: {
        id: carId,
      },
    });

    if (!targetCar) {
      return NextResponse.json(
        { success: false, code: 404, message: "车辆不存在", data: null },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取车辆信息成功",
      data: targetCar,
    });
  } catch (error) {
    console.error("获取车辆信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

const updateCarSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  plateNumber: z.string().optional(),
  status: z.number().optional(),
  shopUserId: z.number().optional(),
});

// PUT - 更新车辆信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const carId = parseInt(id);
    const targetCar = await prisma.car.findUnique({
      where: {
        id: carId,
      },
    });

    if (!targetCar) {
      return NextResponse.json(
        { success: false, code: 404, message: "车辆不存在", data: null },
        { status: 404 }
      );
    }

    let updatedCar = await validateRequest(request, updateCarSchema);
    updatedCar = cleanObject(updatedCar);
    await prisma.car.update({
      where: {
        id: carId,
      },
      data: { ...updatedCar, updatedAt: new Date() },
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新车辆信息成功",
      data: updatedCar,
    });
  } catch (error) {
    console.error("更新车辆信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除车辆
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const carId = parseInt(id);

    // 不允许删除管理员账户
    if (carId === 1) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "不能删除管理员账户",
          data: null,
        },
        { status: 400 }
      );
    }

    const carIndex = await prisma.car.findUnique({
      where: {
        id: carId,
      },
    });
    if (!carIndex) {
      return NextResponse.json(
        { success: false, code: 404, message: "车辆不存在", data: null },
        { status: 404 }
      );
    }

    await prisma.car.delete({
      where: {
        id: carId,
      },
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除车辆成功",
      data: carIndex,
    });
  } catch (error) {
    console.error("删除车辆错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
