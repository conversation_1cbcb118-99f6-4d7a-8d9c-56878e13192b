import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";

// 用户数据类型
interface User {
  id: number;
  username: string;
  nickname: string;
  email: string;
  phone?: string;
  avatar?: string;
  status: "active" | "inactive" | "banned";
  roles: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginTime?: string;
}

// 模拟用户数据（实际项目中应该从数据库获取）
const mockUsers: User[] = [
  {
    id: 1,
    username: "admin",
    nickname: "系统管理员",
    email: "<EMAIL>",
    phone: "13800138000",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=admin",
    status: "active",
    roles: ["admin", "super_admin"],
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
    lastLoginTime: "2024-01-31T08:45:00Z",
  },
  {
    id: 2,
    username: "user",
    nickname: "普通用户",
    email: "<EMAIL>",
    phone: "13800138001",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=user",
    status: "active",
    roles: ["user"],
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-20T14:20:00Z",
    lastLoginTime: "2024-01-30T16:30:00Z",
  },
  {
    id: 3,
    username: "editor",
    nickname: "内容编辑",
    email: "<EMAIL>",
    phone: "13800138002",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=editor",
    status: "active",
    roles: ["editor", "user"],
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-25T09:15:00Z",
    lastLoginTime: "2024-01-29T11:20:00Z",
  },
  {
    id: 4,
    username: "manager",
    nickname: "部门经理",
    email: "<EMAIL>",
    phone: "13800138003",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=manager",
    status: "active",
    roles: ["manager", "user"],
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-28T16:45:00Z",
    lastLoginTime: "2024-01-31T09:10:00Z",
  },
  {
    id: 5,
    username: "guest",
    nickname: "访客用户",
    email: "<EMAIL>",
    phone: "13800138004",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=guest",
    status: "inactive",
    roles: ["guest"],
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-10T12:00:00Z",
    lastLoginTime: "2024-01-15T14:30:00Z",
  },
];

// GET - 获取单个用户信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const userId = parseInt(id);
    // const targetUser = mockUsers.find((u) => u.id === userId);
    const targetUser = await prisma.business.findUnique({
      where: {
        id: userId,
      },
    });

    if (!targetUser) {
      return NextResponse.json(
        { success: false, code: 404, message: "用户不存在", data: null },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取用户信息成功",
      data: targetUser,
    });
  } catch (error) {
    console.error("获取用户信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// PUT - 更新用户信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const userId = parseInt(id);
    // const userIndex = mockUsers.findIndex((u) => u.id === userId);
    const userIndex = await prisma.business.findUnique({
      where: {
        id: userId,
      },
    });

    if (!userIndex) {
      return NextResponse.json(
        { success: false, code: 404, message: "用户不存在", data: null },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { nickname, email, phone, roles, status } = body;

    // 更新用户信息
    const updatedUser = {
      ...userIndex,
      ...(nickname && { nickname }),
      ...(email && { email }),
      ...(phone !== undefined && { phone }),
      ...(roles && { roles }),
      ...(status && { status }),
      updatedAt: new Date().toISOString(),
    };

    // mockUsers[userIndex] = updatedUser;
    await prisma.business.update({
      where: {
        id: userId,
      },
      data: updatedUser,
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新用户信息成功",
      data: updatedUser,
    });
  } catch (error) {
    console.error("更新用户信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除用户
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const userId = parseInt(id);

    // 不允许删除管理员账户
    if (userId === 1) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "不能删除管理员账户",
          data: null,
        },
        { status: 400 }
      );
    }

    const userIndex = mockUsers.findIndex((u) => u.id === userId);

    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, code: 404, message: "用户不存在", data: null },
        { status: 404 }
      );
    }

    const deletedUser = mockUsers.splice(userIndex, 1)[0];

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除用户成功",
      data: deletedUser,
    });
  } catch (error) {
    console.error("删除用户错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
