import { NextRequest, NextResponse } from "next/server";

// 角色数据类型
interface Role {
  id: number;
  name: string;
  code: string;
  description: string;
  permissions: string[];
  status: "active" | "inactive";
  isSystem: boolean;
  userCount: number;
  createdAt: string;
  updatedAt: string;
}

// 模拟角色数据（实际项目中应该从数据库获取）
const mockRoles: Role[] = [
  {
    id: 1,
    name: "超级管理员",
    code: "super_admin",
    description: "拥有系统所有权限的超级管理员",
    permissions: ["*"], // 所有权限
    status: "active",
    isSystem: true,
    userCount: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "管理员",
    code: "admin",
    description: "系统管理员，拥有大部分管理权限",
    permissions: [
      "dashboard:read",
      "users:read",
      "users:create",
      "users:update",
      "roles:read",
      "content:read",
      "content:create",
      "content:update",
      "content:delete",
      "content:publish",
      "system:read",
    ],
    status: "active",
    isSystem: false,
    userCount: 2,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: 3,
    name: "编辑员",
    code: "editor",
    description: "内容编辑员，负责内容的创建和编辑",
    permissions: [
      "dashboard:read",
      "content:read",
      "content:create",
      "content:update",
    ],
    status: "active",
    isSystem: false,
    userCount: 5,
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-20T14:20:00Z",
  },
  {
    id: 4,
    name: "普通用户",
    code: "user",
    description: "普通用户，只能查看基本信息",
    permissions: ["dashboard:read"],
    status: "active",
    isSystem: false,
    userCount: 50,
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-25T09:15:00Z",
  },
];

// GET - 获取单个角色信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const roleId = parseInt(id);
    const role = mockRoles.find((r) => r.id === roleId);

    if (!role) {
      return NextResponse.json(
        { success: false, code: 404, message: "角色不存在", data: null },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取角色信息成功",
      data: role,
    });
  } catch (error) {
    console.error("获取角色信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// PUT - 更新角色信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const roleId = parseInt(id);
    const roleIndex = mockRoles.findIndex((r) => r.id === roleId);

    if (roleIndex === -1) {
      return NextResponse.json(
        { success: false, code: 404, message: "角色不存在", data: null },
        { status: 404 }
      );
    }

    const role = mockRoles[roleIndex];

    // 不允许修改系统角色
    if (role.isSystem) {
      return NextResponse.json(
        { success: false, code: 400, message: "不能修改系统角色", data: null },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, description, permissions, status } = body;

    // 更新角色信息
    const updatedRole = {
      ...role,
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(permissions && { permissions }),
      ...(status && { status }),
      updatedAt: new Date().toISOString(),
    };

    mockRoles[roleIndex] = updatedRole;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新角色信息成功",
      data: updatedRole,
    });
  } catch (error) {
    console.error("更新角色信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除角色
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const roleId = parseInt(id);
    const roleIndex = mockRoles.findIndex((r) => r.id === roleId);

    if (roleIndex === -1) {
      return NextResponse.json(
        { success: false, code: 404, message: "角色不存在", data: null },
        { status: 404 }
      );
    }

    const role = mockRoles[roleIndex];

    // 不允许删除系统角色
    if (role.isSystem) {
      return NextResponse.json(
        { success: false, code: 400, message: "不能删除系统角色", data: null },
        { status: 400 }
      );
    }

    // 检查是否有用户使用该角色
    if (role.userCount > 0) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "该角色下还有用户，不能删除",
          data: null,
        },
        { status: 400 }
      );
    }

    const deletedRole = mockRoles.splice(roleIndex, 1)[0];

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除角色成功",
      data: deletedRole,
    });
  } catch (error) {
    console.error("删除角色错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
