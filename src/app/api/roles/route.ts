import { NextRequest, NextResponse } from 'next/server';

// 角色数据类型
interface Role {
  id: number;
  name: string;
  code: string;
  description: string;
  permissions: string[];
  status: 'active' | 'inactive';
  isSystem: boolean;
  userCount: number;
  createdAt: string;
  updatedAt: string;
}

// 权限数据类型
interface Permission {
  id: string;
  name: string;
  code: string;
  category: string;
  description: string;
  type: 'menu' | 'button' | 'api';
}

// 模拟权限数据
const mockPermissions: Permission[] = [
  // 仪表盘权限
  { id: 'dashboard', name: '仪表盘', code: 'dashboard:read', category: '仪表盘', description: '查看仪表盘', type: 'menu' },
  
  // 用户管理权限
  { id: 'users', name: '用户管理', code: 'users:read', category: '用户管理', description: '查看用户列表', type: 'menu' },
  { id: 'users_create', name: '创建用户', code: 'users:create', category: '用户管理', description: '创建新用户', type: 'button' },
  { id: 'users_update', name: '编辑用户', code: 'users:update', category: '用户管理', description: '编辑用户信息', type: 'button' },
  { id: 'users_delete', name: '删除用户', code: 'users:delete', category: '用户管理', description: '删除用户', type: 'button' },
  
  // 角色管理权限
  { id: 'roles', name: '角色管理', code: 'roles:read', category: '角色管理', description: '查看角色列表', type: 'menu' },
  { id: 'roles_create', name: '创建角色', code: 'roles:create', category: '角色管理', description: '创建新角色', type: 'button' },
  { id: 'roles_update', name: '编辑角色', code: 'roles:update', category: '角色管理', description: '编辑角色信息', type: 'button' },
  { id: 'roles_delete', name: '删除角色', code: 'roles:delete', category: '角色管理', description: '删除角色', type: 'button' },
  { id: 'roles_assign', name: '分配权限', code: 'roles:assign', category: '角色管理', description: '为角色分配权限', type: 'button' },
  
  // 内容管理权限
  { id: 'content', name: '内容管理', code: 'content:read', category: '内容管理', description: '查看内容列表', type: 'menu' },
  { id: 'content_create', name: '创建内容', code: 'content:create', category: '内容管理', description: '创建新内容', type: 'button' },
  { id: 'content_update', name: '编辑内容', code: 'content:update', category: '内容管理', description: '编辑内容', type: 'button' },
  { id: 'content_delete', name: '删除内容', code: 'content:delete', category: '内容管理', description: '删除内容', type: 'button' },
  { id: 'content_publish', name: '发布内容', code: 'content:publish', category: '内容管理', description: '发布/下架内容', type: 'button' },
  
  // 系统设置权限
  { id: 'system', name: '系统设置', code: 'system:read', category: '系统设置', description: '查看系统设置', type: 'menu' },
  { id: 'system_update', name: '修改设置', code: 'system:update', category: '系统设置', description: '修改系统设置', type: 'button' },
  { id: 'system_logs', name: '系统日志', code: 'system:logs', category: '系统设置', description: '查看系统日志', type: 'menu' },
];

// 模拟角色数据
const mockRoles: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'super_admin',
    description: '拥有系统所有权限的超级管理员',
    permissions: mockPermissions.map(p => p.code),
    status: 'active',
    isSystem: true,
    userCount: 1,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    name: '管理员',
    code: 'admin',
    description: '系统管理员，拥有大部分管理权限',
    permissions: [
      'dashboard:read',
      'users:read', 'users:create', 'users:update',
      'roles:read',
      'content:read', 'content:create', 'content:update', 'content:delete', 'content:publish',
      'system:read',
    ],
    status: 'active',
    isSystem: false,
    userCount: 2,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 3,
    name: '编辑员',
    code: 'editor',
    description: '内容编辑员，负责内容的创建和编辑',
    permissions: [
      'dashboard:read',
      'content:read', 'content:create', 'content:update',
    ],
    status: 'active',
    isSystem: false,
    userCount: 5,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-20T14:20:00Z',
  },
  {
    id: 4,
    name: '普通用户',
    code: 'user',
    description: '普通用户，只能查看基本信息',
    permissions: [
      'dashboard:read',
    ],
    status: 'active',
    isSystem: false,
    userCount: 50,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-25T09:15:00Z',
  },
  {
    id: 5,
    name: '访客',
    code: 'guest',
    description: '访客用户，权限受限',
    permissions: [],
    status: 'inactive',
    isSystem: false,
    userCount: 10,
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-10T12:00:00Z',
  },
];

// 验证token
function validateToken(request: NextRequest) {
  const authorization = request.headers.get('authorization');
  
  if (!authorization || !authorization.startsWith('Bearer ')) {
    return null;
  }

  const token = authorization.replace('Bearer ', '');
  
  try {
    if (token.startsWith('mock-token-')) {
      const parts = token.split('-');
      const userId = parseInt(parts[2]);
      return { id: userId };
    }
    return null;
  } catch (error) {
    return null;
  }
}

// 检查用户权限
function hasPermission(userId: number, permission: string): boolean {
  // 超级管理员拥有所有权限
  if (userId === 1) return true;
  
  // 其他用户的权限检查
  const userPermissions: Record<number, string[]> = {
    2: ['roles:read'], // 普通管理员只能查看
    3: ['roles:read'], // 编辑员只能查看
    4: [], // 普通用户无权限
  };
  
  return userPermissions[userId]?.includes(permission) || false;
}

// GET - 获取角色列表
export async function GET(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    if (!hasPermission(user.id, 'roles:read')) {
      return NextResponse.json(
        { success: false, code: 403, message: '权限不足', data: null },
        { status: 403 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const keyword = searchParams.get('keyword') || '';
    const status = searchParams.get('status') || '';

    // 过滤数据
    let filteredRoles = mockRoles;

    if (keyword) {
      filteredRoles = filteredRoles.filter(role => 
        role.name.includes(keyword) ||
        role.code.includes(keyword) ||
        role.description.includes(keyword)
      );
    }

    if (status) {
      filteredRoles = filteredRoles.filter(role => role.status === status);
    }

    // 分页
    const total = filteredRoles.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedRoles = filteredRoles.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      code: 200,
      message: '获取角色列表成功',
      data: {
        list: paginatedRoles,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error('获取角色列表错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新角色
export async function POST(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    if (!hasPermission(user.id, 'roles:create')) {
      return NextResponse.json(
        { success: false, code: 403, message: '权限不足', data: null },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, code, description, permissions = [], status = 'active' } = body;

    // 验证必填字段
    if (!name || !code) {
      return NextResponse.json(
        { success: false, code: 400, message: '角色名称和代码为必填项', data: null },
        { status: 400 }
      );
    }

    // 检查角色代码是否已存在
    const existingRole = mockRoles.find(r => r.code === code);
    if (existingRole) {
      return NextResponse.json(
        { success: false, code: 400, message: '角色代码已存在', data: null },
        { status: 400 }
      );
    }

    // 创建新角色
    const newRole: Role = {
      id: Math.max(...mockRoles.map(r => r.id)) + 1,
      name,
      code,
      description: description || '',
      permissions,
      status,
      isSystem: false,
      userCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockRoles.push(newRole);

    return NextResponse.json({
      success: true,
      code: 200,
      message: '创建角色成功',
      data: newRole,
    });
  } catch (error) {
    console.error('创建角色错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
