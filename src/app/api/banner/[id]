import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";

import { bannnerCacheKey, refreshBannerCache } from "@/lib/bannerCache";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const bannerId = parseInt(id);

    const targetBanner = await prisma.banner.findUnique({
      where: {
        id: bannerId,
      },
    });

    if (!targetBanner) {
      return NextResponse.json(
        { success: false, code: 404, message: "banner不存在", data: null },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取banner成功",
      data: targetBanner,
    });
  } catch (error) {
    console.error("获取banner错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const bannerId = parseInt(id);
    const body = await request.json();
    const {
      title,
      imageUrl,
      linkType = "none",
      linkValue,
      platform = "all",
      sort = 0,
      status = true,
      startTime,
      endTime,
    } = body;
    const cacheKey = bannnerCacheKey(platform, linkType);
    const targetBanner = await prisma.banner.upsert({
      where: {
        id: bannerId,
      },
      update: body,
      create: body,
    });
    await refreshBannerCache(cacheKey);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新banner成功",
      data: targetBanner,
    });
  } catch (error) {
    console.error("更新banner错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const bannerId = parseInt(id);
    const targetBanner = await prisma.banner.findUnique({
      where: {
        id: bannerId,
      },
    });
    if (!targetBanner) {
      return NextResponse.json(
        { success: false, code: 404, message: "banner不存在", data: null },
        { status: 404 }
      );
    }
    const cacheKey = bannnerCacheKey(
      targetBanner.platform,
      targetBanner.linkType
    );
    await prisma.banner.delete({
      where: {
        id: bannerId,
      },
    });
    await refreshBannerCache(cacheKey);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除banner成功",
      data: targetBanner,
    });
  } catch (error) {
    console.error("删除banner错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
