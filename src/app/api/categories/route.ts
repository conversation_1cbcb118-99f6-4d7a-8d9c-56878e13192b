import { NextRequest, NextResponse } from 'next/server';

// 分类数据类型
interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  parentId?: number;
  parentName?: string;
  sort: number;
  articleCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 模拟分类数据
const mockCategories: Category[] = [
  {
    id: 1,
    name: '技术分享',
    slug: 'tech',
    description: '技术相关文章，包括编程、架构、工具等',
    sort: 1,
    articleCount: 15,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    name: '产品动态',
    slug: 'product',
    description: '产品更新、新功能发布等动态信息',
    sort: 2,
    articleCount: 8,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 3,
    name: '行业资讯',
    slug: 'industry',
    description: '行业新闻、趋势分析和市场动态',
    sort: 3,
    articleCount: 12,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T14:20:00Z',
  },
  {
    id: 4,
    name: '公司新闻',
    slug: 'company',
    description: '公司相关新闻、公告和重要事件',
    sort: 4,
    articleCount: 6,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-25T09:15:00Z',
  },
  {
    id: 5,
    name: '用户故事',
    slug: 'stories',
    description: '用户使用案例、成功故事和经验分享',
    sort: 5,
    articleCount: 4,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T12:00:00Z',
  },
  {
    id: 6,
    name: '前端开发',
    slug: 'frontend',
    description: '前端开发相关技术和实践',
    parentId: 1,
    parentName: '技术分享',
    sort: 1,
    articleCount: 8,
    status: 'active',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-05T00:00:00Z',
  },
  {
    id: 7,
    name: '后端开发',
    slug: 'backend',
    description: '后端开发技术和架构设计',
    parentId: 1,
    parentName: '技术分享',
    sort: 2,
    articleCount: 5,
    status: 'active',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-05T00:00:00Z',
  },
  {
    id: 8,
    name: '移动开发',
    slug: 'mobile',
    description: 'iOS、Android等移动端开发',
    parentId: 1,
    parentName: '技术分享',
    sort: 3,
    articleCount: 2,
    status: 'inactive',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-05T00:00:00Z',
  },
];

// 验证token
function validateToken(request: NextRequest) {
  const authorization = request.headers.get('authorization');
  
  if (!authorization || !authorization.startsWith('Bearer ')) {
    return null;
  }

  const token = authorization.replace('Bearer ', '');
  
  try {
    if (token.startsWith('mock-token-')) {
      const parts = token.split('-');
      const userId = parseInt(parts[2]);
      return { id: userId };
    }
    return null;
  } catch (error) {
    return null;
  }
}

// 检查用户权限
function hasPermission(userId: number, permission: string): boolean {
  if (userId === 1) return true;
  
  const userPermissions: Record<number, string[]> = {
    2: ['content:read', 'content:create', 'content:update', 'content:delete'], // 管理员
    3: ['content:read', 'content:create', 'content:update'], // 编辑员
    4: ['content:read'], // 普通用户只能查看
  };
  
  return userPermissions[userId]?.includes(permission) || false;
}

// 构建分类树结构
function buildCategoryTree(categories: Category[]): Category[] {
  const categoryMap = new Map<number, Category & { children?: Category[] }>();
  const rootCategories: (Category & { children?: Category[] })[] = [];

  // 创建分类映射
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // 构建树结构
  categories.forEach(category => {
    const categoryNode = categoryMap.get(category.id)!;
    
    if (category.parentId) {
      const parent = categoryMap.get(category.parentId);
      if (parent) {
        parent.children!.push(categoryNode);
      }
    } else {
      rootCategories.push(categoryNode);
    }
  });

  return rootCategories;
}

// GET - 获取分类列表
export async function GET(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    if (!hasPermission(user.id, 'content:read')) {
      return NextResponse.json(
        { success: false, code: 403, message: '权限不足', data: null },
        { status: 403 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const tree = searchParams.get('tree') === 'true';
    const status = searchParams.get('status') || '';
    const parentId = searchParams.get('parentId') || '';

    // 过滤分类
    let filteredCategories = mockCategories;

    if (status) {
      filteredCategories = filteredCategories.filter(cat => cat.status === status);
    }

    if (parentId) {
      if (parentId === '0') {
        // 获取顶级分类
        filteredCategories = filteredCategories.filter(cat => !cat.parentId);
      } else {
        // 获取指定父分类的子分类
        filteredCategories = filteredCategories.filter(cat => cat.parentId === parseInt(parentId));
      }
    }

    // 按排序字段排序
    filteredCategories.sort((a, b) => a.sort - b.sort);

    // 如果需要树结构
    if (tree) {
      const categoryTree = buildCategoryTree(filteredCategories);
      return NextResponse.json({
        success: true,
        code: 200,
        message: '获取分类树成功',
        data: categoryTree,
      });
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: '获取分类列表成功',
      data: {
        list: filteredCategories,
        total: filteredCategories.length,
      },
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新分类
export async function POST(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    if (!hasPermission(user.id, 'content:create')) {
      return NextResponse.json(
        { success: false, code: 403, message: '权限不足', data: null },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, slug, description, parentId, sort = 0, status = 'active' } = body;

    // 验证必填字段
    if (!name || !slug) {
      return NextResponse.json(
        { success: false, code: 400, message: '分类名称和别名为必填项', data: null },
        { status: 400 }
      );
    }

    // 检查别名是否已存在
    const existingCategory = mockCategories.find(c => c.slug === slug);
    if (existingCategory) {
      return NextResponse.json(
        { success: false, code: 400, message: '分类别名已存在', data: null },
        { status: 400 }
      );
    }

    // 获取父分类信息
    let parentName: string | undefined;
    if (parentId) {
      const parentCategory = mockCategories.find(c => c.id === parentId);
      if (!parentCategory) {
        return NextResponse.json(
          { success: false, code: 400, message: '父分类不存在', data: null },
          { status: 400 }
        );
      }
      parentName = parentCategory.name;
    }

    // 创建新分类
    const newCategory: Category = {
      id: Math.max(...mockCategories.map(c => c.id)) + 1,
      name,
      slug,
      description: description || '',
      parentId,
      parentName,
      sort,
      articleCount: 0,
      status,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockCategories.push(newCategory);

    return NextResponse.json({
      success: true,
      code: 200,
      message: '创建分类成功',
      data: newCategory,
    });
  } catch (error) {
    console.error('创建分类错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
