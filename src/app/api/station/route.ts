import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";
import { z } from "zod";

// GET - 获取用户列表
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const keyword = searchParams.get("keyword") || "";
    const status = searchParams.get("status") || "";

    // 过滤数据 分页
    // let filteredUsers = mockUsers;
    const filteredUsers = await prisma.station.findMany({
      where: {
        name: {
          contains: keyword,
        },
        status: status ? (status as any) : undefined,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 分页
    const total = filteredUsers.length;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取用户列表成功",
      data: {
        list: filteredUsers,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取用户列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新用户
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    // 定义验证schema
    const createBusinessSchema = z.object({
      name: z.string().min(1, "商户名不能为空"),
    });

    // 验证请求数据
    const validationResult = createBusinessSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: validationResult.error.issues[0].message,
          data: null,
        },
        { status: 400 }
      );
    }

    const { name } = validationResult.data;
    const { status = "IDLE", description, shopId, price = 0, thumbnailImg } =
      body;

    // 检查用户名是否已存在
    const existingUser = await prisma.station.findFirst({
      where: {
        name: name,
      },
    });
    if (existingUser) {
      return NextResponse.json(
        { success: false, code: 400, message: "商户名已存在", data: null },
        { status: 400 }
      );
    }
    const baseUrl =
      process.env.NEXT_PUBLIC_API_URL || // 优先使用环境变量
      `${request.nextUrl.protocol}//${request.nextUrl.host}`;

    // 创建新用户
    const newUser = {
      name,
      status,
      description,
      shopId: Number(shopId),
      price: Number(price),
      thumbnailImg: `${baseUrl}${thumbnailImg}`,
    };
    await prisma.station.create({
      data: newUser,
    });

    // mockUsers.push(newUser);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "创建用户成功",
      data: newUser,
    });
  } catch (error) {
    console.error("创建用户错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
