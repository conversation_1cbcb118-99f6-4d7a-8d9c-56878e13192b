import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/db";

// 工位数据类型
interface Station {
  id: number;
  stationname: string;
  nickname: string;
  email: string;
  phone?: string;
  avatar?: string;
  status: "active" | "inactive" | "banned";
  roles: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginTime?: string;
}

// GET - 获取单个工位信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const stationId = parseInt(id);
    // const targetStation = mockStations.find((u) => u.id === stationId);
    const targetStation = await prisma.station.findUnique({
      where: {
        id: stationId,
      },
    });

    if (!targetStation) {
      return NextResponse.json(
        { success: false, code: 404, message: "工位不存在", data: null },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取工位信息成功",
      data: targetStation,
    });
  } catch (error) {
    console.error("获取工位信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// PUT - 更新工位信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const stationId = parseInt(id);
    // const stationIndex = mockStations.findIndex((u) => u.id === stationId);
    const stationIndex = await prisma.station.findUnique({
      where: {
        id: stationId,
      },
    });

    if (!stationIndex) {
      return NextResponse.json(
        { success: false, code: 404, message: "工位不存在", data: null },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { nickname, email, phone, roles, status, thumbnailImg } = body;

    const baseUrl =
      process.env.NEXT_PUBLIC_API_URL || // 优先使用环境变量
      `${request.nextUrl.protocol}//${request.nextUrl.host}`;
    // 更新工位信息
    const updatedStation = {
      ...stationIndex,
      ...(nickname && { nickname }),
      ...(email && { email }),
      ...(phone !== undefined && { phone }),
      ...(roles && { roles }),
      ...(status && { status }),
      updatedAt: new Date().toISOString(),
      ...(thumbnailImg && { thumbnailImg: `${baseUrl}${thumbnailImg}` }),
    };

    // mockStations[stationIndex] = updatedStation;
    await prisma.station.update({
      where: {
        id: stationId,
      },
      data: updatedStation,
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新工位信息成功",
      data: updatedStation,
    });
  } catch (error) {
    console.error("更新工位信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除工位
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const stationId = parseInt(id);

    // 不允许删除管理员账户
    if (stationId === 1) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "不能删除管理员账户",
          data: null,
        },
        { status: 400 }
      );
    }

    const stationIndex = await prisma.station.findUnique({
      where: {
        id: stationId,
      },
    });
    if (!stationIndex) {
      return NextResponse.json(
        { success: false, code: 404, message: "工位不存在", data: null },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除工位成功",
      data: stationIndex,
    });
  } catch (error) {
    console.error("删除工位错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
