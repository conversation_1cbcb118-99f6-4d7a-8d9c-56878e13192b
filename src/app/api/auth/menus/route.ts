import { NextRequest, NextResponse } from 'next/server';

// 模拟菜单数据
const mockMenus = [
  {
    id: 1,
    title: '首页',
    path: '/',
    icon: 'HomeOutlined',
    component: 'Home',
    parentId: null,
    sort: 1,
    type: 'menu' as const,
    permission: 'home:view',
  },
  {
    id: 2,
    title: '仪表盘',
    path: '/dashboard',
    icon: 'DashboardOutlined',
    component: 'Dashboard',
    parentId: null,
    sort: 2,
    type: 'menu' as const,
    permission: 'dashboard:view',
  },
  {
    id: 3,
    title: '用户管理',
    path: '/users',
    icon: 'UserOutlined',
    component: null,
    parentId: null,
    sort: 3,
    type: 'menu' as const,
    permission: 'users:view',
    children: [
      {
        id: 31,
        title: '用户列表',
        path: '/users/list',
        icon: null,
        component: 'UserList',
        parentId: 3,
        sort: 1,
        type: 'menu' as const,
        permission: 'users:list',
      },
      {
        id: 32,
        title: '角色管理',
        path: '/users/roles',
        icon: null,
        component: 'RoleList',
        parentId: 3,
        sort: 2,
        type: 'menu' as const,
        permission: 'users:roles',
      },
    ],
  },
  {
    id: 4,
    title: '内容管理',
    path: '/content',
    icon: 'FileTextOutlined',
    component: null,
    parentId: null,
    sort: 4,
    type: 'menu' as const,
    permission: 'content:view',
    children: [
      {
        id: 41,
        title: '文章管理',
        path: '/content/articles',
        icon: null,
        component: 'ArticleList',
        parentId: 4,
        sort: 1,
        type: 'menu' as const,
        permission: 'content:articles',
      },
      {
        id: 42,
        title: '分类管理',
        path: '/content/categories',
        icon: null,
        component: 'CategoryList',
        parentId: 4,
        sort: 2,
        type: 'menu' as const,
        permission: 'content:categories',
      },
    ],
  },
  {
    id: 5,
    title: '系统管理',
    path: '/system',
    icon: 'SettingOutlined',
    component: null,
    parentId: null,
    sort: 5,
    type: 'menu' as const,
    permission: 'system:view',
    children: [
      {
        id: 51,
        title: '系统设置',
        path: '/system/settings',
        icon: null,
        component: 'SystemSettings',
        parentId: 5,
        sort: 1,
        type: 'menu' as const,
        permission: 'system:settings',
      },
      {
        id: 52,
        title: '操作日志',
        path: '/system/logs',
        icon: null,
        component: 'SystemLogs',
        parentId: 5,
        sort: 2,
        type: 'menu' as const,
        permission: 'system:logs',
      },
    ],
  },
];

export async function GET(request: NextRequest) {
  try {
    // 获取Authorization头
    const authorization = request.headers.get('authorization');
    
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          code: 401, 
          message: '未授权访问', 
          success: false 
        },
        { status: 401 }
      );
    }

    const token = authorization.replace('Bearer ', '');
    
    // 简单的token验证
    if (!token.startsWith('mock-token-')) {
      return NextResponse.json(
        { 
          code: 401, 
          message: 'Token无效', 
          success: false 
        },
        { status: 401 }
      );
    }

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    // 根据用户权限过滤菜单（这里简化处理，返回所有菜单）
    return NextResponse.json({
      code: 200,
      message: '获取菜单成功',
      success: true,
      data: mockMenus,
    });
  } catch (error) {
    console.error('Get menus error:', error);
    return NextResponse.json(
      { 
        code: 500, 
        message: '服务器内部错误', 
        success: false 
      },
      { status: 500 }
    );
  }
}
