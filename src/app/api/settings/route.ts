import { NextRequest, NextResponse } from 'next/server';

// 系统设置数据类型
interface SystemSettings {
  // 基本设置
  siteName: string;
  siteDescription: string;
  siteKeywords: string;
  siteLogo?: string;
  siteFavicon?: string;
  siteUrl: string;
  
  // 功能设置
  enableRegistration: boolean;
  enableEmailVerification: boolean;
  enableSmsVerification: boolean;
  enableTwoFactor: boolean;
  enableComments: boolean;
  enableGuestComments: boolean;
  
  // 安全设置
  passwordMinLength: number;
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  enableCaptcha: boolean;
  
  // 邮件设置
  emailProvider: 'smtp' | 'sendgrid' | 'mailgun';
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  smtpPassword?: string;
  smtpSecure: boolean;
  emailFrom: string;
  emailFromName: string;
  
  // 存储设置
  storageProvider: 'local' | 'oss' | 's3' | 'qiniu';
  storageConfig: Record<string, any>;
  maxFileSize: number;
  allowedFileTypes: string[];
  
  // 缓存设置
  cacheEnabled: boolean;
  cacheDuration: number;
  redisEnabled: boolean;
  redisHost?: string;
  redisPort?: number;
  redisPassword?: string;
  
  // 其他设置
  timezone: string;
  language: string;
  dateFormat: string;
  timeFormat: string;
  currency: string;
  
  updatedAt: string;
  updatedBy: number;
}

// 用户个人设置数据类型
interface UserSettings {
  userId: number;
  
  // 个人信息
  nickname: string;
  avatar?: string;
  bio?: string;
  phone?: string;
  
  // 偏好设置
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  dateFormat: string;
  
  // 通知设置
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  notificationTypes: string[];
  
  // 隐私设置
  profilePublic: boolean;
  showEmail: boolean;
  showPhone: boolean;
  
  // 安全设置
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  
  updatedAt: string;
}

// 模拟系统设置数据
const mockSystemSettings: SystemSettings = {
  // 基本设置
  siteName: 'Next.js 管理后台',
  siteDescription: '基于 Next.js + Ant Design 的现代化管理后台系统',
  siteKeywords: 'Next.js, React, Ant Design, 管理后台, TypeScript',
  siteLogo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=200',
  siteFavicon: '/favicon.ico',
  siteUrl: 'https://admin.example.com',
  
  // 功能设置
  enableRegistration: true,
  enableEmailVerification: true,
  enableSmsVerification: false,
  enableTwoFactor: true,
  enableComments: true,
  enableGuestComments: false,
  
  // 安全设置
  passwordMinLength: 8,
  sessionTimeout: 1440, // 24小时
  maxLoginAttempts: 5,
  lockoutDuration: 30, // 30分钟
  enableCaptcha: true,
  
  // 邮件设置
  emailProvider: 'smtp',
  smtpHost: 'smtp.example.com',
  smtpPort: 587,
  smtpUser: '<EMAIL>',
  smtpPassword: '********',
  smtpSecure: true,
  emailFrom: '<EMAIL>',
  emailFromName: 'Next.js 管理后台',
  
  // 存储设置
  storageProvider: 'local',
  storageConfig: {
    uploadPath: '/uploads',
    baseUrl: 'https://admin.example.com/uploads',
  },
  maxFileSize: 10, // MB
  allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
  
  // 缓存设置
  cacheEnabled: true,
  cacheDuration: 3600, // 1小时
  redisEnabled: false,
  redisHost: 'localhost',
  redisPort: 6379,
  
  // 其他设置
  timezone: 'Asia/Shanghai',
  language: 'zh-CN',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: 'HH:mm:ss',
  currency: 'CNY',
  
  updatedAt: '2024-01-25T10:30:00Z',
  updatedBy: 1,
};

// 模拟用户设置数据
const mockUserSettings: Record<number, UserSettings> = {
  1: {
    userId: 1,
    nickname: '系统管理员',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
    bio: '系统管理员，负责系统维护和管理',
    phone: '13800138000',
    
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    notificationTypes: ['system', 'security', 'content'],
    
    profilePublic: false,
    showEmail: false,
    showPhone: false,
    
    twoFactorEnabled: true,
    loginNotifications: true,
    
    updatedAt: '2024-01-25T10:30:00Z',
  },
  2: {
    userId: 2,
    nickname: '管理员',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
    bio: '管理员用户',
    
    theme: 'auto',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: false,
    notificationTypes: ['system', 'content'],
    
    profilePublic: true,
    showEmail: true,
    showPhone: false,
    
    twoFactorEnabled: false,
    loginNotifications: true,
    
    updatedAt: '2024-01-20T15:20:00Z',
  },
};

// 验证token
function validateToken(request: NextRequest) {
  const authorization = request.headers.get('authorization');
  
  if (!authorization || !authorization.startsWith('Bearer ')) {
    return null;
  }

  const token = authorization.replace('Bearer ', '');
  
  try {
    if (token.startsWith('mock-token-')) {
      const parts = token.split('-');
      const userId = parseInt(parts[2]);
      return { id: userId };
    }
    return null;
  } catch (error) {
    return null;
  }
}

// 检查用户权限
function hasPermission(userId: number, permission: string): boolean {
  if (userId === 1) return true;
  
  const userPermissions: Record<number, string[]> = {
    2: ['system:read'], // 管理员只能查看系统设置
    3: [], // 编辑员无系统设置权限
    4: [], // 普通用户无系统设置权限
  };
  
  return userPermissions[userId]?.includes(permission) || false;
}

// GET - 获取设置信息
export async function GET(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'user';

    if (type === 'system') {
      // 获取系统设置
      if (!hasPermission(user.id, 'system:read')) {
        return NextResponse.json(
          { success: false, code: 403, message: '权限不足', data: null },
          { status: 403 }
        );
      }

      // 隐藏敏感信息
      const settings = { ...mockSystemSettings };
      if (settings.smtpPassword) {
        settings.smtpPassword = '********';
      }
      if (settings.redisPassword) {
        settings.redisPassword = '********';
      }

      return NextResponse.json({
        success: true,
        code: 200,
        message: '获取系统设置成功',
        data: settings,
      });
    } else {
      // 获取用户个人设置
      const userSettings = mockUserSettings[user.id];
      if (!userSettings) {
        return NextResponse.json(
          { success: false, code: 404, message: '用户设置不存在', data: null },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        code: 200,
        message: '获取用户设置成功',
        data: userSettings,
      });
    }
  } catch (error) {
    console.error('获取设置错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// PUT - 更新设置信息
export async function PUT(request: NextRequest) {
  try {
    const user = validateToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, code: 401, message: '未授权访问', data: null },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'user';
    const body = await request.json();

    if (type === 'system') {
      // 更新系统设置
      if (!hasPermission(user.id, 'system:update')) {
        return NextResponse.json(
          { success: false, code: 403, message: '权限不足', data: null },
          { status: 403 }
        );
      }

      // 更新系统设置
      Object.assign(mockSystemSettings, body, {
        updatedAt: new Date().toISOString(),
        updatedBy: user.id,
      });

      return NextResponse.json({
        success: true,
        code: 200,
        message: '更新系统设置成功',
        data: mockSystemSettings,
      });
    } else {
      // 更新用户个人设置
      if (!mockUserSettings[user.id]) {
        mockUserSettings[user.id] = {
          userId: user.id,
          nickname: '',
          theme: 'light',
          language: 'zh-CN',
          timezone: 'Asia/Shanghai',
          dateFormat: 'YYYY-MM-DD',
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: false,
          notificationTypes: [],
          profilePublic: false,
          showEmail: false,
          showPhone: false,
          twoFactorEnabled: false,
          loginNotifications: true,
          updatedAt: new Date().toISOString(),
        };
      }

      // 更新用户设置
      Object.assign(mockUserSettings[user.id], body, {
        updatedAt: new Date().toISOString(),
      });

      return NextResponse.json({
        success: true,
        code: 200,
        message: '更新用户设置成功',
        data: mockUserSettings[user.id],
      });
    }
  } catch (error) {
    console.error('更新设置错误:', error);
    return NextResponse.json(
      { success: false, code: 500, message: '服务器内部错误', data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
