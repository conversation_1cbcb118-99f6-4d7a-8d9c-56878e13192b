"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { useUserStore } from "@/store/useUserStore";
import { defHttp } from "@/lib/axios";
import { useSearch } from "@/app/main/shop/useSearch";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 车辆数据接口
interface Car {
  id: number;
  name: string;
  description: string;
  plateNumber: string;
  status: number;
  shopUserId: number;
  createdAt: string;
  updatedAt: string;
}

// 分页信息接口
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 表单数据接口
interface CarFormData {
  name: string;
  description: string;
  plateNumber: string;
  status: number;
  shopUserId: number;
}

const CarsPage: React.FC = () => {
  const [cars, setCars] = useState<Car[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCar, setEditingCar] = useState<Car | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [shopUserFilter, setShopUserFilter] = useState("");
  const [shopUsers, setShopUsers] = useState<any[]>([]);

  const { searchResults, handleSearchBusiness, handleSearchResults } =
    useSearch((params: { name: string; pageSize: number }) =>
      defHttp.get({ url: `/shopUser`, params })
    );

  // 获取车辆列表
  const fetchCars = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
        ...(shopUserFilter && { shopUser: shopUserFilter }),
      });

      const response = await defHttp.get({ url: `/car`, params });

      setCars(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取车辆列表错误:", error);
      message.error("获取车辆列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCars();
  }, [searchKeyword, statusFilter, shopUserFilter]);

  // 获取商户用户列表
  const fetchShopUsers = async () => {
    try {
      const response = await defHttp.get({ url: `/shopUser` });
      setShopUsers(response.list);
    } catch (error) {
      console.error("获取商户用户列表错误:", error);
      message.error("获取商户用户列表失败");
    }
  };

  // 搜索车辆
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 筛选车辆状态
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 筛选商户用户
  const handleShopUserFilter = (value: string) => {
    setShopUserFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 分页变化
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchCars(current, pageSize);
  };

  // 新增/编辑车辆
  const handleSave = async (values: CarFormData) => {
    try {
      setLoading(true);

      if (editingCar) {
        // 编辑
        const response = await defHttp.put({
          url: `/car/${editingCar.id}`,
          data: values,
        });
        if (response.success) {
          message.success("更新车辆成功");
          setModalVisible(false);
          setEditingCar(null);
          form.resetFields();
          fetchCars(pagination.current, pagination.pageSize);
        } else {
          message.error(response.data.message || "更新车辆失败");
        }
      } else {
        // 新增
        const response = await defHttp.post({ url: "/car", data: values });
        if (response.success) {
          message.success("创建车辆成功");
          setModalVisible(false);
          setEditingCar(null);
          form.resetFields();
          fetchCars(pagination.current, pagination.pageSize);
        } else {
          message.error(response.message || "创建车辆失败");
        }
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除车辆
  const handleDelete = async (carId: number) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/car/${carId}` });

      if (response.success) {
        message.success("删除车辆成功");
        fetchCars(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || "删除车辆失败");
      }
    } catch (error) {
      console.error("删除车辆错误:", error);
      message.error("删除车辆失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchCars();
    fetchShopUsers();
  }, []);

  const columns: ColumnsType<Car> = [
    {
      title: "车辆信息",
      key: "carInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.name}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.description}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "车牌号",
      dataIndex: "plateNumber",
      key: "plateNumber",
      width: 150,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: number) => {
        const statusOption = statusOptions.find((opt) => opt.value === status);
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "商户用户",
      dataIndex: "shopUser",
      key: "shopUser",
      width: 150,
      render: (shopUser: any) => shopUser?.nickname || "-",
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingCar(record);
                form.setFieldsValue({
                  name: record.name,
                  description: record.description,
                  plateNumber: record.plateNumber,
                  status: record.status,
                  shopUserId: record.shopUserId,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个车辆吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];
  const statusOptions = [
    { label: "启用", value: 1, color: "success" },
    { label: "禁用", value: 0, color: "error" },
  ];

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>车辆管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统车辆，包括车辆信息、角色权限等{cars.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchCars(pagination.current, pagination.pageSize)}
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingCar(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增车辆
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div style={{ marginBottom: "16px" }}>
          <Search
            placeholder="搜索车辆名称或描述"
            onSearch={handleSearch}
            style={{ width: 200 }}
          />
          <Select
            style={{ width: 120, marginLeft: 8 }}
            placeholder="筛选状态"
            onChange={handleStatusFilter}
            allowClear
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 车辆表格 */}
        <Table
          columns={columns}
          dataSource={cars}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑车辆弹窗 */}
      <Modal
        title={editingCar ? "编辑车辆" : "新增车辆"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCar(null);
          form.resetFields();
        }}
        onOk={form.submit}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "1",
          }}
        >
          <Form.Item
            name="name"
            label="车辆名称"
            rules={[{ required: true, message: "请输入车辆名称" }]}
          >
            <Input placeholder="请输入车辆名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input placeholder="请输入描述" />
          </Form.Item>

          <Form.Item
            name="plateNumber"
            label="车牌号"
            rules={[{ required: true, message: "请输入车牌号" }]}
          >
            <Input placeholder="请输入车牌号" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="shopUserId"
            label="商户用户"
            rules={[{ required: true, message: "请选择商户用户" }]}
          >
            <Select
              placeholder="请选择商户用户"
              showSearch
              filterOption={false}
              onSearch={handleSearchResults}
              onChange={handleSearchResults}
              notFoundContent={null}
              options={searchResults}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingCar(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingCar ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CarsPage;
