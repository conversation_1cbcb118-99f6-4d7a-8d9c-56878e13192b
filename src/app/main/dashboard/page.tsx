"use client";

import React from "react";
import { Card, Row, Col, Statistic, Typography, Space, Tag } from "antd";
import {
  UserOutlined,
  FileTextOutlined,
  EyeOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";

import { Line, Pie, Bar, Gauge } from "@ant-design/charts";

const { Title, Text } = Typography;

const LineConfig = {
  data: [
    { year: "1991", value: 3 },
    { year: "1992", value: 4 },
    { year: "1993", value: 3.5 },
    { year: "1994", value: 5 },
    { year: "1995", value: 4.9 },
    { year: "1996", value: 6 },
    { year: "1997", value: 7 },
    { year: "1998", value: 9 },
    { year: "1999", value: 11 },
  ],

  xField: "year",
  yField: "value",
  shapeField: "smooth",
  scale: {
    y: {
      domainMin: 0,
    },
  },
  interaction: {
    tooltip: {
      marker: false,
    },
  },
  style: {
    lineWidth: 2,
  },
};

const PieConfig = {
  data: [
    { name: "新用户", value: 335, color: "#1890ff" },
    { name: "活跃用户", value: 310, color: "#52c41a" },
    { name: "沉睡用户", value: 234, color: "#faad14" },
    { name: "流失用户", value: 135, color: "#f5222d" },
  ],
  angleField: "value",
  colorField: "name",
  label: {
    text: "value",
    style: {
      fontWeight: "bold",
    },
  },
  legend: {
    color: {
      title: false,
      position: "right",
      rowPadding: 5,
    },
  },
};

const BarConfig = {
  data: [
    { name: "桌面端", value: 12000 },
    { name: "移动端", value: 8500 },
    { name: "平板端", value: 3200 },
    { name: "其他", value: 1800 },
  ],
  xField: "name",
  yField: "value",
  colorField: "name",
  label: {
    text: "value",
    style: {
      fontWeight: "bold",
    },
  },
};

const GaugeConfig = {
  autoFit: true,
  data: {
    target: 159,
    total: 400,
    name: "score",
    thresholds: [100, 200, 400],
  },
  scale: {
    color: {
      range: ["#F4664A", "#FAAD14", "green"],
    },
  },
  style: {
    textContent: (target: number, total: number) =>
      `得分：${target}\n占比：${(target / total) * 100}%`,
  },
};

export default function DashboardPage() {
  return (
    <div>
      <Title level={2} style={{ marginBottom: "24px" }}>
        📊 仪表盘
      </Title>

      {/* 数据概览卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={1128}
              prefix={<UserOutlined />}
              suffix={
                <Tag color="green" style={{ marginLeft: "8px" }}>
                  <ArrowUpOutlined /> 12%
                </Tag>
              }
              valueStyle={{ color: "#3f8600" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总文章数"
              value={234}
              prefix={<FileTextOutlined />}
              suffix={
                <Tag color="blue" style={{ marginLeft: "8px" }}>
                  <ArrowUpOutlined /> 8%
                </Tag>
              }
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={89563}
              prefix={<EyeOutlined />}
              suffix={
                <Tag color="orange" style={{ marginLeft: "8px" }}>
                  <ArrowUpOutlined /> 25%
                </Tag>
              }
              valueStyle={{ color: "#fa8c16" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日访问"
              value={2456}
              prefix={<TrophyOutlined />}
              suffix={
                <Tag color="red" style={{ marginLeft: "8px" }}>
                  <ArrowDownOutlined /> 3%
                </Tag>
              }
              valueStyle={{ color: "#cf1322" }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据可视化图表 */}
      <Row gutter={[16, 16]} style={{ marginTop: "16px" }}>
        <Col xs={24} lg={12}>
          <Card title="📈 访问趋势">
            <Line {...LineConfig} />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="📊 用户分布">
            <Pie {...PieConfig} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: "16px" }}>
        <Col xs={24} lg={12}>
          <Card title="📱 设备类型统计">
            <Bar {...BarConfig} />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="💻 系统状态">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Gauge {...GaugeConfig} />
              </Col>
              <Col xs={24} sm={8}>
                <Gauge {...GaugeConfig} />
              </Col>
              <Col xs={24} sm={8}>
                <Gauge {...GaugeConfig} />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} style={{ marginTop: "24px" }}>
        <Col span={24}>
          <Card title="🚀 快速操作">
            <Space wrap size="large">
              <Card.Grid style={{ width: "25%", textAlign: "center" }}>
                <UserOutlined style={{ fontSize: "24px", color: "#1890ff" }} />
                <div style={{ marginTop: "8px" }}>用户管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: "25%", textAlign: "center" }}>
                <FileTextOutlined
                  style={{ fontSize: "24px", color: "#52c41a" }}
                />
                <div style={{ marginTop: "8px" }}>内容管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: "25%", textAlign: "center" }}>
                <TrophyOutlined
                  style={{ fontSize: "24px", color: "#faad14" }}
                />
                <div style={{ marginTop: "8px" }}>数据统计</div>
              </Card.Grid>
              <Card.Grid style={{ width: "25%", textAlign: "center" }}>
                <EyeOutlined style={{ fontSize: "24px", color: "#722ed1" }} />
                <div style={{ marginTop: "8px" }}>系统监控</div>
              </Card.Grid>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
