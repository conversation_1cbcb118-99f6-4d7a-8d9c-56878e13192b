"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";

import { defHttp } from "@/lib/axios";
import { useRouter } from "next/navigation";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 订单数据接口
interface Order {
  id: string;
  orderNo: string;
  type: number;
  status: number;
  amount: number;
  payMethod: number;
  createdAt: string;
  updatedAt: string;
}

// 分页信息接口
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 表单数据接口
interface OrderFormData {
  orderNo: string;
  type: number;
  status: number;
  amount: number;
  payMethod: number;
}

const OrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOrder, setEditingOrder] = useState<Order | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const router = useRouter();

  // 获取订单列表
  const fetchOrders = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
        ...(typeFilter && { type: typeFilter }),
      });

      const response = await defHttp.get({ url: `/order/list`, params });

      setOrders(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取订单列表错误:", error);
      message.error("获取订单列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [searchKeyword, statusFilter, typeFilter]);

  // 搜索订单
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 筛选订单状态
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 筛选订单类型
  const handleTypeFilter = (value: string) => {
    setTypeFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 分页变化时的处理函数
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchOrders(current, pageSize);
  };

  // 保存订单
  const handleSave = async (values: OrderFormData) => {
    try {
      setLoading(true);

      if (editingOrder) {
        // 编辑
        const response = await defHttp.put({
          url: `/order/${editingOrder.id}`,
          data: values,
        });
        if (response.success) {
          message.success("更新订单成功");
          setModalVisible(false);
          setEditingOrder(null);
          form.resetFields();
          fetchOrders(pagination.current, pagination.pageSize);
        } else {
          message.error(response.data.message || "更新订单失败");
        }
      } else {
        // 新增
        const response = await defHttp.post({ url: "/order", data: values });
        if (response.success) {
          message.success("创建订单成功");
          setModalVisible(false);
          setEditingOrder(null);
          form.resetFields();
          fetchOrders(pagination.current, pagination.pageSize);
        } else {
          message.error(response.message || "创建订单失败");
        }
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除订单
  const handleDelete = async (orderId: string) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/order/${orderId}` });

      if (response.success) {
        message.success("删除订单成功");
        fetchOrders(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || "删除订单失败");
      }
    } catch (error) {
      console.error("删除订单错误:", error);
      message.error("删除订单失败");
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns: ColumnsType<Order> = [
    {
      title: "订单信息",
      key: "orderInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.orderNo}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.createdAt}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      width: 100,
      render: (type: string) => {
        const typeOption = typeOptions.find((opt) => opt.value === type);
        return typeOption?.label || type;
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        const statusOption = statusOptions.find((opt) => opt.value === status);
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "金额",
      dataIndex: "amount",
      key: "amount",
      width: 150,
      render: (amount: number) => amount + "元",
    },
    {
      title: "支付方式",
      dataIndex: "payMethod",
      key: "payMethod",
      width: 100,
      render: (payMethod: string) => {
        const payMethodOption = payMethodOptions.find(
          (opt) => opt.value === payMethod
        );
        return payMethodOption?.label || payMethod;
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingOrder(record);
                form.setFieldsValue({
                  orderNo: record.orderNo,
                  type: record.type,
                  status: record.status,
                  amount: record.amount,
                  payMethod: record.payMethod,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个订单吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                router.push(`/main/order/detail?id=${record.id}`);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 订单类型选项
  const typeOptions = [
    { value: "RECHARGE", label: "充值" },
    { value: "CONSUME", label: "消费" },
    { value: "REFUND", label: "退款" },
    { value: "WASH", label: "洗车" },
  ];

  // 订单状态选项
  const statusOptions = [
    { value: "PENDING", label: "待支付", color: "default" },
    { value: "PAID", label: "已支付", color: "success" },
    { value: "FAILED", label: "支付失败", color: "error" },
    { value: "REFUNDED", label: "已退款", color: "warning" },
  ];

  // 支付方式选项
  const payMethodOptions = [
    { value: "WECHAT", label: "微信" },
    { value: "ALIPAY", label: "支付宝" },
    { value: "BALANCE", label: "余额" },
  ];

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>订单管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统订单，包括订单信息、角色权限等{orders.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchOrders(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingOrder(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增订单
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索订单号"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选类型"
            allowClear
            style={{ width: 120 }}
            onChange={handleTypeFilter}
          >
            {typeOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 订单表格 */}
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑订单弹窗 */}
      <Modal
        title={editingOrder ? "编辑订单" : "新增订单"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingOrder(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "PENDING",
            type: "RECHARGE",
            payMethod: "WECHAT",
          }}
        >
          <Form.Item
            name="orderNo"
            label="订单号"
            rules={[{ required: true, message: "请输入订单号" }]}
          >
            <Input placeholder="请输入订单号" />
          </Form.Item>

          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: "请选择类型" }]}
          >
            <Select placeholder="请选择类型">
              {typeOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="金额"
            rules={[{ required: true, message: "请输入金额" }]}
          >
            <Input type="number" placeholder="请输入金额" suffix="元" />
          </Form.Item>

          <Form.Item
            name="payMethod"
            label="支付方式"
            rules={[{ required: true, message: "请选择支付方式" }]}
          >
            <Select placeholder="请选择支付方式">
              {payMethodOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              {" "}
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingOrder(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingOrder ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OrdersPage;
