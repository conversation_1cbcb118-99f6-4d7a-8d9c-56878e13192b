"use client";

import React, { useState, useEffect } from "react";
import { Card, Typography, Button, Space, Descriptions } from "antd";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { defHttp } from "@/lib/axios";
import { useUserStore } from "@/store/useUserStore";
import dayjs from "dayjs";

const { Title } = Typography;

const OrderDetailPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("id");
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const data = await defHttp.get({ url: `/order/${orderId}` });

      if (data.type === "WASH") {
        const durationMinutes = dayjs(data.completedAt).diff(
          data.createdAt,
          "minute"
        );
        data.durationMinutes = durationMinutes;
      }

      setOrder(data);
    } catch (error) {
      console.error("获取订单详情错误:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  if (!orderId) {
    return null;
  }

  return (
    <Card
      title="订单详情"
      extra={<Button onClick={() => router.back()}>返回</Button>}
    >
      <Descriptions column={1} bordered>
        <Descriptions.Item label="订单号">{order?.orderNo}</Descriptions.Item>
        <Descriptions.Item label="类型">{order?.type}</Descriptions.Item>
        <Descriptions.Item label="状态">{order?.status}</Descriptions.Item>
        <Descriptions.Item label="金额">{order?.amount}</Descriptions.Item>
        {order?.type === "WASH" && (
          <Descriptions.Item label="完成时间">
            {order?.completedAt}
          </Descriptions.Item>
        )}
        {order?.type === "WASH" && (
          <Descriptions.Item label="时长">
            {order?.durationMinutes}分钟
          </Descriptions.Item>
        )}
        <Descriptions.Item label="支付方式">
          {order?.payMethod}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          {order?.createdAt}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间">
          {order?.updatedAt}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default OrderDetailPage;
