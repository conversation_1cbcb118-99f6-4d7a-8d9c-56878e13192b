'use client';

import React, { useState, useEffect } from 'react';
import { Space, Avatar, Tag, Badge, message } from 'antd';
import { UserOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import MainLayout from '@/components/layout/MainLayout';
import { DataTable, commonRowActions, commonFilters, tableUtils } from '@/components/table';
import type { ColumnsType } from 'antd/es/table';

// 示例数据类型
interface ExampleUser {
  id: number;
  username: string;
  nickname: string;
  email: string;
  phone?: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'banned';
  roles: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginTime?: string;
}

// 模拟数据
const mockData: ExampleUser[] = [
  {
    id: 1,
    username: 'admin',
    nickname: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    status: 'active',
    roles: ['admin', 'super_admin'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    lastLoginTime: '2024-01-31T08:45:00Z',
  },
  {
    id: 2,
    username: 'user',
    nickname: '普通用户',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user',
    status: 'active',
    roles: ['user'],
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-20T14:20:00Z',
    lastLoginTime: '2024-01-30T16:30:00Z',
  },
  {
    id: 3,
    username: 'editor',
    nickname: '内容编辑',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=editor',
    status: 'inactive',
    roles: ['editor', 'user'],
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-25T09:15:00Z',
    lastLoginTime: '2024-01-29T11:20:00Z',
  },
];

export default function TableExamplePage() {
  const [data, setData] = useState<ExampleUser[]>(mockData);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockData.length,
  });

  // 角色选项
  const roleOptions = [
    { label: '超级管理员', value: 'super_admin', color: 'red' },
    { label: '管理员', value: 'admin', color: 'orange' },
    { label: '编辑员', value: 'editor', color: 'green' },
    { label: '普通用户', value: 'user', color: 'default' },
  ];

  // 状态选项
  const statusOptions = [
    { label: '正常', value: 'active', color: 'success' },
    { label: '禁用', value: 'inactive', color: 'warning' },
    { label: '封禁', value: 'banned', color: 'error' },
  ];

  // 表格列定义
  const columns: ColumnsType<ExampleUser> = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            size={40}
            icon={<UserOutlined />}
            src={record.avatar}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>@{record.username}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      width: 180,
      render: (_, record) => (
        <div>
          <div>{record.email}</div>
          {record.phone && (
            <div style={{ fontSize: '12px', color: '#666' }}>{record.phone}</div>
          )}
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 150,
      render: (roles: string[]) => (
        <Space wrap>
          {tableUtils.formatRoles(roles, roleOptions).map((role, index) => (
            <Tag key={index} color={role.color}>
              {role.label}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusOption = tableUtils.formatStatus(status, statusOptions);
        return (
          <Badge
            status={statusOption.color as any}
            text={statusOption.label}
          />
        );
      },
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginTime',
      key: 'lastLoginTime',
      width: 150,
      render: (time: string) => tableUtils.formatTime(time),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: string) => tableUtils.formatTime(time),
    },
  ];

  // 处理搜索
  const handleSearch = (value: string) => {
    setLoading(true);
    setTimeout(() => {
      if (value) {
        const filtered = mockData.filter(item =>
          item.username.includes(value) ||
          item.nickname.includes(value) ||
          item.email.includes(value)
        );
        setData(filtered);
        setPagination(prev => ({ ...prev, total: filtered.length, current: 1 }));
      } else {
        setData(mockData);
        setPagination(prev => ({ ...prev, total: mockData.length, current: 1 }));
      }
      setLoading(false);
    }, 500);
  };

  // 处理筛选
  const handleFilter = (key: string, value: string | number | undefined) => {
    setLoading(true);
    setTimeout(() => {
      let filtered = mockData;
      
      if (key === 'status' && value) {
        filtered = filtered.filter(item => item.status === value);
      }
      
      setData(filtered);
      setPagination(prev => ({ ...prev, total: filtered.length, current: 1 }));
      setLoading(false);
    }, 300);
  };

  // 处理刷新
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setData(mockData);
      setPagination(prev => ({ ...prev, total: mockData.length }));
      setLoading(false);
      message.success('刷新成功');
    }, 1000);
  };

  // 处理导出
  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  // 处理新增
  const handleCreate = () => {
    message.info('新增功能开发中...');
  };

  // 处理查看
  const handleView = (record: ExampleUser) => {
    message.info(`查看用户: ${record.nickname}`);
  };

  // 处理编辑
  const handleEdit = (record: ExampleUser) => {
    message.info(`编辑用户: ${record.nickname}`);
  };

  // 处理删除
  const handleDelete = (record: ExampleUser) => {
    setData(prev => prev.filter(item => item.id !== record.id));
    message.success(`删除用户 ${record.nickname} 成功`);
  };

  return (
    <MainLayout>
      <div style={{ padding: '0' }}>
        <DataTable<ExampleUser>
          title="数据表格组件示例"
          description="这是一个可复用的数据表格组件示例，支持搜索、筛选、分页、行操作等功能"
          dataSource={data}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={tableUtils.createPagination(pagination.current, pagination.pageSize, pagination.total)}
          searchable={true}
          searchPlaceholder="搜索用户名、昵称或邮箱"
          filters={[
            commonFilters.status(statusOptions),
          ]}
          actions={{
            refresh: {
              onClick: handleRefresh,
            },
            export: {
              onClick: handleExport,
            },
            create: {
              text: '新增用户',
              onClick: handleCreate,
            },
          }}
          rowActions={[
            commonRowActions.view(handleView),
            commonRowActions.edit(handleEdit),
            commonRowActions.delete(handleDelete, {
              visible: (record) => record.id !== 1, // 不显示删除管理员的按钮
            }),
          ]}
          onSearch={handleSearch}
          onFilter={handleFilter}
          scroll={{ x: 1200 }}
        />
      </div>
    </MainLayout>
  );
}
