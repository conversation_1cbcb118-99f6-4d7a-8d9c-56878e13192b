"use client";

import React, { useState } from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Table,
  Tag,
  Alert,
  Input,
  Select,
} from "antd";
import {
  BugOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  DeleteOutlined,
  ThunderboltOutlined,
  DisconnectOutlined,
  LockOutlined,
} from "@ant-design/icons";
import MainLayout from "@/components/layout/MainLayout";
import AuthGuard from "@/components/auth/AuthGuard";
import { ResponsiveContainer } from "@/components/layout/ResponsiveLayout";
import ErrorBoundary, {
  withErrorBoundary,
} from "@/components/error/ErrorBoundary";

import {
  useErrorHandler,
  useNetworkErrorHandler,
  useFormErrorHandler,
  useErrorMonitor,
} from "@/hooks/useErrorHandler";
import {
  handleError,
  handleNetworkError,
  handleValidationError,
  handleAuthError,
  handleServerError,
  ErrorType,
  ErrorLevel,
} from "@/utils/errorHandler";
// import { http } from "@/lib/request";

const { Title, Paragraph } = Typography;
const { Option } = Select;

// 故意出错的组件
const BuggyComponent: React.FC<{ shouldThrow: boolean }> = ({
  shouldThrow,
}) => {
  if (shouldThrow) {
    throw new Error("这是一个故意抛出的错误，用于测试错误边界");
  }
  return <div>这个组件正常工作</div>;
};

// 使用错误边界包装的组件
const SafeBuggyComponent = withErrorBoundary(BuggyComponent, {
  level: "component",
  showDetails: true,
});

export default function ErrorsPage() {
  const [shouldThrow, setShouldThrow] = useState(false);
  const [errorType, setErrorType] = useState<ErrorType>(ErrorType.CLIENT);
  const [errorLevel, setErrorLevel] = useState<ErrorLevel>(ErrorLevel.MEDIUM);
  const [errorMessage, setErrorMessage] = useState("这是一个测试错误");

  // 使用错误处理Hook
  const {
    isError,
    errorMessage: hookErrorMessage,
    clearError,
    handleAsync,
  } = useErrorHandler();
  const {
    loading,

    handleRequest,
    clearError: clearNetworkError,
  } = useNetworkErrorHandler();
  const { fieldErrors, setFieldError, clearAllErrors, getFieldError } =
    useFormErrorHandler();
  const { errorHistory, clearHistory, getErrorStats } = useErrorMonitor();

  // 错误统计
  const errorStats = getErrorStats();

  // 触发不同类型的错误
  const triggerError = (type: string) => {
    switch (type) {
      case "client":
        handleError({
          type: ErrorType.CLIENT,
          level: ErrorLevel.MEDIUM,
          message: "客户端错误示例",
          details: { component: "ErrorsPage", action: "triggerError" },
        });
        break;
      case "network":
        handleNetworkError(new Error("网络连接失败"));
        break;
      case "validation":
        handleValidationError("表单验证失败", {
          field: "email",
          value: "invalid-email",
        });
        break;
      case "auth":
        handleAuthError("用户认证失败");
        break;
      case "server":
        handleServerError({
          message: "服务器内部错误",
          code: 500,
          details: { endpoint: "/api/test" },
        });
        break;
      case "custom":
        handleError({
          type: errorType,
          level: errorLevel,
          message: errorMessage,
          timestamp: Date.now(),
        });
        break;
    }
  };

  // 测试网络请求错误
  const testNetworkError = async () => {
    // await handleRequest(() => http.get("/api/nonexistent-endpoint"), {
    //   onError: (error) => {
    //     console.log("网络请求失败:", error);
    //   },
    //   retryCount: 2,
    // });
  };

  // 测试异步错误处理
  const testAsyncError = async () => {
    await handleAsync(
      async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        throw new Error("异步操作失败");
      },
      {
        errorMessage: "异步操作遇到问题",
      }
    );
  };

  // 测试表单验证错误
  const testFormError = () => {
    setFieldError("email", "邮箱格式不正确");
    setFieldError("password", "密码长度不能少于6位");
  };

  // 错误历史表格列
  const errorColumns = [
    {
      title: "时间",
      dataIndex: "timestamp",
      key: "timestamp",
      render: (timestamp: number) => new Date(timestamp).toLocaleString(),
      width: 150,
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      render: (type: ErrorType) => {
        const colorMap = {
          [ErrorType.CLIENT]: "blue",
          [ErrorType.SERVER]: "red",
          [ErrorType.NETWORK]: "orange",
          [ErrorType.VALIDATION]: "purple",
          [ErrorType.AUTHENTICATION]: "gold",
          [ErrorType.AUTHORIZATION]: "magenta",
          [ErrorType.UNKNOWN]: "default",
        };
        return <Tag color={colorMap[type]}>{type}</Tag>;
      },
      width: 100,
    },
    {
      title: "级别",
      dataIndex: "level",
      key: "level",
      render: (level: ErrorLevel) => {
        const colorMap = {
          [ErrorLevel.LOW]: "green",
          [ErrorLevel.MEDIUM]: "orange",
          [ErrorLevel.HIGH]: "red",
          [ErrorLevel.CRITICAL]: "red",
        };
        return <Tag color={colorMap[level]}>{level}</Tag>;
      },
      width: 80,
    },
    {
      title: "消息",
      dataIndex: "message",
      key: "message",
      ellipsis: true,
    },
    {
      title: "代码",
      dataIndex: "code",
      key: "code",
      width: 80,
    },
  ];

  return (
    <AuthGuard>
      <MainLayout>
        <ResponsiveContainer>
          <Title level={2}>错误处理演示</Title>
          <Paragraph>
            这个页面展示了我们实现的完整错误处理系统，包括错误边界、全局错误处理、错误监控和用户友好的错误提示。
          </Paragraph>

          {/* 错误统计 */}
          <Card title="错误统计" style={{ marginBottom: "24px" }}>
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={6}>
                <div style={{ textAlign: "center" }}>
                  <div
                    style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#1890ff",
                    }}
                  >
                    {errorStats.total}
                  </div>
                  <div>总错误数</div>
                </div>
              </Col>
              <Col xs={12} sm={6}>
                <div style={{ textAlign: "center" }}>
                  <div
                    style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#52c41a",
                    }}
                  >
                    {errorStats.recent}
                  </div>
                  <div>最近5分钟</div>
                </div>
              </Col>
              <Col xs={12} sm={6}>
                <div style={{ textAlign: "center" }}>
                  <div
                    style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#faad14",
                    }}
                  >
                    {errorStats.byLevel[ErrorLevel.HIGH] || 0}
                  </div>
                  <div>高级别错误</div>
                </div>
              </Col>
              <Col xs={12} sm={6}>
                <div style={{ textAlign: "center" }}>
                  <div
                    style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#ff4d4f",
                    }}
                  >
                    {errorStats.byLevel[ErrorLevel.CRITICAL] || 0}
                  </div>
                  <div>严重错误</div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 错误边界演示 */}
          <Card title="错误边界演示" style={{ marginBottom: "24px" }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Alert
                message="错误边界测试"
                description="点击下面的按钮来触发组件错误，观察错误边界如何捕获和处理错误。"
                type="info"
                showIcon
              />
              <Space>
                <Button
                  type="primary"
                  danger={shouldThrow}
                  onClick={() => setShouldThrow(!shouldThrow)}
                >
                  {shouldThrow ? "修复组件" : "破坏组件"}
                </Button>
              </Space>
              <ErrorBoundary level="component" showDetails>
                <SafeBuggyComponent shouldThrow={shouldThrow} />
              </ErrorBoundary>
            </Space>
          </Card>

          {/* 错误类型演示 */}
          <Card title="错误类型演示" style={{ marginBottom: "24px" }}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Button
                  block
                  icon={<BugOutlined />}
                  onClick={() => triggerError("client")}
                >
                  客户端错误
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  block
                  icon={<DisconnectOutlined />}
                  onClick={() => triggerError("network")}
                >
                  网络错误
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  block
                  icon={<ExclamationCircleOutlined />}
                  onClick={() => triggerError("validation")}
                >
                  验证错误
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  block
                  icon={<LockOutlined />}
                  onClick={() => triggerError("auth")}
                >
                  认证错误
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  block
                  icon={<WarningOutlined />}
                  onClick={() => triggerError("server")}
                >
                  服务器错误
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  block
                  icon={<ThunderboltOutlined />}
                  onClick={testNetworkError}
                  loading={loading}
                >
                  网络请求错误
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 自定义错误 */}
          <Card title="自定义错误" style={{ marginBottom: "24px" }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Select
                    value={errorType}
                    onChange={setErrorType}
                    style={{ width: "100%" }}
                    placeholder="选择错误类型"
                  >
                    {Object.values(ErrorType).map((type) => (
                      <Option key={type} value={type}>
                        {type}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={8}>
                  <Select
                    value={errorLevel}
                    onChange={setErrorLevel}
                    style={{ width: "100%" }}
                    placeholder="选择错误级别"
                  >
                    {Object.values(ErrorLevel).map((level) => (
                      <Option key={level} value={level}>
                        {level}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={8}>
                  <Button
                    type="primary"
                    onClick={() => triggerError("custom")}
                    block
                  >
                    触发自定义错误
                  </Button>
                </Col>
              </Row>
              <Input
                value={errorMessage}
                onChange={(e) => setErrorMessage(e.target.value)}
                placeholder="输入错误消息"
              />
            </Space>
          </Card>

          {/* Hook错误处理演示 */}
          <Card title="Hook错误处理演示" style={{ marginBottom: "24px" }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              {isError && (
                <Alert
                  message="Hook捕获的错误"
                  description={hookErrorMessage}
                  type="error"
                  closable
                  onClose={clearError}
                />
              )}
              {Object.keys(fieldErrors).length > 0 && (
                <Alert
                  message="表单验证错误"
                  description={
                    <ul>
                      {Object.entries(fieldErrors).map(([field, error]) => (
                        <li key={field}>
                          {field}: {error}
                        </li>
                      ))}
                    </ul>
                  }
                  type="warning"
                  closable
                  onClose={clearAllErrors}
                />
              )}
              <Space>
                <Button onClick={testAsyncError}>测试异步错误</Button>
                <Button onClick={testFormError}>测试表单错误</Button>
                <Button onClick={clearError}>清除Hook错误</Button>
                <Button onClick={clearAllErrors}>清除表单错误</Button>
              </Space>
            </Space>
          </Card>

          {/* 错误历史 */}
          <Card
            title="错误历史"
            extra={
              <Button
                icon={<DeleteOutlined />}
                onClick={clearHistory}
                size="small"
              >
                清空历史
              </Button>
            }
            style={{ marginBottom: "24px" }}
          >
            <Table
              dataSource={errorHistory}
              columns={errorColumns}
              rowKey="timestamp"
              pagination={{ pageSize: 10 }}
              size="small"
              scroll={{ x: 600 }}
            />
          </Card>
        </ResponsiveContainer>
      </MainLayout>
    </AuthGuard>
  );
}
