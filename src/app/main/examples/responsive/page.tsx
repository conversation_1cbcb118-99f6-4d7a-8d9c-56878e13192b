"use client";

import React from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Space,
  Button,
  Table,
  Form,
  Input,
  Select,
} from "antd";
import {
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import MainLayout from "@/components/layout/MainLayout";
import AuthGuard from "@/components/auth/AuthGuard";
import {
  ResponsiveContainer,
  ResponsiveGrid,
} from "@/components/layout/ResponsiveLayout";
import {
  useResponsive,
  useResponsiveConfig,
  useResponsiveValue,
  useResponsiveStyles,
} from "@/hooks/useResponsive";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

export default function ResponsivePage() {
  const responsive = useResponsive();
  const config = useResponsiveConfig();
  const styles = useResponsiveStyles();

  // 响应式值示例
  const cardColumns = useResponsiveValue({
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 6,
    default: 1,
  });

  const buttonSize = useResponsiveValue({
    xs: "small",
    sm: "small",
    md: "middle",
    lg: "middle",
    xl: "large",
    default: "middle",
  });

  // 示例数据
  const tableData = [
    { key: "1", name: "张三", age: 32, address: "北京市朝阳区" },
    { key: "2", name: "李四", age: 28, address: "上海市浦东新区" },
    { key: "3", name: "王五", age: 35, address: "广州市天河区" },
  ];

  const tableColumns = [
    { title: "姓名", dataIndex: "name", key: "name" },
    { title: "年龄", dataIndex: "age", key: "age" },
    { title: "地址", dataIndex: "address", key: "address" },
  ];

  return (
    <AuthGuard>
      <MainLayout>
        <ResponsiveContainer>
          <Title level={2}>响应式设计演示</Title>
          <Paragraph>
            这个页面展示了我们的响应式设计系统，包括断点检测、响应式配置和自适应组件。
            请尝试调整浏览器窗口大小来查看不同的响应式效果。
          </Paragraph>

          {/* 设备信息卡片 */}
          <Card
            title="当前设备信息"
            style={{ marginBottom: "24px" }}
            extra={
              <Space>
                {responsive.isMobile && (
                  <MobileOutlined style={{ color: "#52c41a" }} />
                )}
                {responsive.isTablet && (
                  <TabletOutlined style={{ color: "#1890ff" }} />
                )}
                {responsive.isDesktop && (
                  <DesktopOutlined style={{ color: "#722ed1" }} />
                )}
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Card size="small" title="设备类型">
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <div>
                      <Text strong>设备类型: </Text>
                      <Tag
                        color={
                          responsive.isMobile
                            ? "green"
                            : responsive.isTablet
                            ? "blue"
                            : "purple"
                        }
                      >
                        {responsive.deviceType}
                      </Tag>
                    </div>
                    <div>
                      <Text strong>屏幕尺寸: </Text>
                      <Tag>{responsive.screenSize}</Tag>
                    </div>
                    <div>
                      <Text strong>窗口尺寸: </Text>
                      <Text code>
                        {responsive.windowSize.width} ×{" "}
                        {responsive.windowSize.height}
                      </Text>
                    </div>
                  </Space>
                </Card>
              </Col>

              <Col xs={24} sm={12} md={8}>
                <Card size="small" title="断点状态">
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <div>
                      <Text>xs (≤480px): </Text>
                      <Tag color={responsive.isXs ? "green" : "default"}>
                        {responsive.isXs ? "激活" : "未激活"}
                      </Tag>
                    </div>
                    <div>
                      <Text>sm (≤576px): </Text>
                      <Tag color={responsive.isSm ? "green" : "default"}>
                        {responsive.isSm ? "激活" : "未激活"}
                      </Tag>
                    </div>
                    <div>
                      <Text>md (≤768px): </Text>
                      <Tag color={responsive.isMd ? "green" : "default"}>
                        {responsive.isMd ? "激活" : "未激活"}
                      </Tag>
                    </div>
                    <div>
                      <Text>lg (≤992px): </Text>
                      <Tag color={responsive.isLg ? "green" : "default"}>
                        {responsive.isLg ? "激活" : "未激活"}
                      </Tag>
                    </div>
                    <div>
                      <Text>xl (≤1200px): </Text>
                      <Tag color={responsive.isXl ? "green" : "default"}>
                        {responsive.isXl ? "激活" : "未激活"}
                      </Tag>
                    </div>
                    <div>
                      <Text>xxl (&gt;1200px): </Text>
                      <Tag color={responsive.isXxl ? "green" : "default"}>
                        {responsive.isXxl ? "激活" : "未激活"}
                      </Tag>
                    </div>
                  </Space>
                </Card>
              </Col>

              <Col xs={24} sm={24} md={8}>
                <Card size="small" title="响应式配置">
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <div>
                      <Text>表格大小: </Text>
                      <Tag>{config.table.size}</Tag>
                    </div>
                    <div>
                      <Text>按钮大小: </Text>
                      <Tag>{config.button.size}</Tag>
                    </div>
                    <div>
                      <Text>表单布局: </Text>
                      <Tag>{config.form.layout}</Tag>
                    </div>
                    <div>
                      <Text>侧边栏宽度: </Text>
                      <Tag>{config.layout.siderWidth}px</Tag>
                    </div>
                    <div>
                      <Text>头部高度: </Text>
                      <Tag>{config.layout.headerHeight}px</Tag>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </Card>

          {/* 响应式栅格演示 */}
          <Card title="响应式栅格演示" style={{ marginBottom: "24px" }}>
            <Paragraph>
              <InfoCircleOutlined /> 当前显示 {cardColumns}{" "}
              列，会根据屏幕大小自动调整
            </Paragraph>
            <ResponsiveGrid
              columns={{
                xs: 1,
                sm: 2,
                md: 3,
                lg: 4,
                xl: 6,
              }}
            >
              {Array.from({ length: 12 }, (_, i) => (
                <Card
                  key={i}
                  size="small"
                  style={{
                    background: `hsl(${(i * 30) % 360}, 70%, 95%)`,
                    border: `1px solid hsl(${(i * 30) % 360}, 70%, 80%)`,
                  }}
                >
                  <div style={{ textAlign: "center", padding: "20px 0" }}>
                    <Text strong>卡片 {i + 1}</Text>
                  </div>
                </Card>
              ))}
            </ResponsiveGrid>
          </Card>

          {/* 响应式表格演示 */}
          <Card title="响应式表格演示" style={{ marginBottom: "24px" }}>
            <Table
              dataSource={tableData}
              columns={tableColumns}
              size={config.table.size as any}
              scroll={config.table.scroll}
              pagination={config.table.pagination as any}
            />
          </Card>

          {/* 响应式表单演示 */}
          <Card title="响应式表单演示" style={{ marginBottom: "24px" }}>
            <Form
              layout={config.form.layout as any}
              labelCol={config.form.labelCol}
              wrapperCol={config.form.wrapperCol}
              size={config.form.size as any}
            >
              <Form.Item label="用户名" name="username">
                <Input placeholder="请输入用户名" />
              </Form.Item>
              <Form.Item label="邮箱" name="email">
                <Input placeholder="请输入邮箱" />
              </Form.Item>
              <Form.Item label="城市" name="city">
                <Select placeholder="请选择城市">
                  <Option value="beijing">北京</Option>
                  <Option value="shanghai">上海</Option>
                  <Option value="guangzhou">广州</Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    size={config.button.size as any}
                    block={config.button.block}
                  >
                    提交
                  </Button>
                  <Button
                    size={config.button.size as any}
                    block={config.button.block}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>

          {/* 响应式按钮演示 */}
          <Card title="响应式按钮演示" style={{ marginBottom: "24px" }}>
            <Space
              direction={responsive.isSmallScreen ? "vertical" : "horizontal"}
              style={{ width: "100%" }}
              wrap
            >
              <Button size={buttonSize as any} type="primary">
                主要按钮 ({buttonSize})
              </Button>
              <Button size={buttonSize as any}>默认按钮 ({buttonSize})</Button>
              <Button size={buttonSize as any} type="dashed">
                虚线按钮 ({buttonSize})
              </Button>
              <Button size={buttonSize as any} type="link">
                链接按钮 ({buttonSize})
              </Button>
            </Space>
          </Card>

          {/* 响应式样式演示 */}
          <Card title="响应式样式演示">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <div
                  style={{
                    padding: styles.spacing.medium,
                    background: "#f5f5f5",
                    borderRadius: styles.border.radius,
                    marginBottom: styles.spacing.small,
                  }}
                >
                  <Text style={styles.text}>
                    这段文字使用了响应式字体大小和行高
                  </Text>
                </div>
              </Col>
              <Col xs={24} md={12}>
                <div
                  style={{
                    padding: styles.container.padding,
                    background: "#e6f7ff",
                    borderRadius: styles.border.radius,
                    boxShadow: styles.shadow.card,
                  }}
                >
                  <Text strong style={styles.title}>
                    响应式容器样式
                  </Text>
                </div>
              </Col>
            </Row>

            {/* 显示/隐藏演示 */}
            <div style={{ marginTop: "16px" }}>
              <div style={styles.display.mobileOnly}>
                <Tag color="green">仅在移动设备显示</Tag>
              </div>
              <div style={styles.display.tabletOnly}>
                <Tag color="blue">仅在平板设备显示</Tag>
              </div>
              <div style={styles.display.desktopOnly}>
                <Tag color="purple">仅在桌面设备显示</Tag>
              </div>
              <div style={styles.display.smallScreenOnly}>
                <Tag color="orange">仅在小屏设备显示</Tag>
              </div>
              <div style={styles.display.largeScreenOnly}>
                <Tag color="red">仅在大屏设备显示</Tag>
              </div>
            </div>
          </Card>
        </ResponsiveContainer>
      </MainLayout>
    </AuthGuard>
  );
}
