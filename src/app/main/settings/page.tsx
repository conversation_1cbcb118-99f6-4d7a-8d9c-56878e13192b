'use client';

import React, { useState, useEffect } from 'react';
import {
  Tabs,
  Card,
  Form,
  message,
  Avatar,
  Upload,
  But<PERSON>,
  Divider,
  <PERSON><PERSON>,
} from 'antd';
import {
  SettingOutlined,
  UserOutlined,
  SafetyOutlined,
  BellOutlined,
  EyeOutlined,
  LockOutlined,
  GlobalOutlined,
  MailOutlined,
  CloudOutlined,
  DatabaseOutlined,
  UploadOutlined,
  CameraOutlined,
} from '@ant-design/icons';
import MainLayout from '@/components/layout/MainLayout';
import AuthGuard from '@/components/auth/AuthGuard';
import { BaseForm, TextField, TextAreaField, SelectField, SwitchField, NumberField, PasswordField } from '@/components/form';
import { useUserStore } from '@/store/useUserStore';
// import { http } from '@/lib/request';

const { TabPane } = Tabs;

// 系统设置数据类型
interface SystemSettings {
  siteName: string;
  siteDescription: string;
  siteKeywords: string;
  siteLogo?: string;
  siteUrl: string;
  enableRegistration: boolean;
  enableEmailVerification: boolean;
  enableTwoFactor: boolean;
  passwordMinLength: number;
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  emailProvider: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  emailFrom: string;
  emailFromName: string;
  storageProvider: string;
  maxFileSize: number;
  allowedFileTypes: string[];
  cacheEnabled: boolean;
  cacheDuration: number;
  timezone: string;
  language: string;
  dateFormat: string;
  currency: string;
}

// 用户设置数据类型
interface UserSettings {
  userId: number;
  nickname: string;
  avatar?: string;
  bio?: string;
  phone?: string;
  theme: string;
  language: string;
  timezone: string;
  dateFormat: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  notificationTypes: string[];
  profilePublic: boolean;
  showEmail: boolean;
  showPhone: boolean;
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
}

export default function SettingsPage() {
  const { userInfo } = useUserStore();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  // 系统设置状态
  const [systemSettings, setSystemSettings] = useState<SystemSettings | null>(null);
  const [systemForm] = Form.useForm();

  // 用户设置状态
  const [userSettings, setUserSettings] = useState<UserSettings | null>(null);
  const [profileForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [privacyForm] = Form.useForm();

  // 选项配置
  const options = {
    theme: [
      { label: '浅色主题', value: 'light' },
      { label: '深色主题', value: 'dark' },
      { label: '跟随系统', value: 'auto' },
    ],
    language: [
      { label: '简体中文', value: 'zh-CN' },
      { label: 'English', value: 'en-US' },
      { label: '繁體中文', value: 'zh-TW' },
    ],
    timezone: [
      { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
      { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
      { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
      { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
    ],
    dateFormat: [
      { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
      { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },
      { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
    ],
    currency: [
      { label: '人民币 (CNY)', value: 'CNY' },
      { label: '美元 (USD)', value: 'USD' },
      { label: '欧元 (EUR)', value: 'EUR' },
    ],
    emailProvider: [
      { label: 'SMTP', value: 'smtp' },
      { label: 'SendGrid', value: 'sendgrid' },
      { label: 'Mailgun', value: 'mailgun' },
    ],
    storageProvider: [
      { label: '本地存储', value: 'local' },
      { label: '阿里云OSS', value: 'oss' },
      { label: 'Amazon S3', value: 's3' },
      { label: '七牛云', value: 'qiniu' },
    ],
    notificationTypes: [
      { label: '系统通知', value: 'system' },
      { label: '安全通知', value: 'security' },
      { label: '内容通知', value: 'content' },
      { label: '营销通知', value: 'marketing' },
    ],
  };

  // 获取系统设置
  const fetchSystemSettings = async () => {
    try {
      setLoading(true);
      // const response = await http.get('/settings?type=system');

      // if (response.data.success) {
      //   setSystemSettings(response.data.data);
      //   systemForm.setFieldsValue(response.data.data);
      // } else {
      //   message.error(response.data.message || '获取系统设置失败');
      // }
    } catch (error) {
      console.error('获取系统设置错误:', error);
      message.error('获取系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户设置
  const fetchUserSettings = async () => {
    try {
      setLoading(true);
      // const response = await http.get('/settings?type=user');

      // if (response.data.success) {
      //   const data = response.data.data;
      //   setUserSettings(data);
      //   profileForm.setFieldsValue(data);
      //   securityForm.setFieldsValue(data);
      //   notificationForm.setFieldsValue(data);
      //   privacyForm.setFieldsValue(data);
      // } else {
      //   message.error(response.data.message || '获取用户设置失败');
      // }
    } catch (error) {
      console.error('获取用户设置错误:', error);
      message.error('获取用户设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchUserSettings();
    if (userInfo?.id === 1) {
      fetchSystemSettings();
    }
  }, [userInfo]);

  // 更新系统设置
  const handleSystemSettingsSubmit = async (values: any) => {
    try {
      setLoading(true);
      // const response = await http.put('/settings?type=system', values);

      // if (response.data.success) {
      //   message.success('更新系统设置成功');
      //   setSystemSettings(response.data.data);
      // } else {
      //   message.error(response.data.message || '更新系统设置失败');
      // }
    } catch (error) {
      console.error('更新系统设置错误:', error);
      message.error('更新系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新用户设置
  const handleUserSettingsSubmit = async (values: any) => {
    try {
      setLoading(true);
      // const response = await http.put('/settings?type=user', values);

      // if (response.data.success) {
      //   message.success('更新个人设置成功');
      //   setUserSettings(response.data.data);
      // } else {
      //   message.error(response.data.message || '更新个人设置失败');
      // }
    } catch (error) {
      console.error('更新个人设置错误:', error);
      message.error('更新个人设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 头像上传
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      const avatarUrl = info.file.response?.url;
      if (avatarUrl) {
        profileForm.setFieldsValue({ avatar: avatarUrl });
        message.success('头像上传成功');
      }
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };

  // 渲染个人资料设置
  const renderProfileSettings = () => (
    <Card title="个人资料" extra={<UserOutlined />}>
      <BaseForm
        form={profileForm}
        loading={loading}
        onFinish={handleUserSettingsSubmit}
        submitText="保存资料"
        showCancel={false}
      >
        <div style={{ marginBottom: '24px', textAlign: 'center' }}>
          <Avatar
            size={100}
            src={userSettings?.avatar}
            icon={<UserOutlined />}
            style={{ marginBottom: '16px' }}
          />
          <div>
            <Upload
              name="avatar"
              action="/api/upload"
              showUploadList={false}
              beforeUpload={(file) => {
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                  message.error('只能上传图片文件');
                }
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isLt2M) {
                  message.error('图片大小不能超过2MB');
                }
                return isImage && isLt2M;
              }}
              onChange={handleAvatarUpload}
            >
              <Button icon={<CameraOutlined />}>更换头像</Button>
            </Upload>
          </div>
        </div>

        <TextField
          label="昵称"
          name="nickname"
          required
          placeholder="请输入昵称"
        />

        <TextField
          label="手机号"
          name="phone"
          placeholder="请输入手机号"
        />

        <TextAreaField
          label="个人简介"
          name="bio"
          placeholder="请输入个人简介"
          rows={4}
          maxLength={200}
          showCount
        />

        <SelectField
          label="主题偏好"
          name="theme"
          placeholder="请选择主题"
          options={options.theme}
        />

        <SelectField
          label="语言"
          name="language"
          placeholder="请选择语言"
          options={options.language}
        />

        <SelectField
          label="时区"
          name="timezone"
          placeholder="请选择时区"
          options={options.timezone}
        />

        <SelectField
          label="日期格式"
          name="dateFormat"
          placeholder="请选择日期格式"
          options={options.dateFormat}
        />
      </BaseForm>
    </Card>
  );

  // 渲染安全设置
  const renderSecuritySettings = () => (
    <Card title="安全设置" extra={<SafetyOutlined />}>
      <BaseForm
        form={securityForm}
        loading={loading}
        onFinish={handleUserSettingsSubmit}
        submitText="保存设置"
        showCancel={false}
      >
        <Alert
          message="安全提醒"
          description="为了保护您的账户安全，建议启用双因子认证和登录通知。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <SwitchField
          label="双因子认证"
          name="twoFactorEnabled"
          checkedChildren="已启用"
          unCheckedChildren="已禁用"
          help="启用后登录时需要额外的验证码"
        />

        <SwitchField
          label="登录通知"
          name="loginNotifications"
          checkedChildren="已启用"
          unCheckedChildren="已禁用"
          help="新设备登录时发送通知"
        />

        <Divider />

        <div style={{ marginBottom: '16px' }}>
          <h4>修改密码</h4>
          <p style={{ color: '#666', fontSize: '14px' }}>
            定期修改密码可以提高账户安全性
          </p>
        </div>

        <PasswordField
          label="当前密码"
          name="currentPassword"
          placeholder="请输入当前密码"
        />

        <PasswordField
          label="新密码"
          name="newPassword"
          placeholder="请输入新密码"
        />

        <PasswordField
          label="确认新密码"
          name="confirmPassword"
          placeholder="请再次输入新密码"
        />
      </BaseForm>
    </Card>
  );

  // 渲染通知设置
  const renderNotificationSettings = () => (
    <Card title="通知设置" extra={<BellOutlined />}>
      <BaseForm
        form={notificationForm}
        loading={loading}
        onFinish={handleUserSettingsSubmit}
        submitText="保存设置"
        showCancel={false}
      >
        <SwitchField
          label="邮件通知"
          name="emailNotifications"
          checkedChildren="已启用"
          unCheckedChildren="已禁用"
        />

        <SwitchField
          label="短信通知"
          name="smsNotifications"
          checkedChildren="已启用"
          unCheckedChildren="已禁用"
        />

        <SwitchField
          label="推送通知"
          name="pushNotifications"
          checkedChildren="已启用"
          unCheckedChildren="已禁用"
        />

        <SelectField
          label="通知类型"
          name="notificationTypes"
          placeholder="请选择要接收的通知类型"
          options={options.notificationTypes}
          mode="multiple"
        />
      </BaseForm>
    </Card>
  );

  // 渲染隐私设置
  const renderPrivacySettings = () => (
    <Card title="隐私设置" extra={<EyeOutlined />}>
      <BaseForm
        form={privacyForm}
        loading={loading}
        onFinish={handleUserSettingsSubmit}
        submitText="保存设置"
        showCancel={false}
      >
        <SwitchField
          label="公开个人资料"
          name="profilePublic"
          checkedChildren="公开"
          unCheckedChildren="私密"
          help="其他用户是否可以查看您的个人资料"
        />

        <SwitchField
          label="显示邮箱"
          name="showEmail"
          checkedChildren="显示"
          unCheckedChildren="隐藏"
          help="在个人资料中是否显示邮箱地址"
        />

        <SwitchField
          label="显示手机号"
          name="showPhone"
          checkedChildren="显示"
          unCheckedChildren="隐藏"
          help="在个人资料中是否显示手机号码"
        />
      </BaseForm>
    </Card>
  );

  // 渲染系统设置
  const renderSystemSettings = () => (
    <Tabs defaultActiveKey="basic" type="card">
      <TabPane
        tab={
          <span>
            <GlobalOutlined />
            基本设置
          </span>
        }
        key="basic"
      >
        <Card>
          <BaseForm
            form={systemForm}
            loading={loading}
            onFinish={handleSystemSettingsSubmit}
            submitText="保存设置"
            showCancel={false}
            sections={[
              {
                title: '网站信息',
                children: (
                  <>
                    <TextField
                      label="网站名称"
                      name="siteName"
                      required
                      placeholder="请输入网站名称"
                    />

                    <TextField
                      label="网站URL"
                      name="siteUrl"
                      required
                      placeholder="请输入网站URL"
                    />

                    <TextAreaField
                      label="网站描述"
                      name="siteDescription"
                      placeholder="请输入网站描述"
                      rows={3}
                    />

                    <TextField
                      label="关键词"
                      name="siteKeywords"
                      placeholder="请输入关键词，用逗号分隔"
                    />

                    <TextField
                      label="网站Logo"
                      name="siteLogo"
                      placeholder="请输入Logo URL"
                    />
                  </>
                ),
              },
              {
                title: '功能设置',
                children: (
                  <>
                    <SwitchField
                      label="允许用户注册"
                      name="enableRegistration"
                      checkedChildren="允许"
                      unCheckedChildren="禁止"
                    />

                    <SwitchField
                      label="邮箱验证"
                      name="enableEmailVerification"
                      checkedChildren="必须"
                      unCheckedChildren="可选"
                    />

                    <SwitchField
                      label="双因子认证"
                      name="enableTwoFactor"
                      checkedChildren="启用"
                      unCheckedChildren="禁用"
                    />
                  </>
                ),
              },
              {
                title: '地区设置',
                children: (
                  <>
                    <SelectField
                      label="默认时区"
                      name="timezone"
                      placeholder="请选择时区"
                      options={options.timezone}
                    />

                    <SelectField
                      label="默认语言"
                      name="language"
                      placeholder="请选择语言"
                      options={options.language}
                    />

                    <SelectField
                      label="日期格式"
                      name="dateFormat"
                      placeholder="请选择日期格式"
                      options={options.dateFormat}
                    />

                    <SelectField
                      label="货币单位"
                      name="currency"
                      placeholder="请选择货币"
                      options={options.currency}
                    />
                  </>
                ),
              },
            ]}
          />
        </Card>
      </TabPane>

      <TabPane
        tab={
          <span>
            <LockOutlined />
            安全设置
          </span>
        }
        key="security"
      >
        <Card>
          <BaseForm
            form={systemForm}
            loading={loading}
            onFinish={handleSystemSettingsSubmit}
            submitText="保存设置"
            showCancel={false}
          >
            <NumberField
              label="密码最小长度"
              name="passwordMinLength"
              min={6}
              max={32}
              placeholder="请输入密码最小长度"
            />

            <NumberField
              label="会话超时时间"
              name="sessionTimeout"
              min={5}
              max={10080}
              placeholder="请输入超时时间（分钟）"
              help="用户无操作后自动退出的时间"
            />

            <NumberField
              label="最大登录尝试次数"
              name="maxLoginAttempts"
              min={3}
              max={10}
              placeholder="请输入最大尝试次数"
            />

            <NumberField
              label="锁定持续时间"
              name="lockoutDuration"
              min={5}
              max={1440}
              placeholder="请输入锁定时间（分钟）"
            />
          </BaseForm>
        </Card>
      </TabPane>

      <TabPane
        tab={
          <span>
            <MailOutlined />
            邮件设置
          </span>
        }
        key="email"
      >
        <Card>
          <BaseForm
            form={systemForm}
            loading={loading}
            onFinish={handleSystemSettingsSubmit}
            submitText="保存设置"
            showCancel={false}
          >
            <SelectField
              label="邮件服务商"
              name="emailProvider"
              placeholder="请选择邮件服务商"
              options={options.emailProvider}
            />

            <TextField
              label="SMTP主机"
              name="smtpHost"
              placeholder="请输入SMTP主机地址"
            />

            <NumberField
              label="SMTP端口"
              name="smtpPort"
              placeholder="请输入SMTP端口"
            />

            <TextField
              label="SMTP用户名"
              name="smtpUser"
              placeholder="请输入SMTP用户名"
            />

            <TextField
              label="发件人邮箱"
              name="emailFrom"
              placeholder="请输入发件人邮箱"
            />

            <TextField
              label="发件人名称"
              name="emailFromName"
              placeholder="请输入发件人名称"
            />
          </BaseForm>
        </Card>
      </TabPane>

      <TabPane
        tab={
          <span>
            <CloudOutlined />
            存储设置
          </span>
        }
        key="storage"
      >
        <Card>
          <BaseForm
            form={systemForm}
            loading={loading}
            onFinish={handleSystemSettingsSubmit}
            submitText="保存设置"
            showCancel={false}
          >
            <SelectField
              label="存储服务商"
              name="storageProvider"
              placeholder="请选择存储服务商"
              options={options.storageProvider}
            />

            <NumberField
              label="最大文件大小"
              name="maxFileSize"
              min={1}
              max={100}
              placeholder="请输入最大文件大小（MB）"
            />

            <SelectField
              label="允许的文件类型"
              name="allowedFileTypes"
              placeholder="请选择允许的文件类型"
              mode="multiple"
              options={[
                { label: 'JPG', value: 'jpg' },
                { label: 'PNG', value: 'png' },
                { label: 'GIF', value: 'gif' },
                { label: 'PDF', value: 'pdf' },
                { label: 'DOC', value: 'doc' },
                { label: 'DOCX', value: 'docx' },
                { label: 'XLS', value: 'xls' },
                { label: 'XLSX', value: 'xlsx' },
              ]}
            />
          </BaseForm>
        </Card>
      </TabPane>

      <TabPane
        tab={
          <span>
            <DatabaseOutlined />
            缓存设置
          </span>
        }
        key="cache"
      >
        <Card>
          <BaseForm
            form={systemForm}
            loading={loading}
            onFinish={handleSystemSettingsSubmit}
            submitText="保存设置"
            showCancel={false}
          >
            <SwitchField
              label="启用缓存"
              name="cacheEnabled"
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />

            <NumberField
              label="缓存时长"
              name="cacheDuration"
              min={60}
              max={86400}
              placeholder="请输入缓存时长（秒）"
            />
          </BaseForm>
        </Card>
      </TabPane>
    </Tabs>
  );

  return (
    <AuthGuard>
      <MainLayout>
        <div style={{ padding: '0' }}>
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              size="large"
              tabPosition="left"
              style={{ minHeight: '600px' }}
            >
              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    个人资料
                  </span>
                }
                key="profile"
              >
                {renderProfileSettings()}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <SafetyOutlined />
                    安全设置
                  </span>
                }
                key="security"
              >
                {renderSecuritySettings()}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <BellOutlined />
                    通知设置
                  </span>
                }
                key="notifications"
              >
                {renderNotificationSettings()}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <EyeOutlined />
                    隐私设置
                  </span>
                }
                key="privacy"
              >
                {renderPrivacySettings()}
              </TabPane>

              {userInfo?.id === 1 && (
                <TabPane
                  tab={
                    <span>
                      <SettingOutlined />
                      系统设置
                    </span>
                  }
                  key="system"
                >
                  {renderSystemSettings()}
                </TabPane>
              )}
            </Tabs>
          </Card>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
