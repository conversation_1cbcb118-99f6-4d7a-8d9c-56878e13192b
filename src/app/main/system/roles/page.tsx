'use client';

import React, { useState } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Select,
  Typography,
  Popconfirm,
  message,
  Tree,
  Divider,
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { DataNode } from 'antd/es/tree';
import MainLayout from '@/components/layout/MainLayout';
import AuthGuard from '@/components/auth/AuthGuard';

const { Title } = Typography;
const { TextArea } = Input;

// 角色数据接口
interface Role {
  id: number;
  name: string;
  code: string;
  description: string;
  status: 'active' | 'inactive';
  userCount: number;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

// 权限树数据
const permissionTreeData: DataNode[] = [
  {
    title: '系统管理',
    key: 'system',
    children: [
      { title: '用户管理', key: 'system:users' },
      { title: '角色管理', key: 'system:roles' },
      { title: '菜单管理', key: 'system:menus' },
      { title: '系统设置', key: 'system:settings' },
    ],
  },
  {
    title: '内容管理',
    key: 'content',
    children: [
      { title: '文章管理', key: 'content:articles' },
      { title: '分类管理', key: 'content:categories' },
      { title: '标签管理', key: 'content:tags' },
    ],
  },
  {
    title: '数据统计',
    key: 'analytics',
    children: [
      { title: '数据概览', key: 'analytics:overview' },
      { title: '用户统计', key: 'analytics:users' },
      { title: '内容统计', key: 'analytics:content' },
    ],
  },
];

// 模拟角色数据
const mockRoles: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'admin',
    description: '拥有系统所有权限',
    status: 'active',
    userCount: 1,
    permissions: ['*'],
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-01-01 10:00:00',
  },
  {
    id: 2,
    name: '内容管理员',
    code: 'content_admin',
    description: '负责内容管理相关功能',
    status: 'active',
    userCount: 3,
    permissions: ['content:articles', 'content:categories', 'content:tags'],
    createdAt: '2024-01-02 09:15:00',
    updatedAt: '2024-01-10 14:30:00',
  },
  {
    id: 3,
    name: '普通用户',
    code: 'user',
    description: '基础用户权限',
    status: 'active',
    userCount: 156,
    permissions: ['analytics:overview'],
    createdAt: '2024-01-03 11:30:00',
    updatedAt: '2024-01-03 11:30:00',
  },
];

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [form] = Form.useForm();

  // 状态配置
  const statusConfig = {
    active: { color: 'green', text: '启用' },
    inactive: { color: 'red', text: '禁用' },
  };

  // 新增/编辑角色
  const handleSave = async (values: any) => {
    try {
      setLoading(true);
      
      if (editingRole) {
        // 编辑
        setRoles(roles.map(role => 
          role.id === editingRole.id 
            ? { ...role, ...values, updatedAt: new Date().toLocaleString() }
            : role
        ));
        message.success('角色更新成功');
      } else {
        // 新增
        const newRole: Role = {
          id: Date.now(),
          ...values,
          userCount: 0,
          permissions: [],
          createdAt: new Date().toLocaleString(),
          updatedAt: new Date().toLocaleString(),
        };
        setRoles([...roles, newRole]);
        message.success('角色创建成功');
      }
      
      setModalVisible(false);
      setEditingRole(null);
      form.resetFields();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除角色
  const handleDelete = (id: number) => {
    setRoles(roles.filter(role => role.id !== id));
    message.success('删除成功');
  };

  // 设置权限
  const handleSetPermissions = (role: Role) => {
    setEditingRole(role);
    setSelectedPermissions(role.permissions);
    setPermissionModalVisible(true);
  };

  // 保存权限
  const handleSavePermissions = () => {
    if (editingRole) {
      setRoles(roles.map(role => 
        role.id === editingRole.id 
          ? { ...role, permissions: selectedPermissions, updatedAt: new Date().toLocaleString() }
          : role
      ));
      message.success('权限设置成功');
      setPermissionModalVisible(false);
      setEditingRole(null);
      setSelectedPermissions([]);
    }
  };

  // 表格列配置
  const columns: ColumnsType<Role> = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '角色编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (code) => <Tag color="blue">{code}</Tag>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: keyof typeof statusConfig) => (
        <Tag color={statusConfig[status].color}>
          {statusConfig[status].text}
        </Tag>
      ),
    },
    {
      title: '用户数',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 80,
      render: (count) => (
        <Space>
          <UserOutlined />
          {count}
        </Space>
      ),
    },
    {
      title: '权限数',
      key: 'permissionCount',
      width: 80,
      render: (_, record) => (
        <Tag color="orange">
          {record.permissions.includes('*') ? '全部' : record.permissions.length}
        </Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => {
              setEditingRole(record);
              form.setFieldsValue(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            icon={<SettingOutlined />} 
            size="small"
            onClick={() => handleSetPermissions(record)}
          >
            权限
          </Button>
          <Popconfirm
            title="确定要删除这个角色吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />} 
              size="small"
              disabled={record.code === 'admin'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <AuthGuard requiredPermissions={['system:roles']}>
      <MainLayout>
        <div>
          <Title level={2} style={{ marginBottom: '24px' }}>
            🛡️ 角色管理
          </Title>

          <Card>
            {/* 操作栏 */}
            <div style={{ marginBottom: '16px' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingRole(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                新增角色
              </Button>
            </div>

            {/* 角色表格 */}
            <Table
              columns={columns}
              dataSource={roles}
              rowKey="id"
              loading={loading}
              pagination={{
                total: roles.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </Card>

          {/* 新增/编辑角色弹窗 */}
          <Modal
            title={editingRole ? '编辑角色' : '新增角色'}
            open={modalVisible}
            onOk={() => form.submit()}
            onCancel={() => {
              setModalVisible(false);
              setEditingRole(null);
              form.resetFields();
            }}
            confirmLoading={loading}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
            >
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
              
              <Form.Item
                name="code"
                label="角色编码"
                rules={[
                  { required: true, message: '请输入角色编码' },
                  { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '编码只能包含字母、数字和下划线，且以字母或下划线开头' }
                ]}
              >
                <Input placeholder="请输入角色编码" disabled={editingRole?.code === 'admin'} />
              </Form.Item>
              
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Select.Option value="active">启用</Select.Option>
                  <Select.Option value="inactive">禁用</Select.Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="description"
                label="描述"
              >
                <TextArea rows={3} placeholder="请输入角色描述" />
              </Form.Item>
            </Form>
          </Modal>

          {/* 权限设置弹窗 */}
          <Modal
            title={`设置权限 - ${editingRole?.name}`}
            open={permissionModalVisible}
            onOk={handleSavePermissions}
            onCancel={() => {
              setPermissionModalVisible(false);
              setEditingRole(null);
              setSelectedPermissions([]);
            }}
            width={600}
          >
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button 
                  size="small" 
                  onClick={() => setSelectedPermissions(['*'])}
                >
                  全选
                </Button>
                <Button 
                  size="small" 
                  onClick={() => setSelectedPermissions([])}
                >
                  清空
                </Button>
              </Space>
            </div>
            <Divider />
            <Tree
              checkable
              checkedKeys={selectedPermissions}
              onCheck={(checkedKeys) => {
                setSelectedPermissions(checkedKeys as string[]);
              }}
              treeData={permissionTreeData}
            />
          </Modal>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
