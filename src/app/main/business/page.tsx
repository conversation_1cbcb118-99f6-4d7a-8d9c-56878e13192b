"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { useUserStore } from "@/store/useUserStore";
import { defHttp } from "@/lib/axios";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 商户数据接口
interface Business {
  id: number;
  name: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  type: number;
  status: number;
  createdAt: string;
  updatedAt: string;
}

// 分页信息类型
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 商户表单数据类型
interface BusinessFormData {
  name: string;
  description: string;
  email?: string;
  phone?: string;
  address?: string;
  type: number;
  status: number;
}

export default function UsersPage() {
  const [business, setBusiness] = useState<Business[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<Business | null>(null);
  const [form] = Form.useForm();

  // 分页状态
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [roleFilter, setRoleFilter] = useState<string>("");

  // 角色选项
  const typeOptions = [
    { label: "合同类型1", value: "1", color: "red" },
    { label: "合同类型2", value: "2", color: "orange" },
    { label: "合同类型3", value: "3", color: "blue" },
  ];

  // 状态选项
  const statusOptions = [
    { label: "正常", value: "1", color: "success" },
    { label: "禁用", value: "2", color: "warning" },
    { label: "封禁", value: "3", color: "error" },
  ];

  // 获取商户列表
  const fetchbusiness = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
        ...(roleFilter && { role: roleFilter }),
      });

      const response = await defHttp.get({ url: `/business`, params });

      setBusiness(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取商户列表错误:", error);
      message.error("获取商户列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchbusiness();
  }, [searchKeyword, statusFilter, roleFilter]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleRoleFilter = (value: string) => {
    setRoleFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理分页
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchbusiness(current, pageSize);
  };

  // 新增/编辑商户
  const handleSave = async (values: BusinessFormData) => {
    try {
      setLoading(true);

      if (editingUser) {
        // 编辑
        await defHttp.put({
          url: `/business/${editingUser.id}`,
          data: values,
        });
        message.success("更新商户成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchbusiness(pagination.current, pagination.pageSize);
      } else {
        // 新增
        await defHttp.post({ url: "/business", data: values });

        message.success("创建商户成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchbusiness(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除商户
  const handleDelete = async (businessId: number) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/business/${businessId}` });

      if (response.data.success) {
        message.success("删除商户成功");
        fetchbusiness(pagination.current, pagination.pageSize);
      } else {
        message.error(response.data.message || "删除商户失败");
      }
    } catch (error) {
      console.error("删除商户错误:", error);
      message.error("删除商户失败");
    } finally {
      setLoading(false);
    }
  };

  // 表格列配置
  const columns: ColumnsType<Business> = [
    {
      title: "商户信息",
      key: "name",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.name}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.description}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "联系方式",
      key: "contact",
      width: 180,
      render: (_, record) => (
        <div>
          <div>{record.email}</div>
          {record.phone && (
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.phone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "合同类型",
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (type: number) => {
        const typeOPtion = typeOptions.find(
          (opt) => opt.value === type.toString()
        );
        return typeOPtion?.label || type;
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: number) => {
        const statusOption = statusOptions.find(
          (opt) => opt.value === status.toString()
        );
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "最后登录",
      dataIndex: "lastLoginTime",
      key: "lastLoginTime",
      width: 150,
      render: (time: string) =>
        time ? new Date(time).toLocaleString() : "从未登录",
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingUser(record);
                form.setFieldsValue({
                  name: record.name,
                  email: record.email,
                  phone: record.phone,
                  type: record.type,
                  status: record.status,
                  address: record.address,
                  description: record.description,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个商户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>商户管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统商户，包括商户信息、角色权限等{business.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchbusiness(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingUser(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增商户
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索商户名"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选角色"
            allowClear
            style={{ width: 120 }}
            onChange={handleRoleFilter}
          >
            {typeOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 商户表格 */}
        <Table
          columns={columns}
          dataSource={business}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑商户弹窗 */}
      <Modal
        title={editingUser ? "编辑商户" : "新增商户"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "1",
            type: "1",
          }}
        >
          {!editingUser && (
            <Form.Item
              name="name"
              label="商户名"
              rules={[
                { required: true, message: "请输入商户名" },
                { min: 3, message: "商户名至少3个字符" },
              ]}
            >
              <Input placeholder="请输入商户名" />
            </Form.Item>
          )}
          <Form.Item name="description" label="描述">
            <Input placeholder="请输入描述" />
          </Form.Item>
          <Form.Item
            name="address"
            label="地址"
            rules={[{ required: true, message: "请输入地址" }]}
          >
            <Input placeholder="请输入地址" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[{ type: "email", message: "请输入有效的邮箱地址" }]}
          >
            <Input placeholder="请输入邮箱地址" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { required: true, message: "请输入邮箱" },
              { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Form.Item
            name="type"
            label="合同类型"
            rules={[{ required: true, message: "请选择合同类型" }]}
          >
            <Select placeholder="请选择角色" options={typeOptions} />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingUser(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingUser ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
