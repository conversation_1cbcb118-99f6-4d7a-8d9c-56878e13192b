"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Tooltip,
  Rate,
  Upload,
  Image,
} from "antd";
import ImgCrop from "antd-img-crop";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  LoadingOutlined,
  UserOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";

import { defHttp } from "@/lib/axios";
import { useSearchParams } from "next/navigation";
import { useSearch } from "../useSearch";
import { formatBeijingTime } from "@/utils/dateUtils";

const { Search } = Input;
const { Option } = Select;

// 评论数据接口
interface Review {
  id: number;
  content: string;
  rating: number;
  createdAt: string;
  updatedAt: string;
  images: string[];
  shopUser: {
    id: number;
    name: string;
    phone: string;
    avatar: string;
  };
  shop: {
    id: number;
    name: string;
  };
  order: {
    id: string;
    orderNo: string;
  };
}

// 分页信息接口
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
}

// 评论表单数据接口
interface ReviewFormData {
  content: string;
  rating: number;
  images: string[];
  shopId: number;
  shopUserId?: number;
  orderId?: string;
}

const ReviewPage: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [form] = Form.useForm<ReviewFormData>();
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  //shopId
  const searchParams = useSearchParams();
  const shopId = searchParams.get("shopId");

  // 获取评论列表
  const fetchReviews = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
        ...(shopId && { shopId: shopId }),
      });

      const response = await defHttp.get({ url: `/shop/review`, params });

      setReviews(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取评论列表错误:", error);
      message.error("获取评论列表失败");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchReviews();
  }, [searchKeyword, statusFilter]);

  // 搜索评论
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 筛选评论状态
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 分页变化
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchReviews(current, pageSize);
  };

  // 搜索
  const { searchResults, handleSearchBusiness, handleSearchResults } =
    useSearch((params: { name: string; pageSize: number; type: string }) => {
      params = { ...params, type: "WASH" };
      return defHttp.get({ url: `/order/list`, params });
    }, "orderNo");

  // 新增/编辑评论
  const handleSave = async (values: ReviewFormData) => {
    try {
      setLoading(true);

      values.images = carouselImages.map((item) => item.url);
      shopId && (values.shopId = Number(shopId));

      values.shopUserId = 1;

      if (editingReview) {
        // 编辑
        await defHttp.put({
          url: `/shop/review/${editingReview.id}`,
          data: values,
        });
        message.success("更新评论成功");
        setModalVisible(false);
        setEditingReview(null);
        form.resetFields();
        fetchReviews(pagination.current, pagination.pageSize);
      } else {
        // 新增
        await defHttp.post({ url: "/shop/review", data: values });

        message.success("创建评论成功");
        setModalVisible(false);
        setEditingReview(null);
        form.resetFields();
        fetchReviews(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除评论
  const handleDelete = async (reviewId: number) => {
    try {
      setLoading(true);
      await defHttp.delete({ url: `/shop/review/${reviewId}` });

      message.success("删除评论成功");
      fetchReviews(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error("删除评论错误:", error);
      message.error("删除评论失败");
    } finally {
      setLoading(false);
    }
  };

  const statusOptions = [
    { value: "1", label: "正常" },
    { value: "0", label: "禁用" },
  ];

  const [carouselImages, setCarouselImages] = useState<any[]>([]);
  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("只能上传 JPG/PNG 文件!");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("图片大小不能超过 2MB!");
      return false;
    }
    return true;
  };

  const handleCarouselUpload = ({ file }: any) => {
    if (!file) return;

    let updatedImages = [...carouselImages];

    // ✅ 处理图片删除
    if (file.status === "removed") {
      updatedImages = updatedImages.filter((item) => item.uid !== file.uid);
    } else {
      // ✅ 查找图片索引
      const index = updatedImages.findIndex((item) => item.uid === file.uid);

      const imgObj = {
        uid: file.uid,
        status: file.status,
        url: file.status === "done" ? file.response?.data?.url : undefined,
      };

      if (index !== -1) {
        // ✅ 更新已存在的图片
        updatedImages[index] = imgObj;
      } else {
        // ✅ 添加新图片
        updatedImages.push(imgObj);

        if (file.status === "done") {
          message.success("图片上传成功");
        } else if (file.status === "error") {
          message.error("图片上传失败");
        }
      }
    }

    // ✅ 统一更新状态 & 表单
    setCarouselImages(updatedImages);
  };

  // 评论表格列定义
  const columns: ColumnsType<Review> = [
    {
      title: "用户信息",
      key: "userInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            size={40}
            icon={<UserOutlined />}
            src={record.shopUser.avatar}
          />
          <div>
            <div style={{ fontWeight: "bold" }}>{record.shopUser.name}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.shopUser.phone}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "店铺信息",
      key: "shopInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.shop.name}</div>
          </div>
        </Space>
      ),
    },
    {
      title: "订单信息",
      key: "orderInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.order.orderNo}</div>
          </div>
        </Space>
      ),
    },
    {
      title: "评论内容",
      dataIndex: "content",
      key: "content",
      width: 200,
      ellipsis: true,
    },
    {
      title: "图片",
      key: "images",
      width: 100,
      render: (_, record) => (
        <Space>
          {record.images.map((url) => (
            <Image key={url} src={url} width={60} />
          ))}
        </Space>
      ),
    },
    {
      title: "评分",
      dataIndex: "rating",
      key: "rating",
      width: 100,
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 200,
      render: (time: string) => formatBeijingTime(time),
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 200,
      render: (time: string) => formatBeijingTime(time),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingReview(record);
                form.setFieldsValue({
                  content: record.content,
                  rating: record.rating,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个评论吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>评论管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统评论，包括评论信息、角色权限等{reviews.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchReviews(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingReview(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增评论
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索评论内容"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>
        <Table
          columns={columns}
          dataSource={reviews}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
        <Modal
          title={editingReview ? "编辑评论" : "新增评论"}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            setEditingReview(null);
            form.resetFields();
          }}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            initialValues={{
              status: "1",
            }}
          >
            <Form.Item name="orderId" label="订单号">
              <Select
                placeholder="请选择订单"
                showSearch
                filterOption={false}
                onSearch={handleSearchResults}
                onChange={handleSearchResults}
                notFoundContent={null}
                options={searchResults}
              />
            </Form.Item>
            <Form.Item name="content" label="评论内容">
              <Input.TextArea placeholder="请输入评论内容" />
            </Form.Item>

            <Form.Item
              name="rating"
              label="评分"
              rules={[{ required: true, message: "请输入评分" }]}
            >
              <Rate />
            </Form.Item>

            <Form.Item name="carouselImages" label="图片">
              <ImgCrop rotationSlider aspect={9 / 10}>
                <Upload
                  name="file"
                  listType="picture-card"
                  className="avatar-uploader"
                  action="/api/upload"
                  onChange={handleCarouselUpload}
                  beforeUpload={beforeUpload}
                  fileList={carouselImages}
                  maxCount={6}
                >
                  {carouselImages.length < 7 && uploadButton}
                </Upload>
              </ImgCrop>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
              <Space>
                <Button
                  onClick={() => {
                    setModalVisible(false);
                    setEditingReview(null);
                    form.resetFields();
                  }}
                >
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editingReview ? "更新" : "创建"}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default ReviewPage;
