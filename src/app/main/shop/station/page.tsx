"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
  Upload,
} from "antd";
import ImgCrop from "antd-img-crop";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { useUserStore } from "@/store/useUserStore";
import { defHttp } from "@/lib/axios";
import { useSearchParams } from "next/navigation";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 工位数据接口
interface Station {
  id: number;
  name: string;
  description: string;
  status: string;
  station_hours: string;
  createdAt: string;
  updatedAt: string;
  stationId: number;
  price: number;
  thumbnailImg: string;
}

// 分页信息类型
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 工位表单数据类型
interface StationFormData {
  description?: string;
  name: string;
  status: string;
  thumbnailImg?: string;
  price: number;
}

export default function UsersPage() {
  const [station, setStation] = useState<Station[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<Station | null>(null);
  const [form] = Form.useForm();

  //shopId
  const searchParams = useSearchParams();
  const shopId = searchParams.get("shopId");

  // 分页状态
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");

  // 状态选项
  const statusOptions = [
    { label: "使用中", value: "IN_USE", color: "success" },
    { label: "空闲", value: "IDLE", color: "warning" },
    { label: "维护中", value: "MAINTENANCE", color: "error" },
  ];

  // 获取工位列表
  const fetchstation = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
      });

      const response = await defHttp.get({ url: `/station`, params });

      setStation(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取工位列表错误:", error);
      message.error("获取工位列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchstation();
  }, [searchKeyword, statusFilter]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理分页
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchstation(current, pageSize);
  };

  // 新增/编辑工位
  const handleSave = async (values: StationFormData) => {
    try {
      setLoading(true);
      values.thumbnailImg = imageUrl;

      if (editingUser) {
        // 编辑
        const response = await defHttp.put({
          url: `/station/${editingUser.id}`,
          data: { ...values, shopId: shopId },
        });

        message.success("更新工位成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchstation(pagination.current, pagination.pageSize);
      } else {
        // 新增
        const response = await defHttp.post({
          url: "/station",
          data: { ...values, shopId: shopId },
        });

        message.success("创建工位成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchstation(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除工位
  const handleDelete = async (stationId: number) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/station/${stationId}` });

      message.success("删除工位成功");
      fetchstation(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error("删除工位错误:", error);
      message.error("删除工位失败");
    } finally {
      setLoading(false);
    }
  };

  const [imageUrl, setImageUrl] = useState<string | undefined>(undefined);
  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("只能上传 JPG/PNG 文件!");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("图片大小不能超过 2MB!");
      return false;
    }
    return true;
  };
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === "done") {
      const avatarUrl = info.file.response?.data.url;
      if (avatarUrl) {
        setImageUrl(avatarUrl);
        form.setFieldsValue({ thumbnail_img: avatarUrl });
        message.success("头像上传成功");
      }
    } else if (info.file.status === "error") {
      message.error("头像上传失败");
    }
  };

  // 表格列配置
  const columns: ColumnsType<Station> = [
    {
      title: "工位信息",
      key: "name",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.name}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.description}
            </div>
          </div>
        </Space>
      ),
    },

    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        const statusOption = statusOptions.find((opt) => opt.value === status);
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "价格",
      dataIndex: "price",
      key: "price",
      width: 150,
      render: (price: number) => price + "/分钟",
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingUser(record);
                form.setFieldsValue({
                  name: record.name,
                  status: record.status,
                  description: record.description,
                  price: record.price,
                  thumbnailImg: record.thumbnailImg,
                });
                setImageUrl(record.thumbnailImg);
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个工位吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                message.info(`查看工位: ${record.name}`);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>工位管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统工位，包括工位信息、角色权限等{station.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchstation(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingUser(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增工位
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索工位名"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 工位表格 */}
        <Table
          columns={columns}
          dataSource={station}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑工位弹窗 */}
      <Modal
        title={editingUser ? "编辑工位" : "新增工位"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "IDLE",
          }}
        >
          {!editingUser && (
            <Form.Item
              name="name"
              label="工位名"
              rules={[
                { required: true, message: "请输入工位名" },
                { min: 3, message: "工位名至少3个字符" },
              ]}
            >
              <Input placeholder="请输入工位名" />
            </Form.Item>
          )}
          <Form.Item name="thumbnailImg" label="店铺图片">
            <ImgCrop rotationSlider>
              <Upload
                name="file"
                listType="picture-card"
                className="avatar-uploader"
                action="/api/upload"
                onChange={handleAvatarUpload}
                beforeUpload={beforeUpload}
                showUploadList={false}
              >
                {imageUrl ? (
                  <img src={imageUrl} alt="avatar" style={{ width: "100%" }} />
                ) : (
                  uploadButton
                )}
              </Upload>
            </ImgCrop>
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input placeholder="请输入描述" />
          </Form.Item>

          <Form.Item
            name="price"
            label="价格"
            rules={[{ required: true, message: "请输入价格" }]}
          >
            <Input type="number" placeholder="请输入价格" suffix="/分钟" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingUser(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingUser ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
