"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
  Upload,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  LoadingOutlined,
  UserOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import ImgCrop from "antd-img-crop";
import { useUserStore } from "@/store/useUserStore";
import { defHttp } from "@/lib/axios";
import { useSearch } from "./useSearch";
import { useRouter } from "next/navigation";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 店铺数据接口
interface Shop {
  id: number;
  name: string;
  description: string;
  address: string;
  iphone: string;
  status: string;
  businessHours: string;
  createdAt: string;
  updatedAt: string;
  shopId: number;
  thumbnailImg?: string;
  businessId: number;
  carouselImages: string[];
  avgRating: number;
}

// 分页信息类型
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 店铺表单数据类型
interface ShopFormData {
  description?: string;
  name: string;
  address?: string;
  phone?: string;
  status: string;
  businessHours?: string;
  thumbnailImg?: string;
  businessId: number;
  carouselImages: string[];
}

export default function UsersPage() {
  const router = useRouter();
  const [shop, setShop] = useState<Shop[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<Shop | null>(null);
  const [form] = Form.useForm();

  // 分页状态
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");

  // 状态选项
  const statusOptions = [
    { label: "正常", value: "ACTIVE", color: "success" },
    { label: "暂停营业", value: "PAUSED", color: "warning" },
    { label: "已关闭", value: "CLOSED", color: "error" },
  ];

  // 搜索
  const { searchResults, handleSearchBusiness, handleSearchResults } =
    useSearch((params: { name: string; pageSize: number }) =>
      defHttp.get({ url: `/business`, params })
    );

  // 获取店铺列表
  const fetchshop = useCallback(
    async (page = 1, pageSize = 10) => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(searchKeyword && { keyword: searchKeyword }),
          ...(statusFilter && { status: statusFilter }),
        });

        const response = await defHttp.get({ url: `/shop`, params });

        setShop(response.list);
        setPagination(response.pagination);
      } catch (error) {
        console.error("获取店铺列表错误:", error);
        message.error("获取店铺列表失败");
      } finally {
        setLoading(false);
      }
    },
    [searchKeyword, statusFilter]
  );

  // 初始化加载
  useEffect(() => {
    console.log("aaaaa", searchKeyword, statusFilter);
    fetchshop();
  }, [fetchshop]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理分页
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchshop(current, pageSize);
  };

  // 新增/编辑店铺
  const handleSave = async (values: ShopFormData) => {
    values.thumbnailImg = imageUrl;
    values.carouselImages = carouselImages.map(
      (item: { url: string }, index) => ({
        imageUrl: item.url,
        order: index,
      })
    ) as any[];
    try {
      setLoading(true);

      if (editingUser) {
        // 编辑
        const response = await defHttp.put({
          url: `/shop/${editingUser.id}`,
          data: values,
        });

        message.success("更新店铺成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchshop(pagination.current, pagination.pageSize);
      } else {
        // 新增
        const response = await defHttp.post({ url: "/shop", data: values });

        message.success("创建店铺成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchshop(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除店铺
  const handleDelete = async (shopId: number) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/shop/${shopId}` });

      message.success("删除店铺成功");
      fetchshop(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error("删除店铺错误:", error);
      message.error("删除店铺失败");
    } finally {
      setLoading(false);
    }
  };

  const [imageUrl, setImageUrl] = useState<string | undefined>(undefined);
  const [carouselImages, setCarouselImages] = useState<any[]>([]);
  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("只能上传 JPG/PNG 文件!");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("图片大小不能超过 2MB!");
      return false;
    }
    return true;
  };
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === "done") {
      const avatarUrl = info.file.response?.data.url;
      if (avatarUrl) {
        setImageUrl(avatarUrl);
        form.setFieldsValue({ thumbnail_img: avatarUrl });
        message.success("头像上传成功");
      }
    } else if (info.file.status === "error") {
      message.error("头像上传失败");
    }
  };

  const handleCarouselUpload = ({ file }: any) => {
    if (!file) return;

    let updatedImages = [...carouselImages];

    // ✅ 处理图片删除
    if (file.status === "removed") {
      updatedImages = updatedImages.filter((item) => item.uid !== file.uid);
    } else {
      // ✅ 查找图片索引
      const index = updatedImages.findIndex((item) => item.uid === file.uid);

      const imgObj = {
        uid: file.uid,
        status: file.status,
        url: file.status === "done" ? file.response?.data?.url : undefined,
      };

      if (index !== -1) {
        // ✅ 更新已存在的图片
        updatedImages[index] = imgObj;
      } else {
        // ✅ 添加新图片
        updatedImages.push(imgObj);

        if (file.status === "done") {
          message.success("图片上传成功");
        } else if (file.status === "error") {
          message.error("图片上传失败");
        }
      }
    }

    // ✅ 统一更新状态 & 表单
    setCarouselImages(updatedImages);
    form.setFieldsValue({ carouselImages: updatedImages });
  };

  // 表格列配置
  const columns: ColumnsType<Shop> = [
    {
      title: "店铺信息",
      key: "name",
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            size={40}
            shape="square"
            icon={<UserOutlined />}
            src={record.thumbnailImg}
          />

          <div>
            <div style={{ fontWeight: "bold" }}>{record.name}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.description}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "联系方式",
      key: "contact",
      width: 180,
      render: (_, record) => (
        <div>
          <div>{record.iphone}</div>
          {record.iphone && (
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.iphone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "店铺时间",
      dataIndex: "businessHours",
      key: "businessHours",
      width: 150,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        const statusOption = statusOptions.find((opt) => opt.value === status);
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingUser(record);
                const carouselImages = record.carouselImages.map(
                  (item: any, index) => ({
                    ...item,
                    uid: index,
                    status: "done",
                    url: item.imageUrl,
                  })
                );
                setCarouselImages(carouselImages);
                form.setFieldsValue({
                  name: record.name,
                  iphone: record.iphone,
                  status: record.status,
                  address: record.address,
                  businessHours: record.businessHours,
                  description: record.description,
                  thumbnailImg: record.thumbnailImg,
                  businessId: record.businessId,
                  carouselImages,
                });
                setImageUrl(record.thumbnailImg);
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个店铺吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                router.push(`/main/shop/detail?id=${record.id}`);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>店铺管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统店铺，包括店铺信息、角色权限等{shop.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchshop(pagination.current, pagination.pageSize)}
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingUser(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增店铺
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索店铺名"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 店铺表格 */}
        <Table
          columns={columns}
          dataSource={shop}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑店铺弹窗 */}
      <Modal
        title={editingUser ? "编辑店铺" : "新增店铺"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "ACTIVE",
          }}
        >
          {!editingUser && (
            <Form.Item
              name="name"
              label="店铺名"
              rules={[
                { required: true, message: "请输入店铺名" },
                { min: 2, message: "店铺名至少2个字符" },
              ]}
            >
              <Input placeholder="请输入店铺名" />
            </Form.Item>
          )}

          <Form.Item
            name="businessId"
            label="商家名称"
            rules={[{ required: true, message: "请选择关联的商家" }]}
          >
            <Select
              placeholder="请选择关联的商家"
              showSearch
              filterOption={false}
              onSearch={handleSearchResults}
              onChange={handleSearchResults}
              notFoundContent={null}
              options={searchResults}
            />
          </Form.Item>
          <Form.Item name="thumbnailImg" label="店铺图片">
            <ImgCrop rotationSlider>
              <Upload
                name="file"
                listType="picture-card"
                className="avatar-uploader"
                action="/api/upload"
                onChange={handleAvatarUpload}
                beforeUpload={beforeUpload}
                showUploadList={false}
              >
                {imageUrl ? (
                  <img src={imageUrl} alt="avatar" style={{ width: "100%" }} />
                ) : (
                  uploadButton
                )}
              </Upload>
            </ImgCrop>
          </Form.Item>
          <Form.Item
            name="carouselImages"
            label="店铺轮播图"
            rules={[{ required: true, message: "请上传店铺轮播图" }]}
          >
            <ImgCrop rotationSlider aspect={9 / 10}>
              <Upload
                name="file"
                listType="picture-card"
                className="avatar-uploader"
                action="/api/upload"
                onChange={handleCarouselUpload}
                beforeUpload={beforeUpload}
                fileList={carouselImages}
                maxCount={6}
              >
                {carouselImages.length < 7 && uploadButton}
              </Upload>
            </ImgCrop>
          </Form.Item>

          <Form.Item
            name="address"
            label="地址"
            rules={[{ required: true, message: "请输入地址" }]}
          >
            <Input placeholder="请输入地址" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input placeholder="请输入描述" />
          </Form.Item>

          <Form.Item
            name="iphone"
            label="手机号"
            rules={[
              { required: true, message: "请输入邮箱" },
              { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Form.Item
            name="businessHours"
            label="店铺时间"
            rules={[{ required: true, message: "输入店铺时间" }]}
          >
            <Input placeholder="输入店铺时间" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingUser(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingUser ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
