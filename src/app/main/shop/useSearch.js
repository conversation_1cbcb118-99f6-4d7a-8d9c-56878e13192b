import { useEffect, useState } from "react";

export const useSearch = (api, valueKey = "name") => {
  const [searchBusiness, setSearchBusiness] = useState("");
  const [searchResults, setSearchResults] = useState([]);

  const handleSearchBusiness = (value) => {
    setSearchBusiness(value);
  };
  const handleSearchResults = (results) => {
    fetch(results);
  };

  //axios
  const fetch = async (value) => {
    try {
      const response = await api({ name: value, pageSize: 100 });
      console.log("response", response);
      setSearchResults(
        response.list.map((item) => ({ value: item.id, label: item[valueKey] }))
      );
    } catch (error) {
      console.error("搜索错误:", error);
    }
  };
  useEffect(() => {
    fetch(searchBusiness);
  }, []);

  return {
    searchBusiness,
    searchResults,
    handleSearchBusiness,
    handleSearchResults,
  };
};
