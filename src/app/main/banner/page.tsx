"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
  InputNumber,
  Switch,
  Upload,
} from "antd";
import ImgCrop from "antd-img-crop";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import type { UploadFile } from "antd";

import { defHttp } from "@/lib/axios";

const { Search } = Input;
const { Option } = Select;

// banner数据接口
interface Banner {
  id: number;
  title: string;
  imageUrl: string;
  linkType: string;
  linkValue: string;
  platform: string;
  sort: number;
  status: boolean;
  startTime: string;
  endTime: string;
  createdAt: string;
  updatedAt: string;
  position: number;
}

// 分页信息接口
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// banner表单数据接口
interface BannerFormData {
  title: string;
  imageUrl: string;
  linkType: string;
  linkValue: string;
  platform: string;
  sort: number;
  status: boolean;
  startTime: string;
  endTime: string;
  position: number;
}

const BannersPage: React.FC = () => {
  const [bannnersImg, setBannnersImg] = useState<UploadFile[]>([]);
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBanner, setEditingBanner] = useState<Banner | null>(null);
  const [form] = Form.useForm();
  const [searchKeyword, setSearchKeyword] = useState("");
  const [positionFilter, setPositionFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [linkTypeFilter, setLinkTypeFilter] = useState("");
  const [platformFilter, setPlatformFilter] = useState("");

  // 获取banner列表
  const fetchBanners = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(positionFilter && { position: positionFilter }),
        ...(statusFilter && { status: statusFilter }),
        ...(linkTypeFilter && { linkType: linkTypeFilter }),
        ...(platformFilter && { platform: platformFilter }),
      });

      const response = await defHttp.get({ url: `/banner`, params });

      setBanners(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取banner列表错误:", error);
      message.error("获取banner列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBanners();
  }, [searchKeyword, statusFilter, linkTypeFilter, platformFilter, positionFilter]);

  // 搜索banner
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 位置筛选
  const handlePositionFilter = (value: string) => {
    setPositionFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 状态筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 链接类型筛选
  const handleLinkTypeFilter = (value: string) => {
    setLinkTypeFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 平台筛选
  const handlePlatformFilter = (value: string) => {
    setPlatformFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 表格变化时的回调函数
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchBanners(current, pageSize);
  };

  // 新增/编辑banner
  const handleSave = async (values: BannerFormData) => {
    try {
      setLoading(true);
      if (bannnersImg.length > 0 && bannnersImg[0].url) {
        values.imageUrl = bannnersImg?.[0]?.url;
      }

      if (editingBanner) {
        // 编辑
        await defHttp.put({
          url: `/banner/${editingBanner.id}`,
          data: values,
        });

        message.success("更新banner成功");
        setModalVisible(false);
        setEditingBanner(null);
        form.resetFields();
        fetchBanners(pagination.current, pagination.pageSize);
      } else {
        // 新增
        await defHttp.post({ url: "/banner", data: values });

        message.success("创建banner成功");
        setModalVisible(false);
        setEditingBanner(null);
        form.resetFields();
        fetchBanners(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除banner
  const handleDelete = async (bannerId: number) => {
    try {
      setLoading(true);
      await defHttp.delete({ url: `/banner/${bannerId}` });

      message.success("删除banner成功");
      fetchBanners(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error("删除banner错误:", error);
      message.error("删除banner失败");
    } finally {
      setLoading(false);
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("只能上传 JPG/PNG 文件!");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("图片大小不能超过 2MB!");
      return false;
    }
    return true;
  };

  const handleCarouselUpload = ({ file }: any) => {
    if (!file) return;

    let updatedImages: UploadFile[] = [...bannnersImg];

    // ✅ 处理图片删除
    if (file.status === "removed") {
      updatedImages = updatedImages.filter((item) => item.uid !== file.uid);
    } else {
      // ✅ 查找图片索引
      const index = updatedImages.findIndex((item) => item.uid === file.uid);

      const imgObj = {
        name: file.name,
        uid: file.uid,
        status: file.status,
        url: file.status === "done" ? file.response?.data?.url : undefined,
      };

      if (index !== -1) {
        // ✅ 更新已存在的图片
        updatedImages[index] = imgObj;
      } else {
        // ✅ 添加新图片
        updatedImages.push(imgObj);

        if (file.status === "done") {
          message.success("图片上传成功");
        } else if (file.status === "error") {
          message.error("图片上传失败");
        }
      }
    }

    // ✅ 统一更新状态 & 表单
    setBannnersImg(updatedImages);
    form.setFieldsValue({ carouselImages: updatedImages });
  };

  const LinkTypeOptions = [
    { value: "none", label: "无" },
    { value: "url", label: "URL" },
    { value: "product", label: "商品" },
    { value: "category", label: "分类" },
  ];
  const PlatformOptions = [
    { value: "all", label: "全部" },
    { value: "web", label: "Web" },
    { value: "app", label: "App" },
  ];
  const StatusOptions = [
    { value: "true", label: "启用" },
    { value: "false", label: "禁用" },
  ];
  const PositionOptions = [
    { value: "home", label: "首页banner" },
    { value: "category", label: "分类" },
  ];

  // 表格列定义
  const columns: ColumnsType<Banner> = [
    {
      title: "banner信息",
      key: "bannerInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <div>
            <div style={{ fontWeight: "bold" }}>{record.title}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.linkValue}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "图片",
      dataIndex: "imageUrl",
      key: "imageUrl",
      width: 150,
      render: (imageUrl: string) => (
        <img src={imageUrl} alt="banner" style={{ maxWidth: "100px" }} />
      ),
    },
    {
      title: "位置",
      dataIndex: "position",
      key: "position",
      width: 100,
      render: (position: string) => (
        <Tag color="blue">
          {PositionOptions.find((opt) => opt.value === position)?.label}
        </Tag>
      ),
    },
    {
      title: "链接类型",
      dataIndex: "linkType",
      key: "linkType",
      width: 100,
      render: (linkType: string) => (
        <Tag color="blue">
          {LinkTypeOptions.find((opt) => opt.value === linkType)?.label}
        </Tag>
      ),
    },
    {
      title: "平台",
      dataIndex: "platform",
      key: "platform",
      width: 100,
      render: (platform: string) => (
        <Tag color="blue">
          {PlatformOptions.find((opt) => opt.value === platform)?.label}
        </Tag>
      ),
    },
    {
      title: "排序",
      dataIndex: "sort",
      key: "sort",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: boolean) => (
        <Badge
          status={status ? "success" : "error"}
          text={status ? "启用" : "禁用"}
        />
      ),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingBanner(record);
                form.setFieldsValue({
                  title: record.title,
                  imageUrl: record.imageUrl,
                  linkType: record.linkType,
                  linkValue: record.linkValue,
                  platform: record.platform,
                  sort: record.sort,
                  status: record.status,
                  startTime: record.startTime,
                  endTime: record.endTime,
                  position: record.position,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个banner吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <div style={{ padding: "0" }}>
      <Card>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>banner管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统banner，包括banner信息、角色权限等{banners.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchBanners(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingBanner(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增banner
            </Button>
          </Space>
        </div>
        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索banner标题"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选位置"
            allowClear
            style={{ width: 120 }}
            onChange={handlePositionFilter}
          >
            {PositionOptions.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {StatusOptions.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选链接类型"
            allowClear
            style={{ width: 120 }}
            onChange={handleLinkTypeFilter}
          >
            {LinkTypeOptions.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选平台"
            allowClear
            style={{ width: 120 }}
            onChange={handlePlatformFilter}
          >
            {PlatformOptions.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        </div>
        <Table
          columns={columns}
          dataSource={banners}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑banner弹窗 */}
      <Modal
        title={editingBanner ? "编辑banner" : "新增banner"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingBanner(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: true,
          }}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: "请输入标题" }]}
          >
            <Input placeholder="请输入标题" />
          </Form.Item>

          <Form.Item
            name="carouselImages"
            label="图片"
            rules={[{ required: true, message: "请上传图片" }]}
          >
            <ImgCrop rotationSlider aspect={19.5 / 9}>
              <Upload
                name="file"
                listType="picture-card"
                className="avatar-uploader"
                action="/api/upload"
                onChange={handleCarouselUpload}
                beforeUpload={beforeUpload}
                fileList={bannnersImg}
                maxCount={1}
              >
                {bannnersImg.length < 7 && uploadButton}
              </Upload>
            </ImgCrop>
          </Form.Item>
          <Form.Item
            name="position"
            label="位置"
            rules={[{ required: true, message: "请选择位置" }]}
          >
            <Select placeholder="请选择位置" options={PositionOptions} />
          </Form.Item>

          <Form.Item
            name="linkType"
            label="链接类型"
            rules={[{ required: true, message: "请选择链接类型" }]}
          >
            <Select placeholder="请选择链接类型" options={LinkTypeOptions} />
          </Form.Item>

          <Form.Item
            name="linkValue"
            label="链接值"
            rules={[{ required: true, message: "请输入链接值" }]}
          >
            <Input placeholder="请输入链接值" />
          </Form.Item>

          <Form.Item
            name="platform"
            label="平台"
            rules={[{ required: true, message: "请选择平台" }]}
          >
            <Select placeholder="请选择平台" options={PlatformOptions} />
          </Form.Item>

          <Form.Item
            name="sort"
            label="排序"
            rules={[{ required: true, message: "请输入排序" }]}
          >
            <InputNumber placeholder="请输入排序" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingBanner(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingBanner ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BannersPage;
