"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { useUserStore } from "@/store/useUserStore";
// import { http } from "@/lib/request";
import { defHttp } from "@/lib/axios";
import { isString } from "@/utils/is";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 用户数据接口
interface User {
  id: number;
  username: string;
  nickname: string;
  email: string;
  phone?: string;
  avatar?: string;
  status: number;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginTime?: string;
  roles: string;
}

// 分页信息类型
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 用户表单数据类型
interface UserFormData {
  username?: string;
  nickname: string;
  email: string;
  phone?: string;
  permissions: string | string[];
  status: string;
  roles: string;
}

export default function UsersPage() {
  const { userInfo } = useUserStore();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  // 分页状态
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [roleFilter, setRoleFilter] = useState<string>("");

  // 角色选项
  const roleOptions = [
    { label: "超级管理员", value: "super_admin", color: "red" },
    { label: "管理员", value: "admin", color: "orange" },
    { label: "经理", value: "manager", color: "blue" },
    { label: "编辑员", value: "editor", color: "green" },
    { label: "普通用户", value: "user", color: "default" },
    { label: "访客", value: "guest", color: "gray" },
  ];

  // 状态选项
  const statusOptions = [
    { label: "正常", value: "1", color: "success" },
    { label: "禁用", value: "0", color: "warning" },
    { label: "封禁", value: "-1", color: "error" },
  ];

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
        ...(roleFilter && { role: roleFilter }),
      });

      const response = await defHttp.get({ url: `/users?${params}` });

      setUsers(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取用户列表错误:", error);
      message.error("获取用户列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchUsers();
  }, [searchKeyword, statusFilter, roleFilter]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleRoleFilter = (value: string) => {
    setRoleFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 处理分页
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchUsers(current, pageSize);
  };

  // 新增/编辑用户
  const handleSave = async (values: UserFormData) => {
    try {
      setLoading(true);
      if (editingUser) {
        // 编辑
        const response = await defHttp.put({
          url: `/users/${editingUser.id}`,
          data: values,
        });
        if (response.success) {
          message.success("更新用户成功");
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
          fetchUsers(pagination.current, pagination.pageSize);
        } else {
          message.error(response.data.message || "更新用户失败");
        }
      } else {
        // 新增
        const response = await defHttp.post({ url: "/users", data: values });
        if (response.success) {
          message.success("创建用户成功");
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
          fetchUsers(pagination.current, pagination.pageSize);
        } else {
          message.error(response.message || "创建用户失败");
        }
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除用户
  const handleDelete = async (userId: number) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/users/${userId}` });

      if (response.success) {
        message.success("删除用户成功");
        fetchUsers(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || "删除用户失败");
      }
    } catch (error) {
      console.error("删除用户错误:", error);
      message.error("删除用户失败");
    } finally {
      setLoading(false);
    }
  };

  // 表格列配置
  const columns: ColumnsType<User> = [
    {
      title: "用户信息",
      key: "userInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar size={40} icon={<UserOutlined />} src={record.avatar} />
          <div>
            <div style={{ fontWeight: "bold" }}>{record.nickname}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.username}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "联系方式",
      key: "contact",
      width: 180,
      render: (_, record) => (
        <div>
          <div>{record.email}</div>
          {record.phone && (
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.phone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "角色",
      dataIndex: "roles",
      key: "roles",
      width: 150,
      render: (roles: string[]) => <Space wrap>{roles}</Space>,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: number) => {
        const statusOption = statusOptions.find(
          (opt) => opt.value === status.toString()
        );
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "最后登录",
      dataIndex: "lastLoginTime",
      key: "lastLoginTime",
      width: 150,
      render: (time: string) =>
        time ? new Date(time).toLocaleString() : "从未登录",
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingUser(record);
                form.setFieldsValue({
                  nickname: record.nickname,
                  email: record.email,
                  phone: record.phone,
                  roles: record.roles,
                  status: record.status.toString(),
                  // permissions: record.permissions.join(","),
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id !== 1 && record.id !== userInfo?.id && (
            <Popconfirm
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  console.log("users", users);

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>用户管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统用户，包括用户信息、角色权限等{users.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchUsers(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingUser(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增用户
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            marginBottom: "16px",
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索用户名、昵称或邮箱"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            {statusOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选角色"
            allowClear
            style={{ width: 120 }}
            onChange={handleRoleFilter}
          >
            {roleOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 用户表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑用户弹窗 */}
      <Modal
        title={editingUser ? "编辑用户" : "新增用户"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "1",
            permissions: "user",
          }}
        >
          {!editingUser && (
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: "请输入用户名" },
                { min: 3, message: "用户名至少3个字符" },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: "用户名只能包含字母、数字和下划线",
                },
              ]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>
          )}

          <Form.Item
            name="nickname"
            label="昵称"
            rules={[{ required: true, message: "请输入昵称" }]}
          >
            <Input placeholder="请输入昵称" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: "请输入邮箱" },
              { type: "email", message: "请输入有效的邮箱地址" },
            ]}
          >
            <Input placeholder="请输入邮箱地址" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Form.Item
            name="roles"
            label="角色"
            rules={[{ required: true, message: "请选择角色" }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择角色"
              options={roleOptions}
            />
          </Form.Item>
          {/* <Form.Item
            name="permissions"
            label="权限"
            rules={[{ required: true, message: "请选择角色" }]}
          >
            <Input placeholder="请输入权限" />
          </Form.Item> */}

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingUser(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingUser ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
