/* 全局样式 */
@import "tailwindcss";

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 主题相关的CSS变量 */
:root {
  --primary-color: #1677ff;
  --bg-container: #ffffff;
  --bg-layout: #f5f5f5;
  --text-color: rgba(0, 0, 0, 0.88);
  --text-color-secondary: rgba(0, 0, 0, 0.65);
  --border-color: #d9d9d9;
  --border-radius: 6px;
}

/* 浅色主题 */
.theme-actual-light {
  --bg-container: #ffffff;
  --bg-layout: #f5f5f5;
  --text-color: rgba(0, 0, 0, 0.88);
  --text-color-secondary: rgba(0, 0, 0, 0.65);
  --border-color: #d9d9d9;
  --scrollbar-track: #f1f1f1;
  --scrollbar-thumb: #c1c1c1;
  --scrollbar-thumb-hover: #a8a8a8;
}

/* 深色主题 */
.theme-actual-dark {
  --bg-container: #141414;
  --bg-layout: #000000;
  --text-color: rgba(255, 255, 255, 0.85);
  --text-color-secondary: rgba(255, 255, 255, 0.65);
  --border-color: #424242;
  --scrollbar-track: #1f1f1f;
  --scrollbar-thumb: #424242;
  --scrollbar-thumb-hover: #595959;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track, #f1f1f1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb, #c1c1c1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover, #a8a8a8);
}

/* 链接样式 */
a {
  color: inherit;
  text-decoration: none;
}

/* 禁用文本选择的元素 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 表格响应式 */
.ant-table-wrapper {
  overflow-x: auto;
}

/* 主题切换动画 */
.ant-layout,
.ant-card,
.ant-menu,
.ant-table,
.ant-input,
.ant-btn {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
  }

  .ant-layout-content {
    margin-left: 0 !important;
  }

  .ant-table-wrapper {
    font-size: 12px;
  }

  .ant-btn {
    font-size: 12px;
    height: 28px;
    padding: 0 8px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .ant-layout-sider {
    display: none !important;
  }

  .ant-layout-header {
    display: none !important;
  }

  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* 默认主题 */
.ant-tooltip-inner {
  background-color: #fff !important;
  color: #000 !important;
}

/* 暗色主题 */
.dark .ant-tooltip-inner {
  background-color: #333 !important;
  color: #fff !important;
}