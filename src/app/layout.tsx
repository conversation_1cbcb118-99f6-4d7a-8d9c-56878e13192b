import type { Metadata } from "next";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import MessageProvider from "@/components/providers/MessageProvider";
import ThemeProvider from "@/components/theme/ThemeProvider";

import "./globals.css";

export const metadata: Metadata = {
  title: "CCXC - 企业级管理后台系统",
  description: "基于 Next.js + Ant Design 的现代化企业级管理后台系统",
  keywords: [
    "管理后台",
    "企业级",
    "Next.js",
    "Ant Design",
    "React",
    "TypeScript",
  ],
  authors: [{ name: "WebSoft Team" }],
  creator: "WebSoft Team",
  publisher: "WebSoft",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "CCXC",
  },
  applicationName: "CCXC",
  referrer: "origin-when-cross-origin",
  category: "business",
  classification: "business",
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "apple-mobile-web-app-title": "CCXC",
    "application-name": "CCXC",
    "msapplication-TileColor": "#1890ff",
    "msapplication-config": "/browserconfig.xml",
    "theme-color": "#1890ff",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh">
      <body>
        <AntdRegistry >
          <ThemeProvider>
            <MessageProvider>{children}</MessageProvider>
          </ThemeProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}
