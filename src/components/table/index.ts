// 导出数据表格组件和相关工具
export { default as DataTable } from './DataTable';
export type { DataTableProps } from './DataTable';
export { commonRowActions, commonFilters } from './DataTable';

// 导出表格相关的工具函数
export const tableUtils = {
  // 格式化时间显示
  formatTime: (time: string | undefined) => {
    if (!time) return '从未';
    return new Date(time).toLocaleString();
  },

  // 格式化状态显示
  formatStatus: (status: string, statusOptions: Array<{ label: string; value: string; color: string }>) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option || { label: status, value: status, color: 'default' };
  },

  // 格式化角色显示
  formatRoles: (roles: string[], roleOptions: Array<{ label: string; value: string; color: string }>) => {
    return roles.map(role => {
      const option = roleOptions.find(opt => opt.value === role);
      return option || { label: role, value: role, color: 'default' };
    });
  },

  // 生成分页配置
  createPagination: (current: number, pageSize: number, total: number) => ({
    current,
    pageSize,
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  }),

  // 生成搜索参数
  createSearchParams: (params: Record<string, any>) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  },
};

// 导出常用的列配置
export const commonColumns = {
  // 序号列
  index: (title = '序号') => ({
    title,
    key: 'index',
    width: 60,
    render: (_: any, __: any, index: number) => index + 1,
  }),

  // 时间列
  time: (title: string, dataIndex: string) => ({
    title,
    dataIndex,
    key: dataIndex,
    width: 150,
    render: (time: string) => tableUtils.formatTime(time),
  }),

  // 状态列
  status: (title = '状态', dataIndex = 'status', statusOptions: Array<{ label: string; value: string; color: string }>) => ({
    title,
    dataIndex,
    key: dataIndex,
    width: 100,
    render: (status: string) => {
      const option = tableUtils.formatStatus(status, statusOptions);
      return option.label;
    },
  }),
};
