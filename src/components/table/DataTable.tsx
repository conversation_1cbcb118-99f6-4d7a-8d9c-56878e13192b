'use client';

import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Tooltip,
  message,
  Popconfirm,
  Tag,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import { useResponsive, useResponsiveConfig } from '@/hooks/useResponsive';

const { Search } = Input;
const { Option } = Select;

// 通用数据表格的属性接口
export interface DataTableProps<T = any> {
  // 基础配置
  title?: string;
  description?: string;

  // 数据相关
  dataSource?: T[];
  columns: ColumnsType<T>;
  rowKey?: string | ((record: T) => string);
  loading?: boolean;

  // 分页配置
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: (total: number, range: [number, number]) => string;
  };

  // 搜索和筛选
  searchable?: boolean;
  searchPlaceholder?: string;
  filters?: Array<{
    key: string;
    label: string;
    options: Array<{ label: string; value: string | number }>;
  }>;

  // 操作按钮
  actions?: {
    create?: {
      text?: string;
      icon?: React.ReactNode;
      onClick: () => void;
    };
    refresh?: {
      text?: string;
      icon?: React.ReactNode;
      onClick: () => void;
    };
    export?: {
      text?: string;
      icon?: React.ReactNode;
      onClick: () => void;
    };
    custom?: Array<{
      key: string;
      text: string;
      icon?: React.ReactNode;
      type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
      onClick: () => void;
    }>;
  };

  // 行操作
  rowActions?: Array<{
    key: string;
    text?: string;
    icon?: React.ReactNode;
    type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
    danger?: boolean;
    tooltip?: string;
    confirm?: {
      title: string;
      okText?: string;
      cancelText?: string;
    };
    visible?: (record: T) => boolean;
    disabled?: (record: T) => boolean;
    onClick: (record: T) => void;
  }>;

  // 事件回调
  onTableChange?: (pagination: TablePaginationConfig, filters: any, sorter: any) => void;
  onSearch?: (value: string) => void;
  onFilter?: (key: string, value: string | number | undefined) => void;

  // 样式配置
  size?: 'small' | 'middle' | 'large';
  bordered?: boolean;
  scroll?: { x?: number; y?: number };
  className?: string;
  style?: React.CSSProperties;
}

// 通用数据表格组件
export default function DataTable<T = any>({
  title,
  description,
  dataSource = [],
  columns,
  rowKey = 'id',
  loading = false,
  pagination,
  searchable = true,
  searchPlaceholder = '搜索...',
  filters = [],
  actions,
  rowActions = [],
  onTableChange,
  onSearch,
  onFilter,
  size = 'middle',
  bordered = false,
  scroll,
  className,
  style,
}: DataTableProps<T>) {
  const { isMobile, isTablet, isSmallScreen } = useResponsive();
  const { table: tableConfig, button: buttonConfig } = useResponsiveConfig();

  const [searchValue, setSearchValue] = useState('');
  const [filterValues, setFilterValues] = useState<Record<string, string | number | undefined>>({});

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  };

  // 处理筛选
  const handleFilter = (key: string, value: string | number | undefined) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
    onFilter?.(key, value);
  };

  // 构建表格列（包含行操作）
  const tableColumns: ColumnsType<T> = [
    ...columns,
    ...(rowActions.length > 0 ? [{
      title: '操作',
      key: 'actions',
      width: Math.max(120, rowActions.length * 40),
      fixed: 'right' as const,
      render: (_: any, record: T) => (
        <Space size="small">
          {rowActions.map(action => {
            // 检查是否显示该操作
            if (action.visible && !action.visible(record)) {
              return null;
            }

            const button = (
              <Tooltip title={action.tooltip} key={action.key}>
                <Button
                  type={action.type || 'text'}
                  size="small"
                  danger={action.danger}
                  icon={action.icon}
                  disabled={action.disabled?.(record)}
                  onClick={() => action.onClick(record)}
                >
                  {action.text}
                </Button>
              </Tooltip>
            );

            // 如果需要确认对话框
            if (action.confirm) {
              return (
                <Popconfirm
                  key={action.key}
                  title={action.confirm.title}
                  okText={action.confirm.okText || '确定'}
                  cancelText={action.confirm.cancelText || '取消'}
                  onConfirm={() => action.onClick(record)}
                >
                  {button}
                </Popconfirm>
              );
            }

            return button;
          })}
        </Space>
      ),
    }] : []),
  ];

  return (
    <Card className={className} style={style}>
      {/* 标题和描述 */}
      {(title || description) && (
        <div style={{ marginBottom: '16px' }}>
          {title && <h2 style={{ margin: 0 }}>{title}</h2>}
          {description && (
            <p style={{ margin: '4px 0 0 0', color: '#666' }}>
              {description}
            </p>
          )}
        </div>
      )}

      {/* 操作栏 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: isSmallScreen ? 'stretch' : 'center',
        marginBottom: '16px',
        flexDirection: isSmallScreen ? 'column' : 'row',
        gap: isSmallScreen ? '8px' : '16px',
      }}>
        {/* 搜索和筛选 */}
        <div style={{
          display: 'flex',
          gap: isMobile ? '8px' : '16px',
          flexWrap: 'wrap',
          width: isSmallScreen ? '100%' : 'auto',
        }}>
          {searchable && (
            <Search
              placeholder={searchPlaceholder}
              allowClear
              size={isMobile ? 'small' : 'middle'}
              style={{
                width: isSmallScreen ? '100%' : 300,
                minWidth: isSmallScreen ? 'auto' : 200,
              }}
              onSearch={handleSearch}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
            />
          )}

          {filters.map(filter => (
            <Select
              key={filter.key}
              placeholder={filter.label}
              allowClear
              style={{ width: 120 }}
              value={filterValues[filter.key]}
              onChange={(value) => handleFilter(filter.key, value)}
            >
              {filter.options.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          ))}
        </div>

        {/* 操作按钮 */}
        <Space>
          {actions?.refresh && (
            <Button
              icon={actions.refresh.icon || <ReloadOutlined />}
              onClick={actions.refresh.onClick}
            >
              {actions.refresh.text || '刷新'}
            </Button>
          )}

          {actions?.export && (
            <Button
              icon={actions.export.icon || <ExportOutlined />}
              onClick={actions.export.onClick}
            >
              {actions.export.text || '导出'}
            </Button>
          )}

          {actions?.custom?.map(action => (
            <Button
              key={action.key}
              type={action.type}
              icon={action.icon}
              onClick={action.onClick}
            >
              {action.text}
            </Button>
          ))}

          {actions?.create && (
            <Button
              type="primary"
              icon={actions.create.icon || <PlusOutlined />}
              onClick={actions.create.onClick}
            >
              {actions.create.text || '新增'}
            </Button>
          )}
        </Space>
      </div>

      {/* 数据表格 */}
      <Table<T>
        columns={tableColumns}
        dataSource={dataSource}
        rowKey={rowKey}
        loading={loading}
        size={tableConfig.size as any}
        bordered={bordered}
        scroll={{
          x: tableConfig.scroll.x || scroll?.x,
          y: scroll?.y,
        }}
        pagination={pagination ? {
          current: pagination.current,
          pageSize: pagination.pageSize || tableConfig.pagination.pageSize,
          total: pagination.total,
          size: tableConfig.pagination.size as any,
          showSizeChanger: tableConfig.pagination.showSizeChanger && (pagination.showSizeChanger !== false),
          showQuickJumper: tableConfig.pagination.showQuickJumper && (pagination.showQuickJumper !== false),
          showTotal: tableConfig.pagination.showTotal ?
            (pagination.showTotal || ((total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            )) : undefined,
          simple: isMobile,
        } : false}
        onChange={onTableChange}
      />
    </Card>
  );
}

// 导出常用的行操作配置
export const commonRowActions = {
  view: (onClick: (record: any) => void) => ({
    key: 'view',
    icon: <EyeOutlined />,
    tooltip: '查看',
    onClick,
  }),

  edit: (onClick: (record: any) => void) => ({
    key: 'edit',
    icon: <EditOutlined />,
    tooltip: '编辑',
    onClick,
  }),

  delete: (onClick: (record: any) => void, options?: {
    visible?: (record: any) => boolean;
    disabled?: (record: any) => boolean;
  }) => ({
    key: 'delete',
    icon: <DeleteOutlined />,
    tooltip: '删除',
    danger: true,
    confirm: {
      title: '确定要删除这条记录吗？',
      okText: '确定',
      cancelText: '取消',
    },
    visible: options?.visible,
    disabled: options?.disabled,
    onClick,
  }),
};

// 导出常用的筛选选项
export const commonFilters = {
  status: (options: Array<{ label: string; value: string }>) => ({
    key: 'status',
    label: '状态',
    options,
  }),

  type: (options: Array<{ label: string; value: string }>) => ({
    key: 'type',
    label: '类型',
    options,
  }),

  category: (options: Array<{ label: string; value: string }>) => ({
    key: 'category',
    label: '分类',
    options,
  }),
};
