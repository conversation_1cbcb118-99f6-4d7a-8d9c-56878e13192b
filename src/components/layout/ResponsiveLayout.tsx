"use client";

import React, { useState, useEffect } from "react";
import { Layout, Drawer, But<PERSON>, Affix } from "antd";
import { MenuOutlined, CloseOutlined } from "@ant-design/icons";
import { useResponsive, useResponsiveConfig } from "@/hooks/useResponsive";
import { usePathname } from "next/navigation";
import AuthGuard from "@/components/auth/AuthGuard";
import { routerConfig } from "@/config/router";

const { Header, Sider, Content } = Layout;

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  siderContent?: React.ReactNode;
  headerContent?: React.ReactNode;
  headerExtra?: React.ReactNode;
  collapsed?: boolean;
  onCollapse?: (collapsed: boolean) => void;
  className?: string;
  style?: React.CSSProperties;
}

export default function ResponsiveLayout({
  children,
  siderContent,
  headerContent,
  headerExtra,
  collapsed = false,
  onCollapse,
  className,
  style,
}: ResponsiveLayoutProps) {
  const { isMobile, isTablet, isSmallScreen } = useResponsive();
  const { layout } = useResponsiveConfig();
  const pathname = usePathname();

  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [internalCollapsed, setInternalCollapsed] = useState(collapsed);
  const [requiredPermissions, setRequiredPermissions] = useState<string[]>([]);

  // 响应式处理侧边栏折叠状态
  useEffect(() => {
    if (isSmallScreen) {
      setInternalCollapsed(true);
    } else {
      setInternalCollapsed(collapsed);
    }
  }, [isSmallScreen, collapsed]);

  useEffect(() => {
    const currentRoute = routerConfig
      .flat(Infinity)
      .find((item) => item.key === pathname);
    setRequiredPermissions(currentRoute?.permission || []);
  }, [pathname]);

  // 处理侧边栏折叠
  const handleCollapse = (newCollapsed: boolean) => {
    setInternalCollapsed(newCollapsed);
    onCollapse?.(newCollapsed);
  };

  // 移动端菜单切换
  const toggleMobileMenu = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  // 关闭移动端菜单
  const closeMobileMenu = () => {
    setMobileMenuVisible(false);
  };

  // 移动端布局
  if (isMobile) {
    return (
      <Layout className={className} style={style}>
        {/* 移动端头部 */}
        <Header
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
            height: layout.headerHeight,
            lineHeight: `${layout.headerHeight}px`,
            padding: "0 16px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
            {siderContent && (
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={toggleMobileMenu}
                style={{ padding: "4px" }}
              />
            )}
            {headerContent}
          </div>
          {headerExtra}
        </Header>

        {/* 移动端内容区域 */}
        <Content
          style={{
            marginTop: layout.headerHeight,
            padding: layout.contentPadding,
            minHeight: `calc(100vh - ${layout.headerHeight}px)`,
          }}
        >
          <AuthGuard requiredPermissions={requiredPermissions}>
            {children}
          </AuthGuard>
        </Content>

        {/* 移动端侧边栏抽屉 */}
        {siderContent && (
          <Drawer
            title={
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <span>菜单</span>
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={closeMobileMenu}
                  size="small"
                />
              </div>
            }
            placement="left"
            open={mobileMenuVisible}
            onClose={closeMobileMenu}
            width="80%"
            bodyStyle={{ padding: 0 }}
            headerStyle={{ padding: "16px 24px" }}
            closable={false}
          >
            {siderContent}
          </Drawer>
        )}
      </Layout>
    );
  }

  // 平板和桌面布局
  return (
    <Layout className={className} style={style}>
      {/* 侧边栏 */}
      {siderContent && (
        <Sider
          width={layout.siderWidth}
          collapsedWidth={layout.siderCollapsedWidth}
          collapsed={internalCollapsed}
          onCollapse={handleCollapse}
          breakpoint="lg"
          collapsible={!isTablet}
          trigger={isTablet ? null : undefined}
          style={{
            position: "fixed",
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100,
            boxShadow: "2px 0 8px rgba(0, 0, 0, 0.1)",
          }}
        >
          {siderContent}
        </Sider>
      )}

      <Layout
        style={{
          marginLeft: siderContent
            ? internalCollapsed
              ? layout.siderCollapsedWidth
              : layout.siderWidth
            : 0,
          transition: "margin-left 0.2s",
        }}
      >
        {/* 头部 */}
        <Header
          style={{
            position: "fixed",
            top: 0,
            right: 0,
            left: siderContent
              ? internalCollapsed
                ? layout.siderCollapsedWidth
                : layout.siderWidth
              : 0,
            zIndex: 99,
            height: layout.headerHeight,
            lineHeight: `${layout.headerHeight}px`,
            padding: "0 24px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
            transition: "left 0.2s",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
            {isTablet && siderContent && (
              <Button
                type="text"
                icon={internalCollapsed ? <MenuOutlined /> : <CloseOutlined />}
                onClick={() => handleCollapse(!internalCollapsed)}
              />
            )}
            {headerContent}
          </div>
          {headerExtra}
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            marginTop: layout.headerHeight,
            padding: layout.contentPadding,
            minHeight: `calc(100vh - ${layout.headerHeight}px)`,
          }}
        >
          <AuthGuard requiredPermissions={requiredPermissions}>
            {children}
          </AuthGuard>
        </Content>
      </Layout>
    </Layout>
  );
}

// 响应式容器组件
interface ResponsiveContainerProps {
  children: React.ReactNode;
  maxWidth?: number | string;
  padding?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export function ResponsiveContainer({
  children,
  maxWidth = 1200,
  padding = true,
  className,
  style,
}: ResponsiveContainerProps) {
  const { isMobile, isTablet } = useResponsive();

  const containerStyle: React.CSSProperties = {
    maxWidth: typeof maxWidth === "number" ? `${maxWidth}px` : maxWidth,
    margin: "0 auto",
    width: "100%",
    ...(padding && {
      padding: isMobile ? "12px" : isTablet ? "16px" : "24px",
    }),
    ...style,
  };

  return (
    <div className={className} style={containerStyle}>
      {children}
    </div>
  );
}

// 响应式栅格组件
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
  gutter?: number | [number, number];
  className?: string;
  style?: React.CSSProperties;
}

export function ResponsiveGrid({
  children,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5, xxl: 6 },
  gutter = [16, 16],
  className,
  style,
}: ResponsiveGridProps) {
  const { screenSize } = useResponsive();

  const currentColumns = columns[screenSize] || columns.xs || 1;

  return (
    <div
      className={className}
      style={{
        display: "grid",
        gridTemplateColumns: `repeat(${currentColumns}, 1fr)`,
        gap: Array.isArray(gutter)
          ? `${gutter[1]}px ${gutter[0]}px`
          : `${gutter}px`,
        ...style,
      }}
    >
      {children}
    </div>
  );
}
