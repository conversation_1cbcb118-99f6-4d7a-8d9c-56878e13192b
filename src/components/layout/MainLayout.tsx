"use client";

import React, { useEffect, useState } from "react";
import {
  Layout,
  Menu,
  Button,
  Avatar,
  Dropdown,
  Space,
  Typography,
  Breadcrumb,
} from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import { useRouter, usePathname } from "next/navigation";
import { useUserStore } from "@/store/useUserStore";
import { useAppStore } from "@/store/useAppStore";
import ThemeToggle from "@/components/theme/ThemeToggle";
import QuickThemeToggle from "@/components/theme/QuickThemeToggle";
import ResponsiveLayout from "@/components/layout/ResponsiveLayout";
import { useResponsive, useResponsiveConfig } from "@/hooks/useResponsive";
import ErrorBoundary from "@/components/error/ErrorBoundary";
import { useGlobalErrorNotification } from "@/hooks/useErrorHandler";
// import LanguageSwitcher from '@/components/i18n/LanguageSwitcher';
// import { useI18n } from '@/hooks/useI18n';
import type { MenuProps } from "antd";
import { routerConfig } from "@/config/router";

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { userInfo, logout } = useUserStore();
  const { sidebarCollapsed, toggleSidebar } = useAppStore();
  const { isMobile, isTablet, isSmallScreen } = useResponsive();
  const { layout, table, form, button } = useResponsiveConfig();

  // 启用全局错误通知
  useGlobalErrorNotification();

  // const { nav, common } = useI18n();
  const nav = (key: string) => key; // 临时替代
  const common = (key: string) => key; // 临时替代
  const [selectedKeys, setSelectedKeys] = useState([pathname]);
  const [openKeys, setOpenKeys] = useState<string[]>([pathname]);
  const [breadcrumbItems, setBreadcrumbItems] = useState<any[]>([
    { title: <HomeOutlined />, href: "/" },
  ]);

  // 用户下拉菜单
  const userMenuItems: MenuProps["items"] = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "个人资料",
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "个人设置",
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: common("logout") || "退出登录",
      onClick: () => logout(),
    },
  ];

  useEffect(() => {
    console.log("pathname", pathname);
    // /main/shop/station 有三个/

    if (pathname.split("/").length === 4) {
      // 获取 /main/shop

      const parentPath = pathname.split("/").slice(0, 3).join("/");
      setSelectedKeys([parentPath]);
      setOpenKeys(getParentKeys(parentPath, routerConfig));
      return;
    }
    setSelectedKeys([pathname]);
    setOpenKeys(getParentKeys(pathname, routerConfig));

    // Breadcrumb
    const breadcrumbItems = getBreadcrumbItems(pathname, routerConfig);
    console.log("breadcrumbItems", breadcrumbItems);
    setBreadcrumbItems([
      { title: <HomeOutlined />, href: "/" },
      ...breadcrumbItems,
    ]);
  }, [pathname]);

  const getParentKeys = (targetKey: string, menuItems: any[]): string[] => {
    const parentKeys: string[] = [];

    const findParent = (items: any[], target: string): boolean => {
      for (const item of items) {
        if (item.children) {
          // 检查子菜单中是否包含目标key
          const hasTarget = item.children.some(
            (child: any) => child.key === target
          );
          if (hasTarget) {
            parentKeys.push(item.key);
            return true;
          }
          // 递归查找更深层的子菜单
          if (findParent(item.children, target)) {
            parentKeys.push(item.key);
            return true;
          }
        }
      }
      return false;
    };

    findParent(menuItems, targetKey);
    return parentKeys.reverse(); // 反转以获得正确的层级顺序
  };

  const getBreadcrumbItems = (targetKey: string, menuItems: any[]): any[] => {
    const breadcrumbItems: any[] = [];

    const findBreadcrumb = (items: any[], target: string) => {
      for (const item of items) {
        if (item.key === target) {
          breadcrumbItems.unshift({
            ...item,
            title: item.label,
            href: item.key,
          });
          return true;
        }
        if (item.children) {
          if (findBreadcrumb(item.children, target)) {
            breadcrumbItems.unshift({
              ...item,
              title: item.label,
              href: item.key,
            });
            return true;
          }
        }
      }
      return false;
    };

    findBreadcrumb(menuItems, targetKey);
    return breadcrumbItems;
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    setSelectedKeys([key]);
    router.push(key);
  };

  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  // 侧边栏内容
  const siderContent = (
    <>
      {/* Logo */}
      <div
        style={{
          height: layout.headerHeight,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderBottom: "1px solid #f0f0f0",
          padding: isMobile ? "0 8px" : "0 16px",
        }}
      >
        {sidebarCollapsed && !isMobile ? (
          <div
            style={{
              fontSize: isMobile ? "20px" : "24px",
              fontWeight: "bold",
              color: "#1890ff",
            }}
          >
            W
          </div>
        ) : (
          <div
            style={{
              fontSize: isMobile ? "16px" : "18px",
              fontWeight: "bold",
              color: "#1890ff",
              textAlign: "center",
            }}
          >
            CCXC
          </div>
        )}
      </div>

      {/* 菜单 */}
      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        items={routerConfig}
        onClick={handleMenuClick}
        onOpenChange={handleOpenChange}
        style={{
          border: "none",
          fontSize: isMobile ? "14px" : "16px",
        }}
        inlineCollapsed={sidebarCollapsed && !isMobile}
      />
    </>
  );

  // 头部内容
  const headerContent = (
    <div style={{ display: "flex", alignItems: "center" }}>
      {!isMobile && (
        <Button
          type="text"
          icon={
            sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />
          }
          onClick={toggleSidebar}
          style={{
            fontSize: "16px",
            width: isTablet ? 32 : 40,
            height: isTablet ? 32 : 40,
          }}
        />
      )}

      {/* 面包屑 - 在小屏设备上隐藏 */}
      {!isSmallScreen && (
        <Breadcrumb
          style={{ marginLeft: "16px" }}
          items={breadcrumbItems}
        ></Breadcrumb>
      )}
    </div>
  );

  // 头部右侧内容
  const headerExtra = (
    <Space size={isMobile ? "small" : "middle"}>
      {/* <LanguageSwitcher /> */}
      <QuickThemeToggle size={isMobile ? "small" : "middle"} />
      {!isMobile && <ThemeToggle />}
      <Dropdown
        menu={{ items: userMenuItems }}
        placement="bottomRight"
        trigger={["click"]}
      >
        <Space style={{ cursor: "pointer" }} size="small">
          <Avatar
            size={isMobile ? "small" : "default"}
            icon={<UserOutlined />}
            src={userInfo?.avatar}
          />
          {!isMobile && (
            <Text>{userInfo?.nickname || userInfo?.username || "用户"}</Text>
          )}
        </Space>
      </Dropdown>
    </Space>
  );

  return (
    <ResponsiveLayout
      siderContent={siderContent}
      headerContent={headerContent}
      headerExtra={headerExtra}
      collapsed={sidebarCollapsed}
      onCollapse={toggleSidebar}
      style={{ minHeight: "100vh" }}
    >
      <div
        style={{
          background: "#fff",
          borderRadius: isMobile ? "4px" : "8px",
          padding: layout.contentPadding,
          minHeight: `calc(100vh - ${
            layout.headerHeight + (isMobile ? 24 : 48)
          }px)`,
        }}
      >
        <ErrorBoundary
          level="page"
          showDetails={process.env.NODE_ENV === "development"}
        >
          {children}
        </ErrorBoundary>
      </div>
    </ResponsiveLayout>
  );
}
