"use client";

import React, { Suspense, lazy } from "react";
import { Spin, Card, Skeleton } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

// 加载状态组件
export const PageLoading = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "200px",
      flexDirection: "column",
      gap: "16px",
    }}
  >
    <Spin
      size="large"
      indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
    />
    <div style={{ color: "#666", fontSize: "14px" }}>页面加载中...</div>
  </div>
);

// 组件加载状态
export const ComponentLoading = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100px",
    }}
  >
    <Spin size="small" />
  </div>
);

// 表格加载状态
export const TableLoading = () => (
  <Card>
    <Skeleton active paragraph={{ rows: 6 }} />
  </Card>
);

// 表单加载状态
export const FormLoading = () => (
  <Card>
    <Skeleton active paragraph={{ rows: 4 }} />
  </Card>
);

// 图表加载状态
export const ChartLoading = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "300px",
      background: "#fafafa",
      borderRadius: "6px",
    }}
  >
    <Spin size="large" />
  </div>
);

// 创建懒加载组件的高阶函数
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback: React.ComponentType = PageLoading
) {
  const LazyComponent = lazy(importFunc);

  return (props: React.ComponentProps<T>) => (
    <Suspense fallback={React.createElement(fallback)}>
      <LazyComponent {...props} />
    </Suspense>
  );
}

// 懒加载页面组件
export const LazyDashboard = createLazyComponent(
  () => import("@/app/main/dashboard/page"),
  PageLoading
);

export const LazyUsers = createLazyComponent(
  () => import("@/app/main/users/page"),
  TableLoading
);

export const LazyShop = createLazyComponent(
  () => import("@/app/main/shop/page"),
  TableLoading
);

export const LazyBusiness = createLazyComponent(
  () => import("@/app/main/business/page"),
  TableLoading
);

// export const LazyArticles = createLazyComponent(
//   () => import("@/app/main/articles/page"),
//   TableLoading
// );

// export const LazyCategories = createLazyComponent(
//   () => import("@/app/main/categories/page"),
//   TableLoading
// );

export const LazySettings = createLazyComponent(
  () => import("@/app/main/settings/page"),
  FormLoading
);

// export const LazyFiles = createLazyComponent(
//   () => import("@/app/main/files/page"),
//   TableLoading
// );

// 懒加载示例页面
export const LazyTableExample = createLazyComponent(
  () => import("@/app/main/examples/table/page"),
  TableLoading
);

export const LazyFormExample = createLazyComponent(
  () => import("@/app/main/examples/forms/page"),
  FormLoading
);

export const LazyResponsiveExample = createLazyComponent(
  () => import("@/app/main/examples/responsive/page"),
  PageLoading
);

// 懒加载图片组件
interface LazyImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  style,
  placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij5Mb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg==",
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  const [isInView, setIsInView] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);

  // 使用 Intersection Observer 实现懒加载
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: "50px",
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div
      ref={imgRef}
      style={{
        width,
        height,
        position: "relative",
        overflow: "hidden",
        ...style,
      }}
      className={className}
    >
      {/* 占位符 */}
      {!isLoaded && !hasError && (
        <img
          src={placeholder}
          alt="Loading..."
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            filter: "blur(2px)",
          }}
        />
      )}

      {/* 实际图片 */}
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            position: isLoaded ? "static" : "absolute",
            top: 0,
            left: 0,
            opacity: isLoaded ? 1 : 0,
            transition: "opacity 0.3s ease",
          }}
        />
      )}

      {/* 错误状态 */}
      {hasError && (
        <div
          style={{
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            background: "#f5f5f5",
            color: "#999",
            fontSize: "14px",
          }}
        >
          图片加载失败
        </div>
      )}
    </div>
  );
};

// 虚拟滚动组件
interface VirtualScrollProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  overscan?: number;
}

export const VirtualScroll: React.FC<VirtualScrollProps> = ({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
}) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  return (
    <div
      ref={containerRef}
      style={{
        height: containerHeight,
        overflow: "auto",
      }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: "relative" }}>
        <div
          style={{
            transform: `translateY(${startIndex * itemHeight}px)`,
          }}
        >
          {visibleItems.map((item, index) => (
            <div key={startIndex + index}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// 代码分割路由组件
export const LazyRoute: React.FC<{
  component: React.ComponentType;
  fallback?: React.ComponentType;
}> = ({ component: Component, fallback: Fallback = PageLoading }) => {
  return (
    <Suspense fallback={<Fallback />}>
      <Component />
    </Suspense>
  );
};
