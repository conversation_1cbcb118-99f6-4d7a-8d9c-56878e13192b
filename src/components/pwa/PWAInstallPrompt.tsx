'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Card, Space, Typography, Modal, Steps, Image } from 'antd';
import { 
  DownloadOutlined, 
  MobileOutlined, 
  DesktopOutlined,
  CloseOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useI18n } from '@/hooks/useI18n';

const { Title, Text, Paragraph } = Typography;

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export default function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [deviceType, setDeviceType] = useState<'mobile' | 'desktop'>('desktop');
  const { common } = useI18n();

  useEffect(() => {
    // 检测设备类型
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    setDeviceType(isMobile ? 'mobile' : 'desktop');

    // 监听 beforeinstallprompt 事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // 延迟显示安装提示，避免打扰用户
      setTimeout(() => {
        setShowPrompt(true);
      }, 30000); // 30秒后显示
    };

    // 监听应用安装事件
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowPrompt(false);
      setDeferredPrompt(null);
    };

    // 检查是否已经安装
    const checkIfInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    checkIfInstalled();

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // 处理安装点击
  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      setShowInstructions(true);
      return;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('用户接受了安装提示');
      } else {
        console.log('用户拒绝了安装提示');
      }
      
      setDeferredPrompt(null);
      setShowPrompt(false);
    } catch (error) {
      console.error('安装过程中出错:', error);
      setShowInstructions(true);
    }
  };

  // 关闭提示
  const handleClose = () => {
    setShowPrompt(false);
    // 24小时后再次显示
    localStorage.setItem('pwa-prompt-dismissed', Date.now().toString());
  };

  // 检查是否应该显示提示
  const shouldShowPrompt = () => {
    const dismissed = localStorage.getItem('pwa-prompt-dismissed');
    if (dismissed) {
      const dismissedTime = parseInt(dismissed);
      const now = Date.now();
      const hoursPassed = (now - dismissedTime) / (1000 * 60 * 60);
      return hoursPassed > 24; // 24小时后再次显示
    }
    return true;
  };

  // 手动安装说明步骤
  const getInstallSteps = () => {
    if (deviceType === 'mobile') {
      return [
        {
          title: '打开浏览器菜单',
          description: '点击浏览器右上角的菜单按钮（三个点）',
          icon: <MobileOutlined />,
        },
        {
          title: '选择"添加到主屏幕"',
          description: '在菜单中找到"添加到主屏幕"或"安装应用"选项',
          icon: <DownloadOutlined />,
        },
        {
          title: '确认安装',
          description: '点击"添加"或"安装"按钮完成安装',
          icon: <CheckCircleOutlined />,
        },
      ];
    } else {
      return [
        {
          title: '查看地址栏',
          description: '在浏览器地址栏右侧查找安装图标',
          icon: <DesktopOutlined />,
        },
        {
          title: '点击安装图标',
          description: '点击地址栏中的安装图标或下载按钮',
          icon: <DownloadOutlined />,
        },
        {
          title: '确认安装',
          description: '在弹出的对话框中点击"安装"按钮',
          icon: <CheckCircleOutlined />,
        },
      ];
    }
  };

  if (isInstalled || !shouldShowPrompt()) {
    return null;
  }

  return (
    <>
      {/* 安装提示卡片 */}
      {showPrompt && (
        <Card
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            width: '320px',
            zIndex: 1000,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          }}
          actions={[
            <Button key="install" type="primary" icon={<DownloadOutlined />} onClick={handleInstallClick}>
              立即安装
            </Button>,
            <Button key="close" type="text" icon={<CloseOutlined />} onClick={handleClose}>
              稍后提醒
            </Button>,
          ]}
        >
          <Space direction="vertical" size="small">
            <Title level={5} style={{ margin: 0 }}>
              📱 安装 CCXC
            </Title>
            <Text type="secondary">
              将应用安装到您的设备上，享受更好的使用体验：
            </Text>
            <ul style={{ margin: '8px 0', paddingLeft: '20px', fontSize: '12px', color: '#666' }}>
              <li>离线访问</li>
              <li>快速启动</li>
              <li>原生体验</li>
              <li>桌面图标</li>
            </ul>
          </Space>
        </Card>
      )}

      {/* 手动安装说明弹窗 */}
      <Modal
        title="安装 CCXC 应用"
        open={showInstructions}
        onCancel={() => setShowInstructions(false)}
        footer={[
          <Button key="close" onClick={() => setShowInstructions(false)}>
            我知道了
          </Button>,
        ]}
        width={600}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Paragraph>
            您的浏览器不支持自动安装，请按照以下步骤手动安装应用：
          </Paragraph>
          
          <Steps
            direction="vertical"
            current={-1}
            items={getInstallSteps()}
          />
          
          <div style={{ 
            padding: '16px', 
            background: '#f6ffed', 
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
          }}>
            <Text strong style={{ color: '#52c41a' }}>
              💡 提示：
            </Text>
            <br />
            <Text style={{ color: '#52c41a', fontSize: '12px' }}>
              安装后，您可以像使用原生应用一样使用 CCXC，
              支持离线访问和推送通知。
            </Text>
          </div>
        </Space>
      </Modal>
    </>
  );
}

// PWA状态指示器
export function PWAStatus() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // 检查是否已安装
    const checkIfInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
      }
    };

    // 检查网络状态
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    checkIfInstalled();
    setIsOnline(navigator.onLine);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isInstalled) return null;

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      zIndex: 999,
      padding: '4px 8px',
      background: isOnline ? '#f6ffed' : '#fff2e8',
      border: `1px solid ${isOnline ? '#b7eb8f' : '#ffbb96'}`,
      borderRadius: '4px',
      fontSize: '12px',
      color: isOnline ? '#52c41a' : '#fa8c16',
    }}>
      {isOnline ? '🟢 在线' : '🔴 离线'}
    </div>
  );
}

// PWA更新提示
export function PWAUpdatePrompt() {
  const [showUpdate, setShowUpdate] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then((reg) => {
        setRegistration(reg);
        
        reg.addEventListener('updatefound', () => {
          const newWorker = reg.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setShowUpdate(true);
              }
            });
          }
        });
      });
    }
  }, []);

  const handleUpdate = () => {
    if (registration && registration.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  };

  if (!showUpdate) return null;

  return (
    <Card
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        width: '300px',
        zIndex: 1001,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      }}
      actions={[
        <Button key="update" type="primary" onClick={handleUpdate}>
          立即更新
        </Button>,
        <Button key="later" type="text" onClick={() => setShowUpdate(false)}>
          稍后更新
        </Button>,
      ]}
    >
      <Space direction="vertical" size="small">
        <Title level={5} style={{ margin: 0 }}>
          🔄 发现新版本
        </Title>
        <Text type="secondary">
          应用有新版本可用，建议立即更新以获得最佳体验。
        </Text>
      </Space>
    </Card>
  );
}
