"use client";

import React, { useState } from "react";
import {
  Upload,
  Button,
  message,
  Progress,
  Image,
  Space,
  Typography,
  Card,
} from "antd";
import {
  UploadOutlined,
  InboxOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import type { UploadProps, UploadFile } from "antd";

const { Dragger } = Upload;
const { Text } = Typography;

interface FileUploadProps {
  accept?: string;
  maxCount?: number;
  maxSize?: number; // MB
  listType?: "text" | "picture" | "picture-card" | "picture-circle";
  multiple?: boolean;
  directory?: boolean;
  showUploadList?: boolean;
  disabled?: boolean;
  value?: UploadFile[];
  onChange?: (fileList: UploadFile[]) => void;
  onPreview?: (file: UploadFile) => void;
  onDownload?: (file: UploadFile) => void;
  uploadUrl?: string;
  headers?: Record<string, string>;
  data?: Record<string, any>;
  beforeUpload?: (file: File) => boolean | Promise<boolean>;
  customRequest?: (options: any) => void;
}

export default function FileUpload({
  accept = "*/*",
  maxCount = 10,
  maxSize = 10,
  listType = "text",
  multiple = true,
  directory = false,
  showUploadList = true,
  disabled = false,
  value = [],
  onChange,
  onPreview,
  onDownload,
  uploadUrl = "/api/upload",
  headers = {},
  data = {},
  beforeUpload,
  customRequest,
}: FileUploadProps) {
  const [fileList, setFileList] = useState<UploadFile[]>(value);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [previewTitle, setPreviewTitle] = useState("");

  // 文件上传前的检查
  const handleBeforeUpload = (file: File) => {
    // 文件大小检查
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLtMaxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB!`);
      return false;
    }

    // 文件类型检查
    if (accept !== "*/*") {
      const acceptTypes = accept.split(",").map((type) => type.trim());
      const fileType = file.type;
      const fileName = file.name;
      const fileExt = fileName.substring(fileName.lastIndexOf("."));

      const isAcceptType = acceptTypes.some((type) => {
        if (type.startsWith(".")) {
          return fileExt === type;
        }
        return fileType.match(type.replace("*", ".*"));
      });

      if (!isAcceptType) {
        message.error(`只能上传 ${accept} 格式的文件!`);
        return false;
      }
    }

    // 自定义检查
    if (beforeUpload) {
      return beforeUpload(file);
    }

    return true;
  };

  // 文件列表变化
  const handleChange: UploadProps["onChange"] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    onChange?.(newFileList);
  };

  // 预览文件
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as File);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(
      file.name || file.url!.substring(file.url!.lastIndexOf("/") + 1)
    );

    onPreview?.(file);
  };

  // 下载文件
  const handleDownload = (file: UploadFile) => {
    if (file.url) {
      const link = document.createElement("a");
      link.href = file.url;
      link.download = file.name || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    onDownload?.(file);
  };

  // 自定义上传请求
  const handleCustomRequest = (options: any) => {
    if (customRequest) {
      customRequest(options);
      return;
    }

    const { file, onSuccess, onError, onProgress } = options;
    const formData = new FormData();
    formData.append("file", file);

    // 添加额外数据
    Object.keys(data).forEach((key) => {
      formData.append(key, data[key]);
    });

    // 模拟上传进度
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 30;
      if (progress > 100) {
        progress = 100;
        clearInterval(timer);

        // 模拟上传成功
        setTimeout(() => {
          onSuccess({
            url: URL.createObjectURL(file),
            name: file.name,
          });
        }, 200);
      }
      onProgress({ percent: Math.round(progress) });
    }, 200);
  };

  // 获取文件base64
  const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  // 上传属性
  const uploadProps: UploadProps = {
    name: "file",
    multiple,
    directory,
    listType,
    fileList,
    accept,
    disabled,
    showUploadList: showUploadList
      ? {
          showPreviewIcon: true,
          showDownloadIcon: true,
          showRemoveIcon: true,
          previewIcon: <EyeOutlined />,
          downloadIcon: <DownloadOutlined />,
          removeIcon: <DeleteOutlined />,
        }
      : false,
    beforeUpload: handleBeforeUpload,
    onChange: handleChange,
    onPreview: handlePreview,
    onDownload: handleDownload,
    customRequest: handleCustomRequest,
    headers: {
      Authorization: `Bearer ${
        typeof window !== "undefined" ? localStorage.getItem("token") : ""
      }`,
      ...headers,
    },
  };

  return (
    <>
      {listType === "picture-card" ? (
        <Upload {...uploadProps}>
          {fileList.length >= maxCount ? null : (
            <div>
              <UploadOutlined />
              <div style={{ marginTop: 8 }}>上传</div>
            </div>
          )}
        </Upload>
      ) : (
        <Dragger {...uploadProps} style={{ padding: "20px" }}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined style={{ fontSize: "48px", color: "#1890ff" }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传。文件大小不超过 {maxSize}MB
            {accept !== "*/*" && `，支持格式：${accept}`}
          </p>
        </Dragger>
      )}

      {/* 图片预览 */}
      <Image
        wrapperStyle={{ display: "none" }}
        preview={{
          visible: previewVisible,
          onVisibleChange: setPreviewVisible,
          afterOpenChange: (visible) => !visible && setPreviewImage(""),
        }}
        src={previewImage}
      />
    </>
  );
}

// 图片上传组件
export function ImageUpload({
  value = [],
  onChange,
  maxCount = 5,
  maxSize = 5,
  ...props
}: Omit<FileUploadProps, "accept" | "listType">) {
  return (
    <FileUpload
      accept="image/*"
      listType="picture-card"
      value={value}
      onChange={onChange}
      maxCount={maxCount}
      maxSize={maxSize}
      {...props}
    />
  );
}

// 文档上传组件
export function DocumentUpload({
  value = [],
  onChange,
  maxCount = 10,
  maxSize = 20,
  ...props
}: Omit<FileUploadProps, "accept">) {
  return (
    <FileUpload
      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
      value={value}
      onChange={onChange}
      maxCount={maxCount}
      maxSize={maxSize}
      {...props}
    />
  );
}

// 简单上传按钮
export function SimpleUpload({ children, ...props }: any) {
  // }: FileUploadProps & { children?: React.ReactNode }) {
  return (
    <Upload {...props} showUploadList={false}>
      {children || <Button icon={<UploadOutlined />}>点击上传</Button>}
    </Upload>
  );
}

// 上传进度卡片
export function UploadProgressCard({
  fileList,
  onRemove,
}: {
  fileList: UploadFile[];
  onRemove?: (file: UploadFile) => void;
}) {
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      {fileList.map((file) => (
        <Card
          key={file.uid}
          size="small"
          extra={
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => onRemove?.(file)}
            />
          }
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Text ellipsis style={{ maxWidth: "200px" }}>
                {file.name}
              </Text>
              <Text type="secondary">{file.status}</Text>
            </div>
            {file.percent !== undefined && (
              <Progress
                percent={file.percent}
                size="small"
                status={file.status === "error" ? "exception" : undefined}
              />
            )}
          </Space>
        </Card>
      ))}
    </Space>
  );
}
