'use client';

import React from 'react';
import { Button, Dropdown, Space, Typography, ColorPicker, Slider, Switch } from 'antd';
import {
  SettingOutlined,
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  BgColorsOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useTheme } from '@/store/useThemeStore';
import { primaryColors } from '@/config/theme';
import { useAppStore } from '@/store/useAppStore';

const { Text } = Typography;

export default function ThemeToggle() {
  const {
    mode,
    primaryColor,
    borderRadius,
    compactMode,
    setMode,
    setPrimaryColor,
    setBorderRadius,
    setCompactMode,
    resetTheme,
    getPrimaryColorOptions,
  } = useTheme();

  // 主题模式选项
  const themeMenuItems: MenuProps['items'] = [
    {
      key: 'light',
      icon: <SunOutlined />,
      label: '浅色主题',
      onClick: () => setMode('light'),
    },
    {
      key: 'dark',
      icon: <MoonOutlined />,
      label: '深色主题',
      onClick: () => setMode('dark'),
    },
    {
      key: 'auto',
      icon: <DesktopOutlined />,
      label: '跟随系统',
      onClick: () => setMode('auto'),
    },
  ];

  // 主题设置面板
  const themeSettingsPanel = (
    <div style={{
      padding: '16px',
      width: '300px',
      background: '#fff',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    }}>
      <div style={{ marginBottom: '16px' }}>
        <Text strong style={{ fontSize: '16px' }}>🎨 主题设置</Text>
      </div>

      {/* 主题模式 */}
      <div style={{ marginBottom: '20px' }}>
        <Text strong style={{ display: 'block', marginBottom: '8px' }}>
          主题模式
        </Text>
        <Space>
          <Button
            type={mode === 'light' ? 'primary' : 'default'}
            icon={<SunOutlined />}
            size="small"
            onClick={() => setMode('light')}
          >
            浅色
          </Button>
          <Button
            type={mode === 'dark' ? 'primary' : 'default'}
            icon={<MoonOutlined />}
            size="small"
            onClick={() => setMode('dark')}
          >
            深色
          </Button>
          <Button
            type={mode === 'auto' ? 'primary' : 'default'}
            icon={<DesktopOutlined />}
            size="small"
            onClick={() => setMode('auto')}
          >
            自动
          </Button>
        </Space>
      </div>

      {/* 主色调 */}
      <div style={{ marginBottom: '20px' }}>
        <Text strong style={{ display: 'block', marginBottom: '8px' }}>
          主色调
        </Text>
        <Space wrap>
          <ColorPicker
            value={primaryColor}
            onChange={(color) => setPrimaryColor(color.toHexString())}
            showText
            size="small"
          />
          <Space size="small">
            {getPrimaryColorOptions().slice(0, 6).map(({ color }) => (
              <div
                key={color}
                style={{
                  width: '20px',
                  height: '20px',
                  backgroundColor: color,
                  borderRadius: '4px',
                  cursor: 'pointer',
                  border: primaryColor === color ? '2px solid #000' : '1px solid #d9d9d9',
                }}
                onClick={() => setPrimaryColor(color)}
              />
            ))}
          </Space>
        </Space>
      </div>

      {/* 圆角大小 */}
      <div style={{ marginBottom: '20px' }}>
        <Text strong style={{ display: 'block', marginBottom: '8px' }}>
          圆角大小: {borderRadius}px
        </Text>
        <Slider
          min={0}
          max={20}
          value={borderRadius}
          onChange={setBorderRadius}
          marks={{
            0: '0',
            6: '6',
            12: '12',
            20: '20',
          }}
        />
      </div>

      {/* 功能开关 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '8px',
        }}>
          <Text>紧凑模式</Text>
          <Switch
            size="small"
            checked={compactMode}
            onChange={setCompactMode}
          />
        </div>
      </div>

      {/* 重置按钮 */}
      <div style={{ textAlign: 'center', paddingTop: '12px', borderTop: '1px solid #f0f0f0' }}>
        <Button
          size="small"
          onClick={resetTheme}
        >
          重置默认
        </Button>
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => themeSettingsPanel}
      trigger={['click']}
      placement="bottomRight"
    >
      <Button
        type="text"
        icon={<SettingOutlined />}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
        }}
        title="主题设置"
      />
    </Dropdown>
  );
}

// 简化版主题切换按钮
export function SimpleThemeToggle() {
  const { themeMode, setThemeMode } = useAppStore();

  const getIcon = () => {
    switch (themeMode) {
      case 'light':
        return <SunOutlined />;
      case 'dark':
        return <MoonOutlined />;
      case 'auto':
        return <DesktopOutlined />;
      default:
        return <SunOutlined />;
    }
  };

  const handleToggle = () => {
    const modes: Array<'light' | 'dark' | 'auto'> = ['light', 'dark', 'auto'];
    const currentIndex = modes.indexOf(themeMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex]);
  };

  return (
    <Button
      type="text"
      icon={getIcon()}
      onClick={handleToggle}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '40px',
        height: '40px',
      }}
      title={`当前: ${themeMode === 'light' ? '浅色' : themeMode === 'dark' ? '深色' : '自动'} 主题`}
    />
  );
}

// 主题色彩选择器
export function ColorThemeSelector() {
  const { primaryColor, setPrimaryColor } = useAppStore();

  const presetColors = [
    { name: '拂晓蓝', color: '#1890ff' },
    { name: '极光绿', color: '#52c41a' },
    { name: '日暮黄', color: '#faad14' },
    { name: '薄暮红', color: '#f5222d' },
    { name: '酱紫色', color: '#722ed1' },
    { name: '明青色', color: '#13c2c2' },
  ];

  return (
    <Space wrap>
      {presetColors.map(({ name, color }) => (
        <div
          key={color}
          title={name}
          style={{
            width: '24px',
            height: '24px',
            backgroundColor: color,
            borderRadius: '50%',
            cursor: 'pointer',
            border: primaryColor === color ? '3px solid #000' : '2px solid #fff',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            transition: 'all 0.2s',
          }}
          onClick={() => setPrimaryColor(color)}
        />
      ))}
      <ColorPicker
        value={primaryColor}
        onChange={(color) => setPrimaryColor(color.toHexString())}
        size="small"
        trigger="hover"
      >
        <div
          style={{
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            border: '2px dashed #d9d9d9',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          <BgColorsOutlined />
        </div>
      </ColorPicker>
    </Space>
  );
}
