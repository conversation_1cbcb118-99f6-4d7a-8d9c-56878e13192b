'use client';

import React, { useEffect } from 'react';
import { ConfigProvider, App } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useThemeStore, useSystemThemeWatcher } from '@/store/useThemeStore';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export default function ThemeProvider({ children }: ThemeProviderProps) {
  const { themeConfig, mode, actualTheme, updateThemeConfig } = useThemeStore();

  // 监听系统主题变化
  useSystemThemeWatcher();

  // 初始化时更新主题配置
  useEffect(() => {
    updateThemeConfig();
  }, [updateThemeConfig]);

  // 设置 HTML 根元素的主题类名和CSS变量
  useEffect(() => {
    const root = document.documentElement;

    // 移除所有主题类名
    root.classList.remove('theme-light', 'theme-dark', 'theme-auto');

    // 添加当前主题类名
    root.classList.add(`theme-${mode}`);
    root.classList.add(`theme-actual-${actualTheme}`);

    // 设置 CSS 变量
    root.style.setProperty('--theme-mode', mode);
    root.style.setProperty('--theme-actual', actualTheme);

    // 设置主色调 CSS 变量
    if (themeConfig.token?.colorPrimary) {
      root.style.setProperty('--primary-color', themeConfig.token.colorPrimary);
    }

    // 设置背景色 CSS 变量
    if (themeConfig.token?.colorBgContainer) {
      root.style.setProperty('--bg-container', themeConfig.token.colorBgContainer);
    }

    if (themeConfig.token?.colorBgLayout) {
      root.style.setProperty('--bg-layout', themeConfig.token.colorBgLayout);
    }

    // 设置文字色 CSS 变量
    if (themeConfig.token?.colorText) {
      root.style.setProperty('--text-color', themeConfig.token.colorText);
    }

    if (themeConfig.token?.colorTextSecondary) {
      root.style.setProperty('--text-color-secondary', themeConfig.token.colorTextSecondary);
    }

    // 设置边框色 CSS 变量
    if (themeConfig.token?.colorBorder) {
      root.style.setProperty('--border-color', themeConfig.token.colorBorder);
    }

    // 设置圆角 CSS 变量
    if (themeConfig.token?.borderRadius) {
      root.style.setProperty('--border-radius', `${themeConfig.token.borderRadius}px`);
    }
  }, [themeConfig, mode, actualTheme]);



  return (
    <ConfigProvider
      theme={themeConfig}
      locale={zhCN}
      componentSize="middle"
    >
      <App>
        {children}
      </App>
    </ConfigProvider>
  );
}
