"use client";

import React from "react";
import { But<PERSON>, Tooltip } from "antd";
import { SunOutlined, MoonOutlined, DesktopOutlined } from "@ant-design/icons";
import { useTheme } from "@/store/useThemeStore";

interface QuickThemeToggleProps {
  size?: "small" | "middle" | "large";
  type?: "default" | "primary" | "ghost" | "dashed" | "link" | "text";
  showText?: boolean;
}

export default function QuickThemeToggle({
  size = "middle",
  type = "text",
  showText = false,
}: QuickThemeToggleProps) {
  const { mode, setMode, actualTheme } = useTheme();

  const themeOptions = [
    { key: "light", icon: <SunOutlined />, label: "浅色主题" },
    { key: "dark", icon: <MoonOutlined />, label: "深色主题" },
    { key: "auto", icon: <DesktopOutlined />, label: "跟随系统" },
  ];

  const currentOption = themeOptions.find((option) => option.key === mode);
  const nextMode =
    mode === "light" ? "dark" : mode === "dark" ? "auto" : "light";
  const nextOption = themeOptions.find((option) => option.key === nextMode);

  const handleToggle = () => {
    setMode(nextMode as any);
  };

  return (
    <Tooltip
      title={`当前: ${currentOption?.label} | 点击切换到: ${nextOption?.label}`}
    >
      <Button
        type={type as any}
        size={size}
        icon={currentOption?.icon}
        onClick={handleToggle}
        style={{
          display: "flex",
          alignItems: "center",
          gap: showText ? "8px" : 0,
        }}
      >
        {showText && currentOption?.label}
      </Button>
    </Tooltip>
  );
}
