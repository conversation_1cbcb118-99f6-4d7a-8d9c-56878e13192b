'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Spin } from 'antd';
import { useUserStore } from '@/store/useUserStore';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

// 不需要认证的公开路由
const publicRoutes = [
  '/',
  '/login',
  '/register',
  '/forgot-password',
];

export default function AuthGuard({
  children,
  requireAuth = true,
  requiredPermissions = [],
  fallback
}: AuthGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { isLoggedIn, userInfo, token, fetchUserInfo, hasPermission } = useUserStore();
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 如果是公开路由，直接允许访问
        if (publicRoutes.includes(pathname)) {
          setAuthorized(true);
          setLoading(false);
          return;
        }

        // 如果不需要认证，直接允许访问
        if (!requireAuth) {
          setAuthorized(true);
          setLoading(false);
          return;
        }

        // 检查是否有token
        const storedToken = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

        if (!storedToken && !token) {
          // 没有token，跳转到登录页
          router.replace(`/login?redirect=${encodeURIComponent(pathname)}`);
          setLoading(false);
          return;
        }

        // 如果有token但没有用户信息，尝试获取用户信息
        if (storedToken && !userInfo) {
          try {
            await fetchUserInfo();
          } catch (error) {
            console.error('获取用户信息失败:', error);
            // 获取用户信息失败，清除token并跳转到登录页
            localStorage.removeItem('token');
            router.replace(`/login?redirect=${encodeURIComponent(pathname)}`);
            setLoading(false);
            return;
          }
        }

        // 检查权限
        if (requiredPermissions.length > 0) {
          const hasRequiredPermissions = requiredPermissions.every(permission =>
            hasPermission(permission)
          );

          if (!hasRequiredPermissions) {
            // 没有权限，显示403页面或跳转
            setAuthorized(false);
            setLoading(false);
            return;
          }
        }

        // 所有检查通过
        setAuthorized(true);
      } catch (error) {
        console.error('Auth check error:', error);
        setAuthorized(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [pathname, isLoggedIn, userInfo, token, requireAuth, requiredPermissions, router, fetchUserInfo, hasPermission]);

  // 加载中
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        gap: '16px',
      }}>
        <Spin size="large" />
        <div style={{ color: '#666', fontSize: '14px' }}>
          加载中...
        </div>
      </div>
    );
  }

  // 未授权
  if (!authorized) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        textAlign: 'center',
      }}>
        <div style={{ fontSize: '72px', marginBottom: '24px' }}>🚫</div>
        <h1>访问被拒绝</h1>
        <p>您没有权限访问此页面</p>
      </div>
    );
  }

  return <>{children}</>;
}

// 高阶组件版本
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireAuth?: boolean;
    requiredPermissions?: string[];
  }
) {
  const WrappedComponent = (props: P) => {
    return (
      <AuthGuard
        requireAuth={options?.requireAuth}
        requiredPermissions={options?.requiredPermissions}
      >
        <Component {...props} />
      </AuthGuard>
    );
  };

  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
