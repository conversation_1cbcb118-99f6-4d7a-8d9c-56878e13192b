'use client';

import React, { useState } from 'react';
import { Button, Dropdown, Space, Typography, message } from 'antd';
import { GlobalOutlined, CheckOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Text } = Typography;

// 简化的语言配置（暂时禁用国际化功能）
const languages = [
  { key: 'zh', name: '简体中文', flag: '🇨🇳' },
  { key: 'en', name: 'English', flag: '🇺🇸' },
];

interface LanguageSwitcherProps {
  size?: 'small' | 'middle' | 'large';
  type?: 'default' | 'text' | 'link' | 'primary';
  showText?: boolean;
  placement?: 'bottom' | 'bottomLeft' | 'bottomRight' | 'top' | 'topLeft' | 'topRight';
}

export default function LanguageSwitcher({
  size = 'middle',
  type = 'text',
  showText = false,
  placement = 'bottomRight',
}: LanguageSwitcherProps) {
  const [currentLang, setCurrentLang] = useState('zh');
  const [loading, setLoading] = useState(false);

  // 切换语言（暂时只显示提示）
  const handleLanguageChange = (langKey: string) => {
    if (langKey === currentLang) return;

    setLoading(true);
    setTimeout(() => {
      setCurrentLang(langKey);
      setLoading(false);
      message.info('语言切换功能正在开发中，敬请期待！');
    }, 500);
  };

  // 菜单项
  const menuItems: MenuProps['items'] = languages.map((lang) => ({
    key: lang.key,
    label: (
      <Space>
        <span style={{ fontSize: '16px' }}>{lang.flag}</span>
        <Text>{lang.name}</Text>
        {currentLang === lang.key && <CheckOutlined style={{ color: '#1890ff' }} />}
      </Space>
    ),
    onClick: () => handleLanguageChange(lang.key),
  }));

  const currentLanguage = languages.find(lang => lang.key === currentLang) || languages[0];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement={placement}
      trigger={['click']}
      disabled={loading}
    >
      <Button
        type={type}
        size={size}
        icon={<GlobalOutlined />}
        loading={loading}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...(showText ? {} : { width: '40px', height: '40px' }),
        }}
      >
        {showText && (
          <Space>
            <span style={{ fontSize: '14px' }}>{currentLanguage.flag}</span>
            <Text>{currentLanguage.name}</Text>
          </Space>
        )}
      </Button>
    </Dropdown>
  );
}

// 简化版语言切换器（暂时禁用）
export function SimpleLanguageSwitcher() {
  return (
    <Button
      type="text"
      size="small"
      disabled
      title="语言切换功能正在开发中"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '40px',
        height: '40px',
      }}
    >
      <Space>
        <span style={{ fontSize: '16px' }}>🇨🇳</span>
      </Space>
    </Button>
  );
}
