"use client";

import React from "react";
import { message } from "antd";
import { MsgContext } from "@/hooks/messageInstance";
import { setMessageApi } from "@/lib/axios";

interface MessageProviderProps {
  children: React.ReactNode;
}

export default function MessageProvider({ children }: MessageProviderProps) {
  const [messageApi, contextHolder] = message.useMessage();

  React.useEffect(() => {
    // 存到全局 axios 工具里
    setMessageApi(messageApi);
  }, [messageApi]);
  return (
    <MsgContext.Provider value={messageApi}>
      {contextHolder}
      {children}
    </MsgContext.Provider>
  );
}
