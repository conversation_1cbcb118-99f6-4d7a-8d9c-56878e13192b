import React from 'react'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockData, mockApi } from '@/utils/test-utils'
import DataTable from '@/components/common/DataTable'

// Mock the HTTP request library
jest.mock('@/lib/request', () => ({
  http: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}))

describe('DataTable Component', () => {
  const mockColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
  ]

  const mockDataSource = [
    { id: '1', name: 'User 1', email: '<EMAIL>' },
    { id: '2', name: 'User 2', email: '<EMAIL>' },
    { id: '3', name: 'User 3', email: '<EMAIL>' },
  ]

  const defaultProps = {
    columns: mockColumns,
    dataSource: mockDataSource,
    rowKey: 'id',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders table with data correctly', () => {
    render(<DataTable {...defaultProps} />)
    
    // Check if table headers are rendered
    expect(screen.getByText('ID')).toBeInTheDocument()
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Email')).toBeInTheDocument()
    
    // Check if data rows are rendered
    expect(screen.getByText('User 1')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<DataTable {...defaultProps} loading={true} />)
    
    // Check if loading spinner is present
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('shows empty state when no data', () => {
    render(<DataTable {...defaultProps} dataSource={[]} />)
    
    // Check if empty state is shown
    expect(screen.getByText('暂无数据')).toBeInTheDocument()
  })

  it('handles pagination correctly', async () => {
    const mockPagination = {
      current: 1,
      pageSize: 10,
      total: 100,
      showSizeChanger: true,
      showQuickJumper: true,
    }

    const onPaginationChange = jest.fn()

    render(
      <DataTable 
        {...defaultProps} 
        pagination={mockPagination}
        onPaginationChange={onPaginationChange}
      />
    )
    
    // Check if pagination is rendered
    expect(screen.getByText('1')).toBeInTheDocument()
    
    // Click next page
    const nextButton = screen.getByTitle('下一页')
    fireEvent.click(nextButton)
    
    await waitFor(() => {
      expect(onPaginationChange).toHaveBeenCalledWith(2, 10)
    })
  })

  it('handles search functionality', async () => {
    const onSearch = jest.fn()

    render(
      <DataTable 
        {...defaultProps} 
        searchable={true}
        onSearch={onSearch}
      />
    )
    
    // Find search input
    const searchInput = screen.getByPlaceholderText('搜索...')
    
    // Type in search input
    fireEvent.change(searchInput, { target: { value: 'User 1' } })
    
    // Wait for debounced search
    await waitFor(() => {
      expect(onSearch).toHaveBeenCalledWith('User 1')
    }, { timeout: 1000 })
  })

  it('handles row selection', () => {
    const onSelectionChange = jest.fn()

    render(
      <DataTable 
        {...defaultProps} 
        selectable={true}
        onSelectionChange={onSelectionChange}
      />
    )
    
    // Find and click first row checkbox
    const checkboxes = screen.getAllByRole('checkbox')
    fireEvent.click(checkboxes[1]) // First checkbox is select all
    
    expect(onSelectionChange).toHaveBeenCalledWith(['1'], [mockDataSource[0]])
  })

  it('handles sorting', () => {
    const onSortChange = jest.fn()

    const sortableColumns = [
      {
        ...mockColumns[0],
        sorter: true,
      },
      ...mockColumns.slice(1),
    ]

    render(
      <DataTable 
        {...defaultProps} 
        columns={sortableColumns}
        onSortChange={onSortChange}
      />
    )
    
    // Click on sortable column header
    const sortableHeader = screen.getByText('ID')
    fireEvent.click(sortableHeader)
    
    expect(onSortChange).toHaveBeenCalled()
  })

  it('handles refresh action', () => {
    const onRefresh = jest.fn()

    render(
      <DataTable 
        {...defaultProps} 
        refreshable={true}
        onRefresh={onRefresh}
      />
    )
    
    // Find and click refresh button
    const refreshButton = screen.getByTitle('刷新')
    fireEvent.click(refreshButton)
    
    expect(onRefresh).toHaveBeenCalled()
  })

  it('handles export action', () => {
    const onExport = jest.fn()

    render(
      <DataTable 
        {...defaultProps} 
        exportable={true}
        onExport={onExport}
      />
    )
    
    // Find and click export button
    const exportButton = screen.getByTitle('导出')
    fireEvent.click(exportButton)
    
    expect(onExport).toHaveBeenCalled()
  })

  it('renders action buttons correctly', () => {
    const actions = [
      {
        key: 'edit',
        label: '编辑',
        onClick: jest.fn(),
      },
      {
        key: 'delete',
        label: '删除',
        onClick: jest.fn(),
        danger: true,
      },
    ]

    render(
      <DataTable 
        {...defaultProps} 
        actions={actions}
      />
    )
    
    // Check if action buttons are rendered for each row
    expect(screen.getAllByText('编辑')).toHaveLength(mockDataSource.length)
    expect(screen.getAllByText('删除')).toHaveLength(mockDataSource.length)
  })

  it('handles action button clicks', () => {
    const editAction = jest.fn()
    const deleteAction = jest.fn()

    const actions = [
      {
        key: 'edit',
        label: '编辑',
        onClick: editAction,
      },
      {
        key: 'delete',
        label: '删除',
        onClick: deleteAction,
        danger: true,
      },
    ]

    render(
      <DataTable 
        {...defaultProps} 
        actions={actions}
      />
    )
    
    // Click edit button for first row
    const editButtons = screen.getAllByText('编辑')
    fireEvent.click(editButtons[0])
    
    expect(editAction).toHaveBeenCalledWith(mockDataSource[0])
    
    // Click delete button for first row
    const deleteButtons = screen.getAllByText('删除')
    fireEvent.click(deleteButtons[0])
    
    expect(deleteAction).toHaveBeenCalledWith(mockDataSource[0])
  })

  it('applies responsive configuration', () => {
    render(
      <DataTable 
        {...defaultProps} 
        responsive={true}
      />
    )
    
    // Check if table has responsive classes
    const table = screen.getByRole('table')
    expect(table.closest('.ant-table-wrapper')).toHaveClass('ant-table-responsive')
  })

  it('handles error state', () => {
    const error = new Error('Failed to load data')

    render(
      <DataTable 
        {...defaultProps} 
        error={error}
      />
    )
    
    // Check if error message is displayed
    expect(screen.getByText('数据加载失败')).toBeInTheDocument()
    expect(screen.getByText('Failed to load data')).toBeInTheDocument()
  })
})
