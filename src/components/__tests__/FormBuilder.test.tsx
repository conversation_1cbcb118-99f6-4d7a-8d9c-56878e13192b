import React from 'react'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockData } from '@/utils/test-utils'
import FormBuilder from '@/components/common/FormBuilder'

describe('FormBuilder Component', () => {
  const mockFormConfig = [
    {
      name: 'username',
      label: '用户名',
      type: 'input',
      required: true,
      rules: [
        { required: true, message: '请输入用户名' },
        { min: 3, message: '用户名至少3个字符' },
      ],
    },
    {
      name: 'email',
      label: '邮箱',
      type: 'input',
      inputType: 'email',
      required: true,
      rules: [
        { required: true, message: '请输入邮箱' },
        { type: 'email', message: '请输入有效的邮箱地址' },
      ],
    },
    {
      name: 'role',
      label: '角色',
      type: 'select',
      required: true,
      options: [
        { label: '管理员', value: 'admin' },
        { label: '编辑者', value: 'editor' },
        { label: '查看者', value: 'viewer' },
      ],
      rules: [{ required: true, message: '请选择角色' }],
    },
    {
      name: 'status',
      label: '状态',
      type: 'radio',
      options: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' },
      ],
      defaultValue: 'active',
    },
    {
      name: 'description',
      label: '描述',
      type: 'textarea',
      rows: 4,
    },
  ]

  const defaultProps = {
    config: mockFormConfig,
    onSubmit: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders form fields correctly', () => {
    render(<FormBuilder {...defaultProps} />)
    
    // Check if all form fields are rendered
    expect(screen.getByLabelText('用户名')).toBeInTheDocument()
    expect(screen.getByLabelText('邮箱')).toBeInTheDocument()
    expect(screen.getByLabelText('角色')).toBeInTheDocument()
    expect(screen.getByLabelText('状态')).toBeInTheDocument()
    expect(screen.getByLabelText('描述')).toBeInTheDocument()
  })

  it('shows required field indicators', () => {
    render(<FormBuilder {...defaultProps} />)
    
    // Check if required fields have asterisk
    const usernameLabel = screen.getByText('用户名').closest('.ant-form-item-label')
    const emailLabel = screen.getByText('邮箱').closest('.ant-form-item-label')
    const roleLabel = screen.getByText('角色').closest('.ant-form-item-label')
    
    expect(usernameLabel).toHaveClass('ant-form-item-required')
    expect(emailLabel).toHaveClass('ant-form-item-required')
    expect(roleLabel).toHaveClass('ant-form-item-required')
  })

  it('handles form submission with valid data', async () => {
    const user = userEvent.setup()
    const onSubmit = jest.fn()

    render(<FormBuilder {...defaultProps} onSubmit={onSubmit} />)
    
    // Fill in form fields
    await user.type(screen.getByLabelText('用户名'), 'testuser')
    await user.type(screen.getByLabelText('邮箱'), '<EMAIL>')
    await user.click(screen.getByLabelText('角色'))
    await user.click(screen.getByText('管理员'))
    await user.type(screen.getByLabelText('描述'), 'Test description')
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /提交/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        username: 'testuser',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active', // default value
        description: 'Test description',
      })
    })
  })

  it('shows validation errors for required fields', async () => {
    const user = userEvent.setup()

    render(<FormBuilder {...defaultProps} />)
    
    // Try to submit without filling required fields
    const submitButton = screen.getByRole('button', { name: /提交/i })
    await user.click(submitButton)
    
    // Check if validation errors are shown
    await waitFor(() => {
      expect(screen.getByText('请输入用户名')).toBeInTheDocument()
      expect(screen.getByText('请输入邮箱')).toBeInTheDocument()
      expect(screen.getByText('请选择角色')).toBeInTheDocument()
    })
  })

  it('shows validation errors for invalid input', async () => {
    const user = userEvent.setup()

    render(<FormBuilder {...defaultProps} />)
    
    // Enter invalid data
    await user.type(screen.getByLabelText('用户名'), 'ab') // Too short
    await user.type(screen.getByLabelText('邮箱'), 'invalid-email') // Invalid email
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /提交/i })
    await user.click(submitButton)
    
    // Check if validation errors are shown
    await waitFor(() => {
      expect(screen.getByText('用户名至少3个字符')).toBeInTheDocument()
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
    })
  })

  it('populates form with initial values', () => {
    const initialValues = {
      username: 'existinguser',
      email: '<EMAIL>',
      role: 'editor',
      status: 'inactive',
      description: 'Existing description',
    }

    render(<FormBuilder {...defaultProps} initialValues={initialValues} />)
    
    // Check if form fields are populated
    expect(screen.getByDisplayValue('existinguser')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Existing description')).toBeInTheDocument()
  })

  it('handles form reset', async () => {
    const user = userEvent.setup()

    render(<FormBuilder {...defaultProps} showReset={true} />)
    
    // Fill in some fields
    await user.type(screen.getByLabelText('用户名'), 'testuser')
    await user.type(screen.getByLabelText('邮箱'), '<EMAIL>')
    
    // Reset form
    const resetButton = screen.getByRole('button', { name: /重置/i })
    await user.click(resetButton)
    
    // Check if form is reset
    expect(screen.getByLabelText('用户名')).toHaveValue('')
    expect(screen.getByLabelText('邮箱')).toHaveValue('')
  })

  it('handles loading state', () => {
    render(<FormBuilder {...defaultProps} loading={true} />)
    
    // Check if submit button is disabled and shows loading
    const submitButton = screen.getByRole('button', { name: /提交/i })
    expect(submitButton).toBeDisabled()
    expect(submitButton).toHaveClass('ant-btn-loading')
  })

  it('handles disabled state', () => {
    render(<FormBuilder {...defaultProps} disabled={true} />)
    
    // Check if form fields are disabled
    expect(screen.getByLabelText('用户名')).toBeDisabled()
    expect(screen.getByLabelText('邮箱')).toBeDisabled()
    expect(screen.getByLabelText('描述')).toBeDisabled()
  })

  it('renders different field types correctly', () => {
    const complexConfig = [
      {
        name: 'date',
        label: '日期',
        type: 'date',
      },
      {
        name: 'number',
        label: '数字',
        type: 'number',
      },
      {
        name: 'switch',
        label: '开关',
        type: 'switch',
      },
      {
        name: 'checkbox',
        label: '复选框',
        type: 'checkbox',
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' },
        ],
      },
    ]

    render(<FormBuilder config={complexConfig} onSubmit={jest.fn()} />)
    
    // Check if different field types are rendered
    expect(screen.getByLabelText('日期')).toBeInTheDocument()
    expect(screen.getByLabelText('数字')).toBeInTheDocument()
    expect(screen.getByLabelText('开关')).toBeInTheDocument()
    expect(screen.getByLabelText('选项1')).toBeInTheDocument()
    expect(screen.getByLabelText('选项2')).toBeInTheDocument()
  })

  it('handles conditional field visibility', async () => {
    const conditionalConfig = [
      {
        name: 'type',
        label: '类型',
        type: 'select',
        options: [
          { label: '用户', value: 'user' },
          { label: '管理员', value: 'admin' },
        ],
      },
      {
        name: 'permissions',
        label: '权限',
        type: 'checkbox',
        options: [
          { label: '读取', value: 'read' },
          { label: '写入', value: 'write' },
        ],
        dependencies: ['type'],
        visible: (values: any) => values.type === 'admin',
      },
    ]

    const user = userEvent.setup()

    render(<FormBuilder config={conditionalConfig} onSubmit={jest.fn()} />)
    
    // Initially permissions field should not be visible
    expect(screen.queryByLabelText('权限')).not.toBeInTheDocument()
    
    // Select admin type
    await user.click(screen.getByLabelText('类型'))
    await user.click(screen.getByText('管理员'))
    
    // Now permissions field should be visible
    await waitFor(() => {
      expect(screen.getByLabelText('读取')).toBeInTheDocument()
      expect(screen.getByLabelText('写入')).toBeInTheDocument()
    })
  })

  it('applies responsive layout', () => {
    render(<FormBuilder {...defaultProps} responsive={true} />)
    
    // Check if form has responsive classes
    const form = screen.getByRole('form')
    expect(form).toHaveClass('form-responsive')
  })
})
