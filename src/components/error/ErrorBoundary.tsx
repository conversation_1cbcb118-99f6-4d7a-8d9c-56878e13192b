'use client';

import React, { Component, ReactNode } from 'react';
import { Result, <PERSON>ton, Card, Typography, Space, Collapse } from 'antd';
import { 
  ExclamationCircleOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  BugOutlined,
} from '@ant-design/icons';
import { errorHandler, ErrorType, ErrorLevel } from '@/utils/errorHandler';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

// 错误边界状态接口
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
}

// 错误边界属性接口
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'section';
}

// 错误边界组件
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo });

    // 调用自定义错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 使用全局错误处理器
    errorHandler.handleError({
      type: ErrorType.CLIENT,
      level: ErrorLevel.HIGH,
      message: error.message,
      details: {
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name,
        level: this.props.level || 'component',
      },
      stack: error.stack,
      timestamp: Date.now(),
    });
  }

  // 重试处理
  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  // 返回首页
  handleGoHome = () => {
    window.location.href = '/main/dashboard';
  };

  // 刷新页面
  handleRefresh = () => {
    window.location.reload();
  };

  // 复制错误信息
  handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorText = `
错误ID: ${errorId}
错误消息: ${error?.message}
错误堆栈: ${error?.stack}
组件堆栈: ${errorInfo?.componentStack}
时间: ${new Date().toISOString()}
URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      // 这里可以显示复制成功的提示
    });
  };

  render() {
    const { hasError, error, errorInfo, errorId } = this.state;
    const { children, fallback, showDetails = false, level = 'component' } = this.props;

    if (hasError) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback;
      }

      // 根据级别显示不同的错误页面
      return this.renderErrorUI(error, errorInfo, errorId, level, showDetails);
    }

    return children;
  }

  private renderErrorUI(
    error: Error | null, 
    errorInfo: React.ErrorInfo | null, 
    errorId: string,
    level: string,
    showDetails: boolean
  ) {
    // 页面级错误
    if (level === 'page') {
      return (
        <div style={{ 
          minHeight: '100vh', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '20px',
        }}>
          <Result
            status="500"
            title="页面加载失败"
            subTitle="抱歉，页面遇到了一些问题。请尝试刷新页面或返回首页。"
            extra={
              <Space>
                <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleRefresh}>
                  刷新页面
                </Button>
                <Button icon={<HomeOutlined />} onClick={this.handleGoHome}>
                  返回首页
                </Button>
              </Space>
            }
          />
        </div>
      );
    }

    // 组件级错误
    if (level === 'component') {
      return (
        <Card
          style={{ margin: '16px 0' }}
          bodyStyle={{ textAlign: 'center', padding: '40px 20px' }}
        >
          <ExclamationCircleOutlined 
            style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: '16px' }} 
          />
          <Typography.Title level={4} style={{ marginBottom: '8px' }}>
            组件加载失败
          </Typography.Title>
          <Paragraph type="secondary" style={{ marginBottom: '24px' }}>
            该组件遇到了一些问题，请尝试重新加载
          </Paragraph>
          <Space>
            <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleRetry}>
              重新加载
            </Button>
            {showDetails && (
              <Button icon={<BugOutlined />} onClick={this.handleCopyError}>
                复制错误信息
              </Button>
            )}
          </Space>

          {showDetails && error && (
            <Collapse style={{ marginTop: '24px', textAlign: 'left' }}>
              <Panel header="错误详情" key="error-details">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>错误ID:</Text>
                    <Text code style={{ marginLeft: '8px' }}>{errorId}</Text>
                  </div>
                  <div>
                    <Text strong>错误消息:</Text>
                    <Paragraph code style={{ marginTop: '4px' }}>
                      {error.message}
                    </Paragraph>
                  </div>
                  {error.stack && (
                    <div>
                      <Text strong>错误堆栈:</Text>
                      <Paragraph code style={{ marginTop: '4px', fontSize: '12px' }}>
                        {error.stack}
                      </Paragraph>
                    </div>
                  )}
                  {errorInfo?.componentStack && (
                    <div>
                      <Text strong>组件堆栈:</Text>
                      <Paragraph code style={{ marginTop: '4px', fontSize: '12px' }}>
                        {errorInfo.componentStack}
                      </Paragraph>
                    </div>
                  )}
                </Space>
              </Panel>
            </Collapse>
          )}
        </Card>
      );
    }

    // 区域级错误
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center', 
        border: '1px dashed #ff4d4f',
        borderRadius: '6px',
        backgroundColor: '#fff2f0',
      }}>
        <ExclamationCircleOutlined 
          style={{ fontSize: '24px', color: '#ff4d4f', marginBottom: '8px' }} 
        />
        <div style={{ marginBottom: '12px' }}>
          <Text type="danger">该区域加载失败</Text>
        </div>
        <Button size="small" icon={<ReloadOutlined />} onClick={this.handleRetry}>
          重试
        </Button>
      </div>
    );
  }
}

// 高阶组件：为组件添加错误边界
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WithErrorBoundaryComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = 
    `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundaryComponent;
}

// Hook：在函数组件中使用错误边界
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
    errorHandler.handleError({
      type: ErrorType.CLIENT,
      level: ErrorLevel.MEDIUM,
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
    });
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
}

// 异步错误边界Hook
export function useAsyncError() {
  const [, setError] = React.useState();

  return React.useCallback((error: Error) => {
    setError(() => {
      throw error;
    });
  }, []);
}

export default ErrorBoundary;
