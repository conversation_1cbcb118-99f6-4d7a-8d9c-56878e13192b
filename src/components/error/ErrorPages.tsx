'use client';

import React from 'react';
import { Result, Button, Space, Typography, Card } from 'antd';
import { 
  HomeOutlined, 
  ReloadOutlined, 
  ArrowLeftOutlined,
  ExclamationCircleOutlined,
  DisconnectOutlined,
  LockOutlined,
  FileSearchOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Paragraph } = Typography;

// 通用错误页面属性
interface ErrorPageProps {
  title?: string;
  subTitle?: string;
  extra?: React.ReactNode;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  showRefreshButton?: boolean;
}

// 404 页面
export const NotFoundPage: React.FC<ErrorPageProps> = ({
  title = "页面不存在",
  subTitle = "抱歉，您访问的页面不存在。",
  showBackButton = true,
  showHomeButton = true,
  extra,
}) => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  const handleHome = () => {
    router.push('/main/dashboard');
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
    }}>
      <Result
        status="404"
        title={title}
        subTitle={subTitle}
        extra={
          extra || (
            <Space>
              {showBackButton && (
                <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                  返回上页
                </Button>
              )}
              {showHomeButton && (
                <Button type="primary" icon={<HomeOutlined />} onClick={handleHome}>
                  返回首页
                </Button>
              )}
            </Space>
          )
        }
      />
    </div>
  );
};

// 403 权限不足页面
export const ForbiddenPage: React.FC<ErrorPageProps> = ({
  title = "访问被拒绝",
  subTitle = "抱歉，您没有权限访问此页面。",
  showBackButton = true,
  showHomeButton = true,
  extra,
}) => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  const handleHome = () => {
    router.push('/main/dashboard');
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
    }}>
      <Result
        status="403"
        title={title}
        subTitle={subTitle}
        extra={
          extra || (
            <Space>
              {showBackButton && (
                <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                  返回上页
                </Button>
              )}
              {showHomeButton && (
                <Button type="primary" icon={<HomeOutlined />} onClick={handleHome}>
                  返回首页
                </Button>
              )}
            </Space>
          )
        }
      />
    </div>
  );
};

// 500 服务器错误页面
export const ServerErrorPage: React.FC<ErrorPageProps> = ({
  title = "服务器错误",
  subTitle = "抱歉，服务器遇到了一些问题。请稍后重试。",
  showRefreshButton = true,
  showHomeButton = true,
  extra,
}) => {
  const router = useRouter();

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleHome = () => {
    router.push('/main/dashboard');
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
    }}>
      <Result
        status="500"
        title={title}
        subTitle={subTitle}
        extra={
          extra || (
            <Space>
              {showRefreshButton && (
                <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh}>
                  刷新页面
                </Button>
              )}
              {showHomeButton && (
                <Button icon={<HomeOutlined />} onClick={handleHome}>
                  返回首页
                </Button>
              )}
            </Space>
          )
        }
      />
    </div>
  );
};

// 网络错误页面
export const NetworkErrorPage: React.FC<ErrorPageProps> = ({
  title = "网络连接失败",
  subTitle = "请检查您的网络连接，然后重试。",
  showRefreshButton = true,
  showHomeButton = true,
  extra,
}) => {
  const router = useRouter();

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleHome = () => {
    router.push('/main/dashboard');
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
    }}>
      <Result
        icon={<DisconnectOutlined />}
        title={title}
        subTitle={subTitle}
        extra={
          extra || (
            <Space>
              {showRefreshButton && (
                <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh}>
                  重新连接
                </Button>
              )}
              {showHomeButton && (
                <Button icon={<HomeOutlined />} onClick={handleHome}>
                  返回首页
                </Button>
              )}
            </Space>
          )
        }
      />
    </div>
  );
};

// 维护页面
export const MaintenancePage: React.FC<{
  title?: string;
  description?: string;
  estimatedTime?: string;
}> = ({
  title = "系统维护中",
  description = "系统正在进行维护升级，暂时无法访问。",
  estimatedTime,
}) => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
      backgroundColor: '#f5f5f5',
    }}>
      <Card style={{ maxWidth: '500px', textAlign: 'center' }}>
        <WarningOutlined style={{ fontSize: '64px', color: '#faad14', marginBottom: '24px' }} />
        <Title level={2} style={{ marginBottom: '16px' }}>
          {title}
        </Title>
        <Paragraph style={{ fontSize: '16px', marginBottom: '24px' }}>
          {description}
        </Paragraph>
        {estimatedTime && (
          <Paragraph type="secondary">
            预计恢复时间：{estimatedTime}
          </Paragraph>
        )}
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={() => window.location.reload()}
        >
          刷新页面
        </Button>
      </Card>
    </div>
  );
};

// 加载失败页面
export const LoadingErrorPage: React.FC<{
  onRetry?: () => void;
  message?: string;
}> = ({
  onRetry,
  message = "页面加载失败，请重试",
}) => {
  return (
    <div style={{ 
      minHeight: '200px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '40px',
    }}>
      <div style={{ textAlign: 'center' }}>
        <ExclamationCircleOutlined 
          style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: '16px' }} 
        />
        <Paragraph style={{ marginBottom: '24px' }}>
          {message}
        </Paragraph>
        {onRetry && (
          <Button type="primary" icon={<ReloadOutlined />} onClick={onRetry}>
            重试
          </Button>
        )}
      </div>
    </div>
  );
};

// 空数据页面
export const EmptyDataPage: React.FC<{
  title?: string;
  description?: string;
  action?: React.ReactNode;
}> = ({
  title = "暂无数据",
  description = "当前没有可显示的数据",
  action,
}) => {
  return (
    <div style={{ 
      minHeight: '300px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '40px',
    }}>
      <Result
        icon={<FileSearchOutlined />}
        title={title}
        subTitle={description}
        extra={action}
      />
    </div>
  );
};

// 通用错误页面
export const GenericErrorPage: React.FC<{
  status?: 'error' | 'warning' | 'info';
  title?: string;
  description?: string;
  actions?: React.ReactNode;
}> = ({
  status = 'error',
  title = "出现错误",
  description = "系统遇到了一些问题，请稍后重试。",
  actions,
}) => {
  const router = useRouter();

  const defaultActions = (
    <Space>
      <Button icon={<ArrowLeftOutlined />} onClick={() => router.back()}>
        返回上页
      </Button>
      <Button type="primary" icon={<HomeOutlined />} onClick={() => router.push('/main/dashboard')}>
        返回首页
      </Button>
    </Space>
  );

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
    }}>
      <Result
        status={status}
        title={title}
        subTitle={description}
        extra={actions || defaultActions}
      />
    </div>
  );
};

// 导出所有错误页面组件
export {
  NotFoundPage as Error404,
  ForbiddenPage as Error403,
  ServerErrorPage as Error500,
  NetworkErrorPage as NetworkError,
  MaintenancePage as Maintenance,
  LoadingErrorPage as LoadingError,
  EmptyDataPage as EmptyData,
  GenericErrorPage as GenericError,
};
