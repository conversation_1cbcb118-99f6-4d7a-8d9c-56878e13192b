import React from 'react'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockApi } from '@/utils/test-utils'
import LoginPage from '@/app/login/page'
import { http } from '@/lib/request'

// Mock the HTTP request library
jest.mock('@/lib/request', () => ({
  http: {
    post: jest.fn(),
  },
}))

// Mock Next.js navigation
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('Authentication Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorage.clear()
  })

  describe('Login Flow', () => {
    it('should complete successful login flow', async () => {
      const user = userEvent.setup()

      // Mock successful login API response
      const mockLoginResponse = mockApi.success({
        token: 'mock-jwt-token',
        user: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'admin',
          avatar: '/avatar.jpg',
        },
      })

      ;(http.post as jest.Mock).mockResolvedValue(mockLoginResponse)

      render(<LoginPage />)

      // Fill in login form
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })

      await user.type(usernameInput, 'testuser')
      await user.type(passwordInput, 'password123')
      await user.click(loginButton)

      // Verify API call
      await waitFor(() => {
        expect(http.post).toHaveBeenCalledWith('/api/auth/login', {
          username: 'testuser',
          password: 'password123',
        })
      })

      // Verify token storage
      await waitFor(() => {
        expect(localStorage.getItem('token')).toBe('mock-jwt-token')
        expect(localStorage.getItem('userInfo')).toBe(
          JSON.stringify(mockLoginResponse.data.user)
        )
      })

      // Verify navigation to dashboard
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/main/dashboard')
      })
    })

    it('should handle login failure', async () => {
      const user = userEvent.setup()

      // Mock failed login API response
      const mockErrorResponse = mockApi.error('Invalid credentials', 401)
      ;(http.post as jest.Mock).mockRejectedValue({
        response: {
          status: 401,
          data: mockErrorResponse,
        },
      })

      render(<LoginPage />)

      // Fill in login form with invalid credentials
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })

      await user.type(usernameInput, 'wronguser')
      await user.type(passwordInput, 'wrongpassword')
      await user.click(loginButton)

      // Verify error message is displayed
      await waitFor(() => {
        expect(screen.getByText(/登录失败/i)).toBeInTheDocument()
      })

      // Verify no token is stored
      expect(localStorage.getItem('token')).toBe(null)
      expect(localStorage.getItem('userInfo')).toBe(null)

      // Verify no navigation occurs
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()

      render(<LoginPage />)

      // Try to submit without filling fields
      const loginButton = screen.getByRole('button', { name: /登录/i })
      await user.click(loginButton)

      // Verify validation errors
      await waitFor(() => {
        expect(screen.getByText(/请输入用户名/i)).toBeInTheDocument()
        expect(screen.getByText(/请输入密码/i)).toBeInTheDocument()
      })

      // Verify no API call is made
      expect(http.post).not.toHaveBeenCalled()
    })

    it('should handle network error', async () => {
      const user = userEvent.setup()

      // Mock network error
      ;(http.post as jest.Mock).mockRejectedValue(new Error('Network Error'))

      render(<LoginPage />)

      // Fill in login form
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })

      await user.type(usernameInput, 'testuser')
      await user.type(passwordInput, 'password123')
      await user.click(loginButton)

      // Verify network error message
      await waitFor(() => {
        expect(screen.getByText(/网络连接失败/i)).toBeInTheDocument()
      })
    })

    it('should show loading state during login', async () => {
      const user = userEvent.setup()

      // Mock delayed API response
      ;(http.post as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockApi.success({})), 1000))
      )

      render(<LoginPage />)

      // Fill in login form
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })

      await user.type(usernameInput, 'testuser')
      await user.type(passwordInput, 'password123')
      await user.click(loginButton)

      // Verify loading state
      expect(loginButton).toBeDisabled()
      expect(loginButton).toHaveClass('ant-btn-loading')
    })
  })

  describe('Remember Me Functionality', () => {
    it('should remember user when checkbox is checked', async () => {
      const user = userEvent.setup()

      const mockLoginResponse = mockApi.success({
        token: 'mock-jwt-token',
        user: { id: '1', username: 'testuser' },
      })

      ;(http.post as jest.Mock).mockResolvedValue(mockLoginResponse)

      render(<LoginPage />)

      // Fill in form and check remember me
      await user.type(screen.getByLabelText(/用户名/i), 'testuser')
      await user.type(screen.getByLabelText(/密码/i), 'password123')
      await user.click(screen.getByLabelText(/记住我/i))
      await user.click(screen.getByRole('button', { name: /登录/i }))

      // Verify remember me data is stored
      await waitFor(() => {
        expect(localStorage.getItem('rememberMe')).toBe('true')
        expect(localStorage.getItem('rememberedUsername')).toBe('testuser')
      })
    })

    it('should not remember user when checkbox is unchecked', async () => {
      const user = userEvent.setup()

      const mockLoginResponse = mockApi.success({
        token: 'mock-jwt-token',
        user: { id: '1', username: 'testuser' },
      })

      ;(http.post as jest.Mock).mockResolvedValue(mockLoginResponse)

      render(<LoginPage />)

      // Fill in form without checking remember me
      await user.type(screen.getByLabelText(/用户名/i), 'testuser')
      await user.type(screen.getByLabelText(/密码/i), 'password123')
      await user.click(screen.getByRole('button', { name: /登录/i }))

      // Verify remember me data is not stored
      await waitFor(() => {
        expect(localStorage.getItem('rememberMe')).toBe(null)
        expect(localStorage.getItem('rememberedUsername')).toBe(null)
      })
    })
  })

  describe('Auto-login on Page Load', () => {
    it('should auto-login with valid stored token', async () => {
      // Set up stored token and user info
      localStorage.setItem('token', 'valid-token')
      localStorage.setItem('userInfo', JSON.stringify({
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
      }))

      // Mock token validation API
      ;(http.post as jest.Mock).mockResolvedValue(mockApi.success({ valid: true }))

      render(<LoginPage />)

      // Should redirect to dashboard automatically
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/main/dashboard')
      })
    })

    it('should clear invalid stored token', async () => {
      // Set up invalid stored token
      localStorage.setItem('token', 'invalid-token')
      localStorage.setItem('userInfo', JSON.stringify({ id: '1' }))

      // Mock token validation failure
      ;(http.post as jest.Mock).mockRejectedValue({
        response: { status: 401 }
      })

      render(<LoginPage />)

      // Should clear stored data and show login form
      await waitFor(() => {
        expect(localStorage.getItem('token')).toBe(null)
        expect(localStorage.getItem('userInfo')).toBe(null)
      })

      // Should show login form
      expect(screen.getByLabelText(/用户名/i)).toBeInTheDocument()
    })
  })

  describe('Form Validation', () => {
    it('should validate username format', async () => {
      const user = userEvent.setup()

      render(<LoginPage />)

      // Enter invalid username (too short)
      const usernameInput = screen.getByLabelText(/用户名/i)
      await user.type(usernameInput, 'ab')
      await user.tab() // Trigger blur event

      // Verify validation error
      await waitFor(() => {
        expect(screen.getByText(/用户名至少3个字符/i)).toBeInTheDocument()
      })
    })

    it('should validate password format', async () => {
      const user = userEvent.setup()

      render(<LoginPage />)

      // Enter invalid password (too short)
      const passwordInput = screen.getByLabelText(/密码/i)
      await user.type(passwordInput, '123')
      await user.tab() // Trigger blur event

      // Verify validation error
      await waitFor(() => {
        expect(screen.getByText(/密码至少6个字符/i)).toBeInTheDocument()
      })
    })
  })
})
