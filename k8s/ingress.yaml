# Ingress 配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mp-react-nextjs-ingress
  namespace: default
  labels:
    app: mp-react-nextjs
  annotations:
    # Nginx Ingress Controller 配置
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # 证书管理
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
    # 限流配置
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/rate-limit-connections: "10"
    
    # 特殊路径限流
    nginx.ingress.kubernetes.io/server-snippet: |
      location /api/auth/login {
        limit_req zone=login burst=3 nodelay;
        limit_req_status 429;
      }
    
    # 安全头
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options DENY;
      add_header X-Content-Type-Options nosniff;
      add_header X-XSS-Protection "1; mode=block";
      add_header Referrer-Policy "strict-origin-when-cross-origin";
      add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 客户端最大请求体大小
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    
    # 超时设置
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    
    # 缓存配置
    nginx.ingress.kubernetes.io/server-snippet: |
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
      }
    
    # 压缩
    nginx.ingress.kubernetes.io/enable-gzip: "true"
    nginx.ingress.kubernetes.io/gzip-types: "text/plain,text/css,application/json,application/javascript,text/xml,application/xml,application/xml+rss,text/javascript"

spec:
  tls:
  - hosts:
    - your-domain.com
    - www.your-domain.com
    secretName: mp-react-nextjs-tls
  
  rules:
  # 主域名
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mp-react-nextjs-service
            port:
              number: 80
  
  # www 子域名
  - host: www.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mp-react-nextjs-service
            port:
              number: 80

---
# 开发环境 Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mp-react-nextjs-dev-ingress
  namespace: development
  labels:
    app: mp-react-nextjs
    environment: development
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
    
    # 开发环境认证
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: dev-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - Development Environment'

spec:
  tls:
  - hosts:
    - dev.your-domain.com
    secretName: mp-react-nextjs-dev-tls
  
  rules:
  - host: dev.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mp-react-nextjs-service
            port:
              number: 80

---
# 测试环境 Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mp-react-nextjs-staging-ingress
  namespace: staging
  labels:
    app: mp-react-nextjs
    environment: staging
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
    
    # 白名单IP访问
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"

spec:
  tls:
  - hosts:
    - staging.your-domain.com
    secretName: mp-react-nextjs-staging-tls
  
  rules:
  - host: staging.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mp-react-nextjs-service
            port:
              number: 80
