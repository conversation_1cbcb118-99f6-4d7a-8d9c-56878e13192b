# ConfigMap 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: default
  labels:
    app: mp-react-nextjs
data:
  # 应用配置
  nextauth-url: "https://your-domain.com"
  api-base-url: "https://api.your-domain.com"
  
  # 功能开关
  feature-flags: |
    {
      "enableAnalytics": true,
      "enablePWA": true,
      "enableI18n": true,
      "enableThemeCustomization": true,
      "enableFileUpload": true,
      "enableRealTimeNotifications": true
    }
  
  # 缓存配置
  cache-config: |
    {
      "redis": {
        "ttl": 3600,
        "maxRetries": 3
      },
      "memory": {
        "maxSize": "100mb",
        "ttl": 300
      }
    }
  
  # 日志配置
  log-config: |
    {
      "level": "info",
      "format": "json",
      "enableConsole": true,
      "enableFile": false
    }
  
  # 监控配置
  monitoring-config: |
    {
      "metrics": {
        "enabled": true,
        "interval": 30
      },
      "tracing": {
        "enabled": true,
        "sampleRate": 0.1
      }
    }

---
# Secret 配置 (需要手动创建或使用外部密钥管理)
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: default
  labels:
    app: mp-react-nextjs
type: Opaque
stringData:
  # 数据库连接
  database-url: "****************************************************/mp_react_db"
  
  # Redis 连接
  redis-url: "redis://redis-service:6379"
  
  # JWT 密钥
  jwt-secret: "your-super-secret-jwt-key-change-in-production"
  
  # NextAuth 密钥
  nextauth-secret: "your-nextauth-secret-change-in-production"
  
  # 第三方服务密钥
  github-client-id: "your-github-client-id"
  github-client-secret: "your-github-client-secret"
  
  google-client-id: "your-google-client-id"
  google-client-secret: "your-google-client-secret"
  
  # 邮件服务配置
  smtp-host: "smtp.gmail.com"
  smtp-port: "587"
  smtp-user: "<EMAIL>"
  smtp-password: "your-app-password"
  
  # 对象存储配置
  s3-access-key: "your-s3-access-key"
  s3-secret-key: "your-s3-secret-key"
  s3-bucket: "your-s3-bucket"
  s3-region: "us-east-1"
  
  # 监控服务
  sentry-dsn: "your-sentry-dsn"
  datadog-api-key: "your-datadog-api-key"

---
# Persistent Volume Claim for uploads
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: default
  labels:
    app: mp-react-nextjs
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
