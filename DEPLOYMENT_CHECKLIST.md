# 🚀 生产环境部署清单

## 📋 部署前准备

### 1. 环境配置
- [ ] 复制 `.env.production` 并修改为实际配置
- [ ] 设置数据库连接字符串 `DATABASE_URL`
- [ ] 设置 Redis 连接字符串 `REDIS_URL`
- [ ] 生成强密钥 `JWT_SECRET` 和 `NEXTAUTH_SECRET`
- [ ] 配置域名 `NEXTAUTH_URL` 和 `NEXT_PUBLIC_APP_URL`
- [ ] 配置文件上传目录 `UPLOAD_DIR`
- [ ] 配置邮件服务（如需要）
- [ ] 配置第三方服务密钥（OAuth、监控等）

### 2. 安全配置
- [ ] 更新所有默认密码和密钥
- [ ] 配置 HTTPS 证书
- [ ] 设置防火墙规则
- [ ] 配置 CORS 策略
- [ ] 启用安全头部
- [ ] 配置 CSP（内容安全策略）

### 3. 基础设施准备
- [ ] 准备服务器或云服务
- [ ] 安装 Docker 和 Docker Compose
- [ ] 准备数据库服务器（PostgreSQL）
- [ ] 准备缓存服务器（Redis）
- [ ] 配置负载均衡器（如需要）
- [ ] 配置 CDN（如需要）

### 4. 域名和 SSL
- [ ] 购买并配置域名
- [ ] 申请 SSL 证书
- [ ] 配置 DNS 记录
- [ ] 设置域名解析

## 🔧 部署步骤

### 方式一：Docker Compose 部署（推荐用于单机部署）

1. **克隆代码**
   ```bash
   git clone <your-repo-url>
   cd mp-react-nextjs
   ```

2. **配置环境变量**
   ```bash
   cp .env.production .env.local
   # 编辑 .env.local 文件，填入实际配置
   ```

3. **构建和启动服务**
   ```bash
   # 构建镜像
   docker-compose build
   
   # 启动服务
   docker-compose up -d
   ```

4. **验证部署**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 查看日志
   docker-compose logs -f app
   
   # 健康检查
   curl http://localhost:3000/api/health
   ```

### 方式二：Kubernetes 部署（推荐用于集群部署）

1. **准备 Kubernetes 集群**
   ```bash
   # 确保 kubectl 已配置
   kubectl cluster-info
   ```

2. **构建和推送镜像**
   ```bash
   # 构建镜像
   docker build -t your-registry/mp-react-nextjs:v1.0.0 .
   
   # 推送到镜像仓库
   docker push your-registry/mp-react-nextjs:v1.0.0
   ```

3. **部署到 Kubernetes**
   ```bash
   # 创建命名空间
   kubectl create namespace production
   
   # 应用配置
   kubectl apply -f k8s/ -n production
   ```

4. **验证部署**
   ```bash
   # 检查 Pod 状态
   kubectl get pods -n production
   
   # 查看服务
   kubectl get services -n production
   
   # 查看日志
   kubectl logs -f deployment/mp-react-nextjs-app -n production
   ```

### 方式三：使用部署脚本

```bash
# 给脚本执行权限
chmod +x scripts/deploy.sh

# 部署到生产环境
./scripts/deploy.sh production v1.0.0
```

## ✅ 部署后验证

### 1. 功能测试
- [ ] 访问主页：`https://your-domain.com`
- [ ] 登录功能正常
- [ ] 仪表盘数据显示正常
- [ ] 文件上传功能正常
- [ ] 主题切换功能正常
- [ ] 响应式设计在各设备正常

### 2. 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] API 响应时间 < 500ms
- [ ] 图表渲染正常
- [ ] 内存使用正常
- [ ] CPU 使用正常

### 3. 安全测试
- [ ] HTTPS 正常工作
- [ ] 安全头部已设置
- [ ] 无敏感信息泄露
- [ ] 认证和授权正常
- [ ] 文件上传安全检查

### 4. 监控和日志
- [ ] 健康检查接口：`/api/health`
- [ ] 应用日志正常输出
- [ ] 错误监控配置（如 Sentry）
- [ ] 性能监控配置
- [ ] 备份策略配置

## 🔄 维护和更新

### 日常维护
- [ ] 定期检查服务状态
- [ ] 监控资源使用情况
- [ ] 查看错误日志
- [ ] 数据库备份
- [ ] 安全更新

### 版本更新
```bash
# 1. 构建新版本
docker build -t your-registry/mp-react-nextjs:v1.1.0 .

# 2. 推送镜像
docker push your-registry/mp-react-nextjs:v1.1.0

# 3. 更新部署
./scripts/deploy.sh production v1.1.0

# 4. 验证更新
curl https://your-domain.com/api/health
```

### 回滚操作
```bash
# Docker Compose 回滚
docker-compose down
docker-compose up -d

# Kubernetes 回滚
kubectl rollout undo deployment/mp-react-nextjs-app -n production
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   - 检查环境变量配置
   - 查看容器日志
   - 验证端口占用情况

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 检查网络连通性

3. **静态资源加载失败**
   - 检查 CDN 配置
   - 验证文件权限
   - 查看 Nginx 配置

4. **性能问题**
   - 检查内存使用
   - 分析慢查询
   - 优化图片和静态资源

### 紧急联系方式
- 开发团队：<EMAIL>
- 运维团队：<EMAIL>
- 紧急电话：+86-xxx-xxxx-xxxx

## 📚 相关文档
- [架构文档](./docs/ARCHITECTURE.md)
- [API 文档](./docs/API.md)
- [开发指南](./docs/DEVELOPMENT.md)
- [测试指南](./docs/TESTING.md)
