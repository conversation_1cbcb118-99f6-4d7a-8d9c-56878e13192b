# 组件文档

本文档详细介绍了项目中的所有可复用组件。

## 📋 目录

- [组件概览](#组件概览)
- [布局组件](#布局组件)
- [表单组件](#表单组件)
- [数据展示](#数据展示)
- [反馈组件](#反馈组件)
- [导航组件](#导航组件)
- [工具组件](#工具组件)

## 🏗️ 组件概览

### 组件分类

```
components/
├── layout/           # 布局组件
├── form/            # 表单组件
├── table/           # 表格组件
├── charts/          # 图表组件
├── upload/          # 上传组件
├── theme/           # 主题组件
├── auth/            # 认证组件
├── error/           # 错误组件
├── lazy/            # 懒加载组件
├── pwa/             # PWA组件
├── i18n/            # 国际化组件
└── editors/         # 编辑器组件
```

### 设计原则

- **可复用性**: 组件设计考虑多场景复用
- **可配置性**: 提供丰富的配置选项
- **类型安全**: 完整的 TypeScript 类型定义
- **性能优化**: 合理使用 memo 和 lazy loading
- **无障碍性**: 支持键盘导航和屏幕阅读器

## 🏗️ 布局组件

### MainLayout

主布局组件，提供应用的整体布局结构。

**Props**:
```typescript
interface MainLayoutProps {
  children: React.ReactNode
  showSidebar?: boolean
  showHeader?: boolean
  showFooter?: boolean
  sidebarCollapsed?: boolean
  onSidebarToggle?: () => void
}
```

**使用示例**:
```tsx
import MainLayout from '@/components/layout/MainLayout'

function App() {
  return (
    <MainLayout showSidebar={true} showHeader={true}>
      <div>页面内容</div>
    </MainLayout>
  )
}
```

**特性**:
- 响应式设计，自适应移动端
- 支持侧边栏折叠/展开
- 可配置显示/隐藏各个区域
- 内置面包屑导航
- 支持主题切换

### ResponsiveLayout

响应式布局组件，根据屏幕尺寸自动调整布局。

**Props**:
```typescript
interface ResponsiveLayoutProps {
  children: React.ReactNode
  breakpoints?: {
    xs: number
    sm: number
    md: number
    lg: number
    xl: number
  }
  gutter?: number | [number, number]
}
```

## 📝 表单组件

### FormBuilder

动态表单构建器，根据配置生成表单。

**Props**:
```typescript
interface FormBuilderProps {
  config: FormFieldConfig[]
  initialValues?: Record<string, any>
  onSubmit: (values: Record<string, any>) => void
  loading?: boolean
  disabled?: boolean
  showReset?: boolean
  responsive?: boolean
}

interface FormFieldConfig {
  name: string
  label: string
  type: 'input' | 'select' | 'textarea' | 'date' | 'number' | 'switch' | 'radio' | 'checkbox'
  required?: boolean
  rules?: Rule[]
  options?: { label: string; value: any }[]
  placeholder?: string
  disabled?: boolean
  dependencies?: string[]
  visible?: (values: Record<string, any>) => boolean
}
```

**使用示例**:
```tsx
import FormBuilder from '@/components/form/FormBuilder'

const formConfig = [
  {
    name: 'username',
    label: '用户名',
    type: 'input',
    required: true,
    rules: [
      { required: true, message: '请输入用户名' },
      { min: 3, message: '用户名至少3个字符' }
    ]
  },
  {
    name: 'role',
    label: '角色',
    type: 'select',
    options: [
      { label: '管理员', value: 'admin' },
      { label: '用户', value: 'user' }
    ]
  }
]

function UserForm() {
  const handleSubmit = (values) => {
    console.log('表单数据:', values)
  }

  return (
    <FormBuilder
      config={formConfig}
      onSubmit={handleSubmit}
      responsive={true}
    />
  )
}
```

### BaseForm

基础表单组件，提供表单的基本功能。

**特性**:
- 自动表单验证
- 支持异步提交
- 错误状态处理
- 加载状态显示
- 重置功能

## 📊 数据展示

### DataTable

高级数据表格组件，支持分页、搜索、排序等功能。

**Props**:
```typescript
interface DataTableProps<T = any> {
  columns: ColumnType<T>[]
  dataSource: T[]
  loading?: boolean
  pagination?: PaginationConfig
  searchable?: boolean
  selectable?: boolean
  exportable?: boolean
  refreshable?: boolean
  actions?: ActionConfig[]
  onPaginationChange?: (page: number, pageSize: number) => void
  onSearch?: (keyword: string) => void
  onSelectionChange?: (selectedKeys: string[], selectedRows: T[]) => void
  onRefresh?: () => void
  onExport?: () => void
  responsive?: boolean
  error?: Error
}

interface ActionConfig {
  key: string
  label: string
  icon?: React.ReactNode
  onClick: (record: any) => void
  danger?: boolean
  disabled?: (record: any) => boolean
}
```

**使用示例**:
```tsx
import DataTable from '@/components/table/DataTable'

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    sorter: true,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  }
]

const actions = [
  {
    key: 'edit',
    label: '编辑',
    onClick: (record) => console.log('编辑', record)
  },
  {
    key: 'delete',
    label: '删除',
    danger: true,
    onClick: (record) => console.log('删除', record)
  }
]

function UserTable() {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(false)

  return (
    <DataTable
      columns={columns}
      dataSource={data}
      loading={loading}
      searchable={true}
      selectable={true}
      actions={actions}
      responsive={true}
    />
  )
}
```

### ChartComponents

图表组件集合，基于 ECharts 和 Ant Design Charts。

**可用图表**:
- `LineChart`: 折线图
- `BarChart`: 柱状图
- `PieChart`: 饼图
- `AreaChart`: 面积图
- `ScatterChart`: 散点图

**使用示例**:
```tsx
import { LineChart, PieChart } from '@/components/charts/ChartComponents'

function Dashboard() {
  const lineData = [
    { date: '2023-01', value: 100 },
    { date: '2023-02', value: 120 },
    { date: '2023-03', value: 150 }
  ]

  const pieData = [
    { type: '类型A', value: 40 },
    { type: '类型B', value: 30 },
    { type: '类型C', value: 30 }
  ]

  return (
    <div>
      <LineChart
        data={lineData}
        xField="date"
        yField="value"
        title="趋势图"
      />
      <PieChart
        data={pieData}
        angleField="value"
        colorField="type"
        title="分布图"
      />
    </div>
  )
}
```

## 📁 文件上传

### FileUpload

文件上传组件，支持拖拽上传、预览、进度显示。

**Props**:
```typescript
interface FileUploadProps {
  accept?: string
  multiple?: boolean
  maxSize?: number
  maxCount?: number
  showPreview?: boolean
  showProgress?: boolean
  onUpload: (file: File) => Promise<UploadResult>
  onRemove?: (file: UploadFile) => void
  onChange?: (fileList: UploadFile[]) => void
  disabled?: boolean
  listType?: 'text' | 'picture' | 'picture-card'
}
```

**使用示例**:
```tsx
import FileUpload from '@/components/upload/FileUpload'

function ImageUpload() {
  const handleUpload = async (file) => {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })
    
    return response.json()
  }

  return (
    <FileUpload
      accept="image/*"
      maxSize={5 * 1024 * 1024} // 5MB
      maxCount={5}
      showPreview={true}
      listType="picture-card"
      onUpload={handleUpload}
    />
  )
}
```

## 🎨 主题组件

### ThemeProvider

主题提供者组件，管理应用主题。

**Props**:
```typescript
interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: 'light' | 'dark'
  primaryColor?: string
  customTheme?: Partial<ThemeConfig>
}
```

### ThemeToggle

主题切换组件。

**使用示例**:
```tsx
import { ThemeProvider, ThemeToggle } from '@/components/theme'

function App() {
  return (
    <ThemeProvider defaultTheme="light" primaryColor="#1890ff">
      <div>
        <ThemeToggle />
        {/* 应用内容 */}
      </div>
    </ThemeProvider>
  )
}
```

## 🚨 错误组件

### ErrorBoundary

错误边界组件，捕获和处理组件错误。

**Props**:
```typescript
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  level?: 'page' | 'component' | 'section'
  showDetails?: boolean
}
```

**使用示例**:
```tsx
import ErrorBoundary from '@/components/error/ErrorBoundary'

function App() {
  return (
    <ErrorBoundary level="page" showDetails={process.env.NODE_ENV === 'development'}>
      <MyComponent />
    </ErrorBoundary>
  )
}
```

### ErrorPages

错误页面组件集合。

**可用页面**:
- `NotFoundPage`: 404 页面
- `ForbiddenPage`: 403 页面
- `ServerErrorPage`: 500 页面
- `NetworkErrorPage`: 网络错误页面

## 🔐 认证组件

### AuthGuard

认证守卫组件，保护需要登录的页面。

**Props**:
```typescript
interface AuthGuardProps {
  children: React.ReactNode
  requiredPermissions?: string[]
  fallback?: React.ReactNode
  redirectTo?: string
}
```

**使用示例**:
```tsx
import AuthGuard from '@/components/auth/AuthGuard'

function AdminPage() {
  return (
    <AuthGuard requiredPermissions={['admin']}>
      <div>管理员页面内容</div>
    </AuthGuard>
  )
}
```

## 📝 编辑器组件

### RichTextEditor

富文本编辑器组件。

**Props**:
```typescript
interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  height?: number
  toolbar?: ToolbarConfig
  plugins?: string[]
}
```

### MarkdownEditor

Markdown 编辑器组件。

**使用示例**:
```tsx
import { RichTextEditor, MarkdownEditor } from '@/components/editors'

function ArticleEditor() {
  const [content, setContent] = useState('')
  const [editorType, setEditorType] = useState('rich')

  return (
    <div>
      {editorType === 'rich' ? (
        <RichTextEditor
          value={content}
          onChange={setContent}
          height={400}
        />
      ) : (
        <MarkdownEditor
          value={content}
          onChange={setContent}
          height={400}
        />
      )}
    </div>
  )
}
```

## 🌐 国际化组件

### LanguageSwitcher

语言切换组件。

**使用示例**:
```tsx
import LanguageSwitcher from '@/components/i18n/LanguageSwitcher'

function Header() {
  return (
    <div>
      <LanguageSwitcher />
    </div>
  )
}
```

## 📱 PWA 组件

### PWAInstallPrompt

PWA 安装提示组件。

**使用示例**:
```tsx
import PWAInstallPrompt from '@/components/pwa/PWAInstallPrompt'

function App() {
  return (
    <div>
      <PWAInstallPrompt />
      {/* 应用内容 */}
    </div>
  )
}
```

## 🔧 开发指南

### 创建新组件

1. **组件结构**:
```
components/
└── MyComponent/
    ├── index.tsx        # 组件主文件
    ├── types.ts         # 类型定义
    ├── styles.module.css # 样式文件
    └── __tests__/       # 测试文件
        └── MyComponent.test.tsx
```

2. **组件模板**:
```tsx
import React from 'react'
import { MyComponentProps } from './types'
import styles from './styles.module.css'

const MyComponent: React.FC<MyComponentProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div className={`${styles.container} ${className}`} {...props}>
      {children}
    </div>
  )
}

export default MyComponent
```

3. **类型定义**:
```typescript
export interface MyComponentProps {
  children?: React.ReactNode
  className?: string
  // 其他 props
}
```

### 组件测试

```tsx
import { render, screen } from '@testing-library/react'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent>Test Content</MyComponent>)
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })
})
```

### 样式规范

- 使用 CSS Modules 避免样式冲突
- 遵循 BEM 命名规范
- 支持主题变量
- 响应式设计优先

### 性能优化

- 使用 `React.memo` 避免不必要的重渲染
- 使用 `useMemo` 和 `useCallback` 优化计算
- 懒加载大型组件
- 合理使用 `Suspense`
