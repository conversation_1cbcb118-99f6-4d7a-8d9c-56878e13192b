# 🚀 Vercel 部署指南

## 📋 部署前准备

### 1. 安装 Vercel CLI
```bash
npm install -g vercel
```

### 2. 登录 Vercel
```bash
vercel login
```

### 3. 准备环境变量
复制 `.env.vercel` 文件并根据实际情况修改配置。

## 🔧 部署方式

### 方式一：使用 Vercel CLI（推荐）

#### 快速部署
```bash
# 预览部署
npm run deploy:vercel

# 生产部署
npm run deploy:vercel:prod
```

#### 手动部署
```bash
# 1. 链接项目
vercel link

# 2. 设置环境变量
vercel env add DATABASE_URL production
vercel env add JWT_SECRET production
# ... 其他环境变量

# 3. 部署到预览环境
vercel

# 4. 部署到生产环境
vercel --prod
```

### 方式二：通过 Git 集成（自动部署）

1. **连接 Git 仓库**
   - 访问 [Vercel Dashboard](https://vercel.com/dashboard)
   - 点击 "New Project"
   - 选择您的 Git 仓库
   - 导入项目

2. **配置构建设置**
   ```
   Framework Preset: Next.js
   Build Command: npm run build
   Output Directory: .next
   Install Command: npm install
   ```

3. **设置环境变量**
   在 Vercel Dashboard 的项目设置中添加环境变量：
   - `DATABASE_URL`
   - `JWT_SECRET`
   - `NEXTAUTH_SECRET`
   - `NEXTAUTH_URL`
   - 其他必要的环境变量

4. **部署**
   - 推送代码到 main 分支自动触发生产部署
   - 推送到其他分支触发预览部署

## 🗄️ 数据库配置

### 选项一：Vercel Postgres（推荐）
```bash
# 1. 在 Vercel Dashboard 中创建 Postgres 数据库
# 2. 复制连接字符串到环境变量
POSTGRES_URL="postgres://default:<EMAIL>:5432/verceldb"
```

### 选项二：PlanetScale
```bash
# 1. 创建 PlanetScale 数据库
# 2. 获取连接字符串
DATABASE_URL="mysql://xxx:<EMAIL>/xxx?sslaccept=strict"
```

### 选项三：Supabase
```bash
# 1. 创建 Supabase 项目
# 2. 获取连接字符串
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

## 🔄 缓存配置

### 选项一：Vercel KV（推荐）
```bash
# 在 Vercel Dashboard 中创建 KV 存储
KV_URL="redis://xxx"
KV_REST_API_URL="https://xxx"
KV_REST_API_TOKEN="xxx"
```

### 选项二：Upstash Redis
```bash
# 创建 Upstash Redis 实例
REDIS_URL="redis://xxx:<EMAIL>:6379"
```

## 📁 文件存储配置

### 使用 Vercel Blob（推荐）
```bash
# 在 Vercel Dashboard 中启用 Blob 存储
BLOB_READ_WRITE_TOKEN="vercel_blob_rw_xxx"
NEXT_PUBLIC_BLOB_STORE_URL="https://xxx.public.blob.vercel-storage.com"
```

### 使用 AWS S3
```bash
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-bucket-name"
```

## 🔐 环境变量管理

### 设置环境变量
```bash
# 添加环境变量
vercel env add VARIABLE_NAME production

# 列出环境变量
vercel env ls

# 拉取环境变量到本地
vercel env pull .env.local

# 删除环境变量
vercel env rm VARIABLE_NAME production
```

### 环境变量优先级
1. Vercel Dashboard 设置的环境变量
2. `.env.local` 文件
3. `.env.production` 文件
4. `.env` 文件

## 🌐 自定义域名

### 添加自定义域名
1. 在 Vercel Dashboard 中进入项目设置
2. 点击 "Domains" 标签
3. 添加您的域名
4. 配置 DNS 记录：
   ```
   Type: CNAME
   Name: www (或 @)
   Value: cname.vercel-dns.com
   ```

### 配置 SSL
Vercel 自动为所有域名提供免费的 SSL 证书。

## 📊 监控和分析

### Vercel Analytics
```bash
# 安装 Vercel Analytics
npm install @vercel/analytics

# 在 _app.tsx 中添加
import { Analytics } from '@vercel/analytics/react'

export default function App({ Component, pageProps }) {
  return (
    <>
      <Component {...pageProps} />
      <Analytics />
    </>
  )
}
```

### Vercel Speed Insights
```bash
# 安装 Speed Insights
npm install @vercel/speed-insights

# 在 _app.tsx 中添加
import { SpeedInsights } from '@vercel/speed-insights/next'

export default function App({ Component, pageProps }) {
  return (
    <>
      <Component {...pageProps} />
      <SpeedInsights />
    </>
  )
}
```

## 🚨 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 检查构建日志
   vercel logs
   
   # 本地测试构建
   npm run build
   ```

2. **环境变量未生效**
   ```bash
   # 检查环境变量
   vercel env ls
   
   # 重新部署
   vercel --prod
   ```

3. **数据库连接失败**
   - 检查数据库连接字符串
   - 确认数据库服务状态
   - 检查网络连接

4. **文件上传失败**
   - 检查 Blob 存储配置
   - 确认存储权限设置
   - 检查文件大小限制

### 调试技巧
```bash
# 查看部署日志
vercel logs

# 查看函数日志
vercel logs --follow

# 本地调试
vercel dev
```

## 📈 性能优化

### 1. 启用 Edge Runtime
```typescript
// app/api/example/route.ts
export const runtime = 'edge'
```

### 2. 使用 ISR（增量静态再生）
```typescript
// app/page.tsx
export const revalidate = 3600 // 1小时
```

### 3. 优化图片
```typescript
// next.config.js
module.exports = {
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
}
```

## 🔄 CI/CD 配置

### GitHub Actions 示例
```yaml
# .github/workflows/deploy.yml
name: Deploy to Vercel
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 📚 有用的链接

- [Vercel 文档](https://vercel.com/docs)
- [Next.js 部署指南](https://nextjs.org/docs/deployment)
- [Vercel CLI 文档](https://vercel.com/docs/cli)
- [环境变量配置](https://vercel.com/docs/concepts/projects/environment-variables)
- [自定义域名](https://vercel.com/docs/concepts/projects/domains)
