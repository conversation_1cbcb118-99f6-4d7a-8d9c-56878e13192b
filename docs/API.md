# API 文档

本文档详细介绍了 MP React Next.js 应用的所有 API 接口。

## 📋 目录

- [API 概览](#api-概览)
- [认证接口](#认证接口)
- [用户管理](#用户管理)
- [角色权限](#角色权限)
- [内容管理](#内容管理)
- [文件上传](#文件上传)
- [系统设置](#系统设置)
- [错误处理](#错误处理)

## 🌐 API 概览

### 基础信息

- **Base URL**: `https://your-domain.com/api`
- **版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式

```typescript
interface ApiResponse<T = any> {
  success: boolean
  data: T | null
  message: string
  code: number
  timestamp?: string
}
```

### 分页响应格式

```typescript
interface PaginatedResponse<T = any> {
  success: boolean
  data: {
    list: T[]
    pagination: {
      current: number
      pageSize: number
      total: number
      showSizeChanger: boolean
      showQuickJumper: boolean
    }
  }
  message: string
  code: number
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 验证失败 |
| 500 | 服务器错误 |

## 🔐 认证接口

### 用户登录

**POST** `/auth/login`

登录获取访问令牌。

**请求参数**:
```typescript
{
  username: string    // 用户名
  password: string    // 密码
  remember?: boolean  // 记住登录状态
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "1",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "avatar": "/avatar.jpg",
      "permissions": ["read", "write", "delete"]
    }
  },
  "message": "登录成功",
  "code": 200
}
```

### 获取用户信息

**GET** `/auth/userinfo`

获取当前登录用户信息。

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "1",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin",
    "avatar": "/avatar.jpg",
    "permissions": ["read", "write", "delete"],
    "lastLoginAt": "2023-12-01T10:00:00Z"
  },
  "message": "获取成功",
  "code": 200
}
```

### 获取菜单权限

**GET** `/auth/menus`

获取当前用户的菜单权限。

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "dashboard",
      "name": "仪表盘",
      "path": "/dashboard",
      "icon": "DashboardOutlined",
      "children": []
    },
    {
      "id": "system",
      "name": "系统管理",
      "path": "/system",
      "icon": "SettingOutlined",
      "children": [
        {
          "id": "users",
          "name": "用户管理",
          "path": "/system/users",
          "icon": "UserOutlined"
        }
      ]
    }
  ],
  "message": "获取成功",
  "code": 200
}
```

## 👥 用户管理

### 获取用户列表

**GET** `/users`

获取用户列表，支持分页和搜索。

**查询参数**:
```typescript
{
  page?: number        // 页码，默认 1
  pageSize?: number    // 每页数量，默认 10
  search?: string      // 搜索关键词
  role?: string        // 角色筛选
  status?: string      // 状态筛选
  sortBy?: string      // 排序字段
  sortOrder?: 'asc' | 'desc'  // 排序方向
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": "1",
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "status": "active",
        "avatar": "/avatar.jpg",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-12-01T10:00:00Z"
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 10,
      "total": 1,
      "showSizeChanger": true,
      "showQuickJumper": true
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 创建用户

**POST** `/users`

创建新用户。

**请求参数**:
```typescript
{
  username: string     // 用户名
  email: string        // 邮箱
  password: string     // 密码
  role: string         // 角色
  avatar?: string      // 头像URL
  status?: string      // 状态，默认 'active'
}
```

### 更新用户

**PUT** `/users/{id}`

更新用户信息。

**请求参数**:
```typescript
{
  username?: string    // 用户名
  email?: string       // 邮箱
  role?: string        // 角色
  avatar?: string      // 头像URL
  status?: string      // 状态
}
```

### 删除用户

**DELETE** `/users/{id}`

删除指定用户。

## 🛡️ 角色权限

### 获取角色列表

**GET** `/roles`

获取角色列表。

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "admin",
      "description": "系统管理员",
      "permissions": ["read", "write", "delete"],
      "userCount": 1,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "message": "获取成功",
  "code": 200
}
```

### 创建角色

**POST** `/roles`

创建新角色。

**请求参数**:
```typescript
{
  name: string           // 角色名称
  description: string    // 角色描述
  permissions: string[]  // 权限列表
}
```

### 获取权限列表

**GET** `/permissions`

获取所有可用权限。

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "user:read",
      "name": "查看用户",
      "category": "用户管理"
    },
    {
      "id": "user:write",
      "name": "编辑用户",
      "category": "用户管理"
    }
  ],
  "message": "获取成功",
  "code": 200
}
```

## 📝 内容管理

### 获取文章列表

**GET** `/articles`

获取文章列表。

**查询参数**:
```typescript
{
  page?: number
  pageSize?: number
  search?: string
  categoryId?: string
  status?: 'draft' | 'published' | 'archived'
  author?: string
}
```

### 创建文章

**POST** `/articles`

创建新文章。

**请求参数**:
```typescript
{
  title: string        // 标题
  content: string      // 内容
  categoryId: string   // 分类ID
  tags?: string[]      // 标签
  status?: string      // 状态
  publishAt?: string   // 发布时间
}
```

### 获取分类列表

**GET** `/categories`

获取文章分类列表。

## 📁 文件上传

### 上传文件

**POST** `/upload`

上传文件到服务器。

**请求格式**: `multipart/form-data`

**请求参数**:
```typescript
{
  file: File          // 文件对象
  type?: string       // 文件类型
  category?: string   // 文件分类
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "file_123",
    "filename": "image.jpg",
    "originalName": "my-image.jpg",
    "url": "/uploads/2023/12/image.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg",
    "uploadedAt": "2023-12-01T10:00:00Z"
  },
  "message": "上传成功",
  "code": 200
}
```

### 删除文件

**DELETE** `/upload/{id}`

删除指定文件。

## ⚙️ 系统设置

### 获取系统设置

**GET** `/settings`

获取系统配置。

**响应示例**:
```json
{
  "success": true,
  "data": {
    "siteName": "MP React Admin",
    "siteDescription": "现代化管理后台系统",
    "logo": "/logo.png",
    "theme": {
      "primaryColor": "#1890ff",
      "darkMode": false
    },
    "features": {
      "enableRegistration": false,
      "enableEmailVerification": true,
      "enableTwoFactor": false
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 更新系统设置

**PUT** `/settings`

更新系统配置。

## 🚨 错误处理

### 错误响应格式

```typescript
interface ErrorResponse {
  success: false
  data: null
  message: string
  code: number
  errors?: {
    field: string
    message: string
  }[]
}
```

### 常见错误示例

**验证错误 (422)**:
```json
{
  "success": false,
  "data": null,
  "message": "验证失败",
  "code": 422,
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    },
    {
      "field": "password",
      "message": "密码长度至少6位"
    }
  ]
}
```

**认证错误 (401)**:
```json
{
  "success": false,
  "data": null,
  "message": "未认证，请先登录",
  "code": 401
}
```

**权限错误 (403)**:
```json
{
  "success": false,
  "data": null,
  "message": "权限不足",
  "code": 403
}
```

## 📊 健康检查

### 系统健康检查

**GET** `/health`

检查系统健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "uptime": 86400,
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "database": {
      "status": "pass",
      "message": "Database connection successful",
      "responseTime": 15
    },
    "redis": {
      "status": "pass",
      "message": "Redis connection successful",
      "responseTime": 5
    }
  }
}
```

## 🔧 开发工具

### API 测试

推荐使用以下工具测试 API：

1. **Postman**: 图形化 API 测试工具
2. **curl**: 命令行工具
3. **HTTPie**: 现代化命令行工具

### 示例请求

```bash
# 登录
curl -X POST https://your-domain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 获取用户列表
curl -X GET https://your-domain.com/api/users \
  -H "Authorization: Bearer <token>"
```
