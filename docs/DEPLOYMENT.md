# 部署指南

本文档详细介绍了如何将 MP React Next.js 应用部署到不同的环境中。

## 📋 目录

- [部署概览](#部署概览)
- [环境准备](#环境准备)
- [Docker 部署](#docker-部署)
- [Kubernetes 部署](#kubernetes-部署)
- [CI/CD 流程](#cicd-流程)
- [监控和维护](#监控和维护)

## 🏗️ 部署概览

### 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│                 │    │                 │    │                 │
│ Docker Compose  │───▶│   Kubernetes    │───▶│   Kubernetes    │
│ Local Testing   │    │ Pre-production  │    │ Live Environment│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 支持的部署方式

1. **Docker Compose** - 开发和测试环境
2. **Kubernetes** - 生产和预生产环境
3. **Vercel/Netlify** - 静态部署（可选）
4. **传统服务器** - PM2 + Nginx

## 🛠️ 环境准备

### 系统要求

- **Node.js**: 18.x 或更高版本
- **Docker**: 20.x 或更高版本
- **Kubernetes**: 1.24 或更高版本
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 10GB 可用空间

### 环境变量配置

1. 复制环境变量模板：
```bash
cp .env.example .env.local
```

2. 根据环境修改配置：
```bash
# 开发环境
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 生产环境
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 🐳 Docker 部署

### 单容器部署

```bash
# 构建镜像
docker build -t mp-react-nextjs .

# 运行容器
docker run -d \
  --name mp-react-app \
  -p 3000:3000 \
  --env-file .env.local \
  mp-react-nextjs
```

### Docker Compose 部署

1. **开发环境**：
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

2. **生产环境**：
```bash
# 启动生产服务
docker-compose up -d

# 扩展应用实例
docker-compose up -d --scale app=3

# 更新服务
docker-compose pull && docker-compose up -d
```

### Docker 镜像优化

```dockerfile
# 多阶段构建示例
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
EXPOSE 3000
CMD ["node", "server.js"]
```

## ☸️ Kubernetes 部署

### 快速部署

```bash
# 应用所有配置
kubectl apply -f k8s/

# 检查部署状态
kubectl get pods -l app=mp-react-nextjs

# 查看服务
kubectl get services
```

### 分步部署

1. **创建命名空间**：
```bash
kubectl create namespace mp-react-nextjs
```

2. **部署配置和密钥**：
```bash
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
```

3. **部署应用**：
```bash
kubectl apply -f k8s/deployment.yaml
```

4. **配置网络**：
```bash
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml
```

### 滚动更新

```bash
# 更新镜像
kubectl set image deployment/mp-react-nextjs-app \
  app=ghcr.io/your-org/mp-react-nextjs:v1.1.0

# 查看更新状态
kubectl rollout status deployment/mp-react-nextjs-app

# 回滚到上一版本
kubectl rollout undo deployment/mp-react-nextjs-app
```

### 扩缩容

```bash
# 手动扩容
kubectl scale deployment mp-react-nextjs-app --replicas=5

# 自动扩容（HPA）
kubectl apply -f k8s/hpa.yaml
```

## 🔄 CI/CD 流程

### GitHub Actions 工作流

我们的 CI/CD 流程包含以下阶段：

1. **代码质量检查**
   - ESLint 代码检查
   - TypeScript 类型检查
   - 单元测试和覆盖率
   - E2E 测试

2. **安全扫描**
   - 依赖漏洞扫描
   - 代码安全分析
   - Docker 镜像扫描

3. **构建和推送**
   - Docker 镜像构建
   - 多架构支持
   - 镜像推送到注册表

4. **部署**
   - 开发环境自动部署
   - 生产环境手动批准
   - 健康检查和冒烟测试

### 手动部署

使用部署脚本：

```bash
# 部署到开发环境
./scripts/deploy.sh development v1.0.0

# 部署到生产环境
./scripts/deploy.sh production v1.0.0
```

### 部署检查清单

- [ ] 环境变量已正确配置
- [ ] 数据库迁移已完成
- [ ] SSL 证书已配置
- [ ] 监控和日志已设置
- [ ] 备份策略已实施
- [ ] 回滚计划已准备

## 📊 监控和维护

### 健康检查

应用提供了完整的健康检查端点：

```bash
# 详细健康检查
curl https://your-domain.com/api/health

# 简单健康检查（负载均衡器使用）
curl -I https://your-domain.com/api/health
```

健康检查包含：
- 数据库连接状态
- Redis 连接状态
- 内存使用情况
- 磁盘空间检查
- 外部服务可用性

### 日志管理

```bash
# Docker 日志
docker logs mp-react-app -f

# Kubernetes 日志
kubectl logs -f deployment/mp-react-nextjs-app

# 日志聚合（使用 ELK Stack）
# 配置 Filebeat -> Elasticsearch -> Kibana
```

### 性能监控

1. **应用性能监控 (APM)**：
   - Sentry 错误监控
   - New Relic 性能监控
   - DataDog 基础设施监控

2. **业务指标监控**：
   - 用户活跃度
   - 页面加载时间
   - API 响应时间
   - 错误率统计

### 备份策略

1. **数据库备份**：
```bash
# PostgreSQL 备份
pg_dump -h localhost -U username dbname > backup.sql

# 自动备份脚本
0 2 * * * /scripts/backup-db.sh
```

2. **文件备份**：
```bash
# 上传文件备份
rsync -av /app/uploads/ /backup/uploads/

# 使用对象存储
aws s3 sync /app/uploads/ s3://your-backup-bucket/uploads/
```

### 故障排除

#### 常见问题

1. **应用无法启动**：
```bash
# 检查日志
kubectl logs deployment/mp-react-nextjs-app

# 检查配置
kubectl describe pod <pod-name>
```

2. **数据库连接失败**：
```bash
# 检查数据库状态
kubectl get pods -l app=postgres

# 测试连接
kubectl exec -it <app-pod> -- npm run db:test
```

3. **内存不足**：
```bash
# 检查资源使用
kubectl top pods

# 调整资源限制
kubectl patch deployment mp-react-nextjs-app -p '{"spec":{"template":{"spec":{"containers":[{"name":"app","resources":{"limits":{"memory":"1Gi"}}}]}}}}'
```

#### 性能优化

1. **应用层优化**：
   - 启用 Gzip 压缩
   - 配置 CDN
   - 优化图片加载
   - 实施缓存策略

2. **基础设施优化**：
   - 使用 SSD 存储
   - 配置负载均衡
   - 启用自动扩容
   - 优化网络配置

### 安全最佳实践

1. **容器安全**：
   - 使用非 root 用户
   - 最小化镜像体积
   - 定期更新基础镜像
   - 扫描安全漏洞

2. **网络安全**：
   - 配置防火墙规则
   - 使用 HTTPS
   - 实施 WAF
   - 限制 API 访问

3. **数据安全**：
   - 加密敏感数据
   - 定期备份
   - 访问控制
   - 审计日志

## 🚀 生产环境清单

部署到生产环境前，请确保：

- [ ] **安全配置**
  - [ ] HTTPS 已启用
  - [ ] 安全头已配置
  - [ ] 防火墙规则已设置
  - [ ] 访问控制已实施

- [ ] **性能优化**
  - [ ] CDN 已配置
  - [ ] 缓存策略已实施
  - [ ] 数据库已优化
  - [ ] 静态资源已压缩

- [ ] **监控和日志**
  - [ ] 应用监控已设置
  - [ ] 错误追踪已配置
  - [ ] 日志聚合已实施
  - [ ] 告警规则已配置

- [ ] **备份和恢复**
  - [ ] 数据备份策略已实施
  - [ ] 恢复流程已测试
  - [ ] 灾难恢复计划已准备

- [ ] **文档和培训**
  - [ ] 运维文档已更新
  - [ ] 团队已培训
  - [ ] 应急联系人已确定
