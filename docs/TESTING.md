# 测试指南

本项目采用全面的测试策略，包括单元测试、集成测试和端到端测试。

## 📋 目录

- [测试架构](#测试架构)
- [测试工具](#测试工具)
- [运行测试](#运行测试)
- [编写测试](#编写测试)
- [测试覆盖率](#测试覆盖率)
- [最佳实践](#最佳实践)

## 🏗️ 测试架构

### 测试层级

```
测试金字塔
    /\
   /  \     E2E Tests (端到端测试)
  /____\    - 用户流程测试
 /      \   - 跨浏览器测试
/________\  Integration Tests (集成测试)
\        /  - API集成测试
 \______/   - 组件集成测试
  \    /    Unit Tests (单元测试)
   \__/     - 函数测试
            - 组件测试
            - Hook测试
```

### 测试文件结构

```
mp-react-nextjs/
├── src/
│   ├── components/
│   │   └── __tests__/          # 组件单元测试
│   ├── hooks/
│   │   └── __tests__/          # Hook测试
│   ├── utils/
│   │   └── __tests__/          # 工具函数测试
│   └── test-utils.tsx          # 测试工具
├── __tests__/
│   └── integration/            # 集成测试
├── e2e/                        # 端到端测试
│   ├── auth.spec.ts
│   ├── global-setup.ts
│   └── global-teardown.ts
├── jest.config.js              # Jest配置
├── jest.setup.js               # Jest设置
└── playwright.config.ts        # Playwright配置
```

## 🛠️ 测试工具

### 单元测试和集成测试
- **Jest**: 测试运行器和断言库
- **React Testing Library**: React组件测试
- **@testing-library/user-event**: 用户交互模拟
- **@testing-library/jest-dom**: DOM断言扩展

### 端到端测试
- **Playwright**: 跨浏览器E2E测试
- **支持浏览器**: Chrome, Firefox, Safari, Edge

### 测试工具
- **test-utils.tsx**: 自定义渲染函数和Mock工具
- **Mock数据生成器**: 生成测试数据
- **测试报告生成器**: 综合测试报告

## 🚀 运行测试

### 基本命令

```bash
# 运行所有单元测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage

# 运行CI模式测试
npm run test:ci

# 运行E2E测试
npm run test:e2e

# 可视化运行E2E测试
npm run test:e2e:ui

# 运行所有测试
npm run test:all
```

### 高级命令

```bash
# 运行特定测试文件
npm test -- DataTable.test.tsx

# 运行匹配模式的测试
npm test -- --testNamePattern="should handle"

# 更新快照
npm test -- --updateSnapshot

# 调试模式运行测试
npm test -- --detectOpenHandles --forceExit
```

## ✍️ 编写测试

### 组件测试示例

```typescript
import { render, screen, fireEvent } from '@/utils/test-utils'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent title="Test" />)
    
    expect(screen.getByText('Test')).toBeInTheDocument()
  })

  it('should handle click events', async () => {
    const handleClick = jest.fn()
    render(<MyComponent onClick={handleClick} />)
    
    await fireEvent.click(screen.getByRole('button'))
    
    expect(handleClick).toHaveBeenCalled()
  })
})
```

### Hook测试示例

```typescript
import { renderHook, act } from '@testing-library/react'
import { useMyHook } from '../useMyHook'

describe('useMyHook', () => {
  it('should initialize with default value', () => {
    const { result } = renderHook(() => useMyHook())
    
    expect(result.current.value).toBe(0)
  })

  it('should update value', () => {
    const { result } = renderHook(() => useMyHook())
    
    act(() => {
      result.current.setValue(10)
    })
    
    expect(result.current.value).toBe(10)
  })
})
```

### E2E测试示例

```typescript
import { test, expect } from '@playwright/test'

test('user login flow', async ({ page }) => {
  await page.goto('/login')
  
  await page.fill('[data-testid="username"]', 'testuser')
  await page.fill('[data-testid="password"]', 'password')
  await page.click('[data-testid="login-button"]')
  
  await expect(page).toHaveURL('/dashboard')
})
```

## 📊 测试覆盖率

### 覆盖率目标
- **语句覆盖率**: ≥ 80%
- **分支覆盖率**: ≥ 80%
- **函数覆盖率**: ≥ 80%
- **行覆盖率**: ≥ 80%

### 查看覆盖率报告

```bash
# 生成覆盖率报告
npm run test:coverage

# 查看HTML报告
open coverage/lcov-report/index.html
```

### 覆盖率配置

```javascript
// jest.config.js
coverageThreshold: {
  global: {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80,
  },
}
```

## 🎯 最佳实践

### 测试命名
```typescript
// ✅ 好的测试名称
describe('UserForm', () => {
  it('should validate required fields when submitting empty form', () => {})
  it('should call onSubmit with form data when validation passes', () => {})
})

// ❌ 不好的测试名称
describe('UserForm', () => {
  it('test validation', () => {})
  it('test submit', () => {})
})
```

### 测试结构 (AAA模式)
```typescript
it('should update user profile', async () => {
  // Arrange (准备)
  const user = mockData.user()
  const onUpdate = jest.fn()
  render(<UserProfile user={user} onUpdate={onUpdate} />)
  
  // Act (执行)
  await userEvent.type(screen.getByLabelText('Name'), 'New Name')
  await userEvent.click(screen.getByRole('button', { name: 'Save' }))
  
  // Assert (断言)
  expect(onUpdate).toHaveBeenCalledWith({
    ...user,
    name: 'New Name',
  })
})
```

### Mock策略
```typescript
// ✅ 在测试文件顶部Mock外部依赖
jest.mock('@/lib/request', () => ({
  http: {
    get: jest.fn(),
    post: jest.fn(),
  },
}))

// ✅ 使用类型安全的Mock
const mockHttp = http as jest.Mocked<typeof http>
mockHttp.get.mockResolvedValue(mockResponse)
```

### 测试数据管理
```typescript
// ✅ 使用工厂函数生成测试数据
const createUser = (overrides = {}) => ({
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  ...overrides,
})

// ✅ 使用测试工具中的Mock数据
import { mockData } from '@/utils/test-utils'
const users = mockData.users(5)
```

### 异步测试
```typescript
// ✅ 使用waitFor处理异步操作
await waitFor(() => {
  expect(screen.getByText('Success')).toBeInTheDocument()
})

// ✅ 使用findBy查询异步元素
const successMessage = await screen.findByText('Success')
expect(successMessage).toBeInTheDocument()
```

## 🔧 故障排除

### 常见问题

1. **模块解析错误**
   ```bash
   # 检查Jest配置中的moduleNameMapper
   moduleNameMapper: {
     '^@/(.*)$': '<rootDir>/src/$1',
   }
   ```

2. **异步测试超时**
   ```typescript
   // 增加超时时间
   jest.setTimeout(10000)
   
   // 或在特定测试中
   it('should handle async operation', async () => {
     // test code
   }, 10000)
   ```

3. **Mock不生效**
   ```typescript
   // 确保Mock在导入之前
   jest.mock('@/lib/request')
   import { http } from '@/lib/request'
   ```

### 调试技巧

```typescript
// 调试渲染结果
import { screen } from '@testing-library/react'
screen.debug() // 打印当前DOM

// 查看查询结果
console.log(screen.getByRole('button', { name: /submit/i }))

// 使用logRoles查看可用角色
import { logRoles } from '@testing-library/react'
logRoles(container)
```

## 📈 持续改进

### 测试指标监控
- 定期检查测试覆盖率
- 监控测试执行时间
- 跟踪测试失败率

### 测试维护
- 定期更新测试依赖
- 重构重复的测试代码
- 删除过时的测试

### 团队协作
- 代码审查包含测试
- 共享测试最佳实践
- 定期进行测试培训
