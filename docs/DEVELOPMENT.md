# 开发指南

本文档为开发者提供详细的开发指南和最佳实践。

## 📋 目录

- [开发环境](#开发环境)
- [项目结构](#项目结构)
- [开发规范](#开发规范)
- [状态管理](#状态管理)
- [路由管理](#路由管理)
- [样式指南](#样式指南)
- [性能优化](#性能优化)
- [调试技巧](#调试技巧)

## 🛠️ 开发环境

### 系统要求

- **Node.js**: 18.x 或更高版本
- **npm**: 8.x 或更高版本
- **Git**: 2.x 或更高版本
- **VSCode**: 推荐使用的编辑器

### 推荐的 VSCode 扩展

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### 环境配置

1. **克隆项目**:
```bash
git clone <repository-url>
cd mp-react-nextjs
```

2. **安装依赖**:
```bash
npm install
```

3. **配置环境变量**:
```bash
cp .env.example .env.local
# 编辑 .env.local 文件
```

4. **启动开发服务器**:
```bash
npm run dev
```

## 📁 项目结构

```
mp-react-nextjs/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # 认证相关页面
│   │   ├── dashboard/         # 仪表盘页面
│   │   ├── system/            # 系统管理页面
│   │   ├── api/               # API 路由
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # 可复用组件
│   │   ├── common/            # 通用组件
│   │   ├── layout/            # 布局组件
│   │   ├── form/              # 表单组件
│   │   └── ...
│   ├── hooks/                 # 自定义 Hooks
│   ├── store/                 # 状态管理
│   ├── utils/                 # 工具函数
│   ├── lib/                   # 第三方库配置
│   ├── types/                 # 类型定义
│   └── constants/             # 常量定义
├── public/                    # 静态资源
├── docs/                      # 项目文档
├── scripts/                   # 构建脚本
├── k8s/                       # Kubernetes 配置
├── __tests__/                 # 测试文件
├── e2e/                       # E2E 测试
└── ...配置文件
```

### 文件命名规范

- **组件文件**: PascalCase (如 `UserTable.tsx`)
- **页面文件**: kebab-case (如 `user-management.tsx`)
- **工具文件**: camelCase (如 `formatDate.ts`)
- **常量文件**: UPPER_SNAKE_CASE (如 `API_ENDPOINTS.ts`)
- **类型文件**: PascalCase (如 `UserTypes.ts`)

## 📝 开发规范

### TypeScript 规范

1. **严格类型检查**:
```typescript
// 启用严格模式
"strict": true,
"noImplicitAny": true,
"strictNullChecks": true
```

2. **接口定义**:
```typescript
// 使用 interface 定义对象类型
interface User {
  id: string
  username: string
  email: string
  role: UserRole
  createdAt: Date
}

// 使用 type 定义联合类型
type UserRole = 'admin' | 'editor' | 'viewer'
type Status = 'loading' | 'success' | 'error'
```

3. **泛型使用**:
```typescript
// API 响应类型
interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
}

// 组件 Props
interface TableProps<T = any> {
  dataSource: T[]
  columns: ColumnConfig<T>[]
  onRowClick?: (record: T) => void
}
```

### React 组件规范

1. **函数组件优先**:
```typescript
// ✅ 推荐
const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  return (
    <div>
      <h3>{user.username}</h3>
      <button onClick={() => onEdit(user)}>编辑</button>
    </div>
  )
}

// ❌ 避免使用类组件（除非必要）
class UserCard extends React.Component<UserCardProps> {
  // ...
}
```

2. **Hooks 使用规范**:
```typescript
// ✅ 正确的 Hooks 使用
const UserList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  
  const fetchUsers = useCallback(async () => {
    setLoading(true)
    try {
      const response = await api.getUsers()
      setUsers(response.data)
    } catch (error) {
      console.error('获取用户失败:', error)
    } finally {
      setLoading(false)
    }
  }, [])
  
  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])
  
  return (
    <div>
      {loading ? <Spin /> : <UserTable users={users} />}
    </div>
  )
}
```

3. **性能优化**:
```typescript
// 使用 memo 避免不必要的重渲染
const UserCard = React.memo<UserCardProps>(({ user, onEdit }) => {
  return (
    <div>
      <h3>{user.username}</h3>
      <button onClick={() => onEdit(user)}>编辑</button>
    </div>
  )
})

// 使用 useMemo 缓存计算结果
const UserStats: React.FC<{ users: User[] }> = ({ users }) => {
  const stats = useMemo(() => {
    return {
      total: users.length,
      active: users.filter(u => u.status === 'active').length,
      inactive: users.filter(u => u.status === 'inactive').length,
    }
  }, [users])
  
  return <div>{/* 渲染统计信息 */}</div>
}
```

### 代码风格

1. **ESLint 配置**:
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "prefer-const": "error",
    "no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

2. **Prettier 配置**:
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 🗂️ 状态管理

### Zustand Store 模式

1. **Store 定义**:
```typescript
// store/useUserStore.ts
interface UserState {
  userInfo: User | null
  isAuthenticated: boolean
  permissions: string[]
}

interface UserActions {
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  updateUserInfo: (userInfo: Partial<User>) => void
  checkPermission: (permission: string) => boolean
}

export const useUserStore = create<UserState & UserActions>((set, get) => ({
  // 状态
  userInfo: null,
  isAuthenticated: false,
  permissions: [],
  
  // 动作
  login: async (credentials) => {
    try {
      const response = await api.login(credentials)
      set({
        userInfo: response.data.user,
        isAuthenticated: true,
        permissions: response.data.user.permissions,
      })
    } catch (error) {
      throw error
    }
  },
  
  logout: () => {
    set({
      userInfo: null,
      isAuthenticated: false,
      permissions: [],
    })
  },
  
  updateUserInfo: (userInfo) => {
    set(state => ({
      userInfo: { ...state.userInfo, ...userInfo }
    }))
  },
  
  checkPermission: (permission) => {
    const { permissions } = get()
    return permissions.includes(permission)
  },
}))
```

2. **Store 使用**:
```typescript
// 在组件中使用
const UserProfile: React.FC = () => {
  const { userInfo, updateUserInfo } = useUserStore()
  
  const handleUpdate = (data: Partial<User>) => {
    updateUserInfo(data)
  }
  
  return (
    <div>
      <h2>{userInfo?.username}</h2>
      {/* 其他内容 */}
    </div>
  )
}

// 选择性订阅
const UserAvatar: React.FC = () => {
  const avatar = useUserStore(state => state.userInfo?.avatar)
  
  return <img src={avatar} alt="用户头像" />
}
```

## 🛣️ 路由管理

### App Router 使用

1. **页面结构**:
```
app/
├── (auth)/
│   ├── login/
│   │   └── page.tsx
│   └── layout.tsx
├── dashboard/
│   ├── page.tsx
│   └── loading.tsx
├── system/
│   ├── users/
│   │   ├── page.tsx
│   │   ├── [id]/
│   │   │   └── page.tsx
│   │   └── loading.tsx
│   └── layout.tsx
└── layout.tsx
```

2. **路由守卫**:
```typescript
// app/dashboard/layout.tsx
import { AuthGuard } from '@/components/auth/AuthGuard'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthGuard>
      <MainLayout>
        {children}
      </MainLayout>
    </AuthGuard>
  )
}
```

3. **动态路由**:
```typescript
// app/system/users/[id]/page.tsx
interface PageProps {
  params: { id: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

export default function UserDetailPage({ params }: PageProps) {
  const { id } = params
  
  return (
    <div>
      <h1>用户详情 - {id}</h1>
    </div>
  )
}
```

## 🎨 样式指南

### CSS Modules 使用

1. **组件样式**:
```css
/* components/UserCard/styles.module.css */
.container {
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-color);
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
}

.actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .actions {
    flex-direction: column;
  }
}
```

2. **样式使用**:
```typescript
import styles from './styles.module.css'

const UserCard: React.FC<UserCardProps> = ({ user }) => {
  return (
    <div className={styles.container}>
      <h3 className={styles.title}>{user.username}</h3>
      <div className={styles.actions}>
        <button>编辑</button>
        <button>删除</button>
      </div>
    </div>
  )
}
```

### 主题变量

```css
/* globals.css */
:root {
  /* 颜色变量 */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  
  /* 文本颜色 */
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --text-color-disabled: #bfbfbf;
  
  /* 背景颜色 */
  --bg-color: #ffffff;
  --bg-color-secondary: #fafafa;
  
  /* 边框颜色 */
  --border-color: #d9d9d9;
  --border-color-split: #f0f0f0;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

[data-theme='dark'] {
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --bg-color: #141414;
  --bg-color-secondary: #1f1f1f;
  --border-color: #434343;
}
```

## ⚡ 性能优化

### 代码分割

1. **动态导入**:
```typescript
// 懒加载组件
const UserManagement = lazy(() => import('@/components/UserManagement'))
const RoleManagement = lazy(() => import('@/components/RoleManagement'))

// 使用 Suspense
function SystemPage() {
  return (
    <Suspense fallback={<Spin size="large" />}>
      <Routes>
        <Route path="/users" element={<UserManagement />} />
        <Route path="/roles" element={<RoleManagement />} />
      </Routes>
    </Suspense>
  )
}
```

2. **路由级别分割**:
```typescript
// next.config.js
module.exports = {
  webpack: (config) => {
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    }
    return config
  },
}
```

### 图片优化

```typescript
import Image from 'next/image'

// 使用 Next.js Image 组件
<Image
  src="/user-avatar.jpg"
  alt="用户头像"
  width={64}
  height={64}
  priority={false}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

## 🐛 调试技巧

### 开发工具

1. **React Developer Tools**:
   - 组件树查看
   - Props 和 State 检查
   - 性能分析

2. **Redux DevTools** (适用于 Zustand):
```typescript
import { devtools } from 'zustand/middleware'

export const useUserStore = create<UserState & UserActions>()(
  devtools(
    (set, get) => ({
      // store 实现
    }),
    {
      name: 'user-store',
    }
  )
)
```

3. **网络调试**:
```typescript
// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    console.log('请求:', config)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    console.log('响应:', response)
    return response
  },
  (error) => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)
```

### 常见问题解决

1. **Hydration 错误**:
```typescript
// 使用 useEffect 避免 SSR/CSR 不一致
const [mounted, setMounted] = useState(false)

useEffect(() => {
  setMounted(true)
}, [])

if (!mounted) {
  return null
}
```

2. **内存泄漏**:
```typescript
// 清理副作用
useEffect(() => {
  const timer = setInterval(() => {
    // 定时任务
  }, 1000)
  
  return () => {
    clearInterval(timer)
  }
}, [])
```

## 📚 学习资源

### 官方文档
- [Next.js 文档](https://nextjs.org/docs)
- [React 文档](https://react.dev)
- [TypeScript 文档](https://www.typescriptlang.org/docs)
- [Ant Design 文档](https://ant.design/docs/react/introduce-cn)

### 推荐阅读
- [React 性能优化](https://react.dev/learn/render-and-commit)
- [Next.js 最佳实践](https://nextjs.org/docs/pages/building-your-application/deploying/production-checklist)
- [TypeScript 最佳实践](https://typescript-eslint.io/rules/)

### 社区资源
- [React 中文社区](https://react.docschina.org/)
- [Next.js 中文文档](https://www.nextjs.cn/)
- [Ant Design 中文社区](https://ant.design/index-cn)
